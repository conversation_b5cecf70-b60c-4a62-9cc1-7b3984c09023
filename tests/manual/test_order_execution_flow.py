#!/usr/bin/env python3
"""
订单执行流程测试
- 测试 CSP 和 Long Call 订单下单
- 验证 pretrade 校验在真实环境下的表现
- 测试订单状态跟踪和执行报告
"""

import asyncio
import sys
from pathlib import Path
from decimal import Decimal
from datetime import date, datetime, timezone

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.gateways.ibkr_client import IBKRClient, OptionContract, Right, OrderSide, OrderType
from src.execution.order_manager import OrderManager, Order, OrderStatus
from src.execution.ibit_order_meta import IBITOptionOrderMeta
from src.risk.pretrade_filters import PretradeFilters


async def test_csp_order_execution():
    """测试 CSP 订单执行流程"""
    
    print("🔍 测试 CSP 订单执行...")
    print("=" * 40)
    
    # 1. 创建 IBKR 客户端
    client = IBKRClient(use_simulation=True)  # 使用模拟模式进行安全测试
    client.connect()
    
    # 2. 创建订单管理器
    order_manager = OrderManager(config={
        "pretrade_filters": {
            "max_spread_bps": 500,
            "min_oi": 100,
            "min_volume": 50,
            "daily_csp_notional_cap": "50000"
        }
    })
    
    # 3. 构造 CSP 订单
    contract = OptionContract("IBIT", date(2025, 3, 21), Decimal("48"), Right.PUT)
    meta = IBITOptionOrderMeta(
        action="SELL_PUT_CSP",
        contract=contract,
        spread_bps=300,  # 3% 价差
        open_interest=500,
        volume=100,
        buying_power=Decimal("10000"),
        days_to_expiry=45
    )
    
    order = Order(
        order_id="test_csp_001",
        symbol=f"IBIT {contract.expiry.isoformat()} {contract.strike} P",
        side=OrderSide.SELL,
        order_type=OrderType.LIMIT,
        quantity=Decimal("1"),
        price=Decimal("2.50"),
        ibit_meta=meta
    )
    
    # 4. 执行订单
    print(f"📤 提交 CSP 订单: {order.symbol}")
    await order_manager._execute_order(order)
    
    # 5. 检查结果
    if order.status == OrderStatus.REJECTED:
        print(f"❌ 订单被拒绝: {order.last_error}")
        return False
    else:
        print(f"✅ 订单状态: {order.status}")
        return True


async def test_long_call_with_theta_guard():
    """测试 Long Call 与 theta 守护"""
    
    print("\n🔍 测试 Long Call 与 theta 守护...")
    print("=" * 40)
    
    order_manager = OrderManager(config={
        "pretrade_filters": {
            "max_spread_bps": 400,
            "min_oi": 200,
            "min_volume": 30
        }
    })
    
    # 测试 1: 正常 Long Call（应该通过）
    print("📤 测试正常 Long Call...")
    normal_meta = IBITOptionOrderMeta(
        action="LONG_CALL",
        spread_bps=300,
        open_interest=400,
        volume=80,
        days_to_expiry=30,  # 30天到期
        iv=0.6  # 正常 IV
    )
    
    normal_order = Order(
        order_id="test_call_normal",
        symbol=f"IBIT {date.today().isoformat()} 52 C",
        side=OrderSide.BUY,
        order_type=OrderType.LIMIT,
        quantity=Decimal("1"),
        price=Decimal("3.00"),
        ibit_meta=normal_meta
    )
    
    await order_manager._execute_order(normal_order)
    
    if normal_order.status == OrderStatus.REJECTED:
        print(f"❌ 正常订单被拒绝: {normal_order.last_error}")
    else:
        print(f"✅ 正常订单通过: {normal_order.status}")
    
    # 测试 2: 高风险 Long Call（应该被拒绝）
    print("📤 测试高风险 Long Call（theta 守护）...")
    risky_meta = IBITOptionOrderMeta(
        action="LONG_CALL",
        spread_bps=350,
        open_interest=300,
        volume=60,
        days_to_expiry=5,   # 接近到期
        iv=0.95  # 极高 IV
    )
    
    risky_order = Order(
        order_id="test_call_risky",
        symbol=f"IBIT {date.today().isoformat()} 53 C",
        side=OrderSide.BUY,
        order_type=OrderType.LIMIT,
        quantity=Decimal("1"),
        price=Decimal("2.80"),
        ibit_meta=risky_meta
    )
    
    await order_manager._execute_order(risky_order)
    
    if risky_order.status == OrderStatus.REJECTED:
        print(f"✅ 高风险订单被正确拒绝: {risky_order.last_error}")
        return True
    else:
        print(f"❌ 高风险订单未被拒绝: {risky_order.status}")
        return False


async def test_liquidity_filtering():
    """测试流动性过滤"""
    
    print("\n🔍 测试流动性过滤...")
    print("=" * 40)
    
    order_manager = OrderManager(config={
        "pretrade_filters": {
            "max_spread_bps": 200,  # 严格的价差要求
            "min_oi": 1000,         # 严格的 OI 要求
            "min_volume": 500       # 严格的成交量要求
        }
    })
    
    # 构造流动性不足的订单
    poor_liquidity_meta = IBITOptionOrderMeta(
        action="LONG_CALL",
        spread_bps=800,  # 价差过大
        open_interest=50,  # OI 不足
        volume=10,  # 成交量不足
        days_to_expiry=30,
        iv=0.5
    )
    
    order = Order(
        order_id="test_liquidity_001",
        symbol=f"IBIT {date.today().isoformat()} 51 C",
        side=OrderSide.BUY,
        order_type=OrderType.LIMIT,
        quantity=Decimal("1"),
        price=Decimal("2.20"),
        ibit_meta=poor_liquidity_meta
    )
    
    print("📤 提交流动性不足的订单...")
    await order_manager._execute_order(order)
    
    if order.status == OrderStatus.REJECTED:
        print(f"✅ 流动性不足订单被正确拒绝: {order.last_error}")
        return True
    else:
        print(f"❌ 流动性不足订单未被拒绝: {order.status}")
        return False


async def main():
    """主测试函数"""
    
    print("🚀 IBKR 订单执行与风控测试")
    print("=" * 50)
    print(f"测试时间: {datetime.now(timezone.utc)}")
    
    # 执行测试
    tests = [
        ("CSP 订单执行", test_csp_order_execution),
        ("Long Call theta 守护", test_long_call_with_theta_guard),
        ("流动性过滤", test_liquidity_filtering),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
            
            if result:
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
                
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
            results[test_name] = False
    
    # 汇总结果
    print("=" * 50)
    print("📊 订单执行测试结果:")
    
    passed = sum(1 for r in results.values() if r)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 订单执行与风控测试全部通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查风控配置。")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
