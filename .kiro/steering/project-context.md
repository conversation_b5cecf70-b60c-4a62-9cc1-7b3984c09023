---
inclusion: always
---

# BTC期权网格交易项目上下文

## 项目概述

这是一个BTC智能期权网格策略交易系统，使用期权工具替代传统现货网格交易，通过创新的期权策略实现3-5倍资金效率提升。

## 核心文档结构

- `requirements.md` - 详细需求规格说明(包含流动性、回测、监控等扩展需求)
- `design.md` - 完整系统架构设计文档(包含数据模型、异常处理、接口定义、核心组件实现、配置管理、测试框架等所有设计细节)
- `BTC智能期权网格交易框架架构设计.md` - 系统架构设计概览和核心组件设计
- `tasks.md` - 具体实施任务清单(39个任务)
- `implementation-guide.md` - 完整实施指南和路线图
- `智能BTC期权网格策略框架.md` - 策略框架概述和核心理念

## 核心创新点

1. **期权网格策略**: 通过Sell Put/Call替代现货网格，保证金模式下实现多倍资金效率
2. **跨交易所因果分析**: 分析Binance vs Deribit的资金流向差异，识别结构性机会
3. **微观结构精准出场**: 基于VWAP-LWAP价差、订单簿深度等信号实现精准止盈
4. **期权生命周期管理**: 智能处理期权行权后的策略转换

## 技术栈

- **后端**: Python 3.9+ + FastAPI + AsyncIO
- **数据源**: Binance WebSocket + Deribit WebSocket
- **存储**: Redis Cluster + PostgreSQL + TimescaleDB
- **计算**: Celery分布式任务队列
- **通知**: Telegram Bot + 邮件告警
- **前端**: React + TypeScript + Ant Design
- **部署**: Docker + Kubernetes

## 系统架构

采用6层分层架构：

1. **API Gateway Layer**: 交易所连接和数据同步
2. **Data Layer**: 数据处理、因果分析、微观结构信号
3. **Strategy Layer**: 期权网格策略、分支策略、策略协调
4. **Risk Management Layer**: 风险控制、保证金管理、到期管理
5. **Execution Layer**: 订单执行、仓位管理、行权处理
6. **Infrastructure Layer**: 事件总线、配置管理、日志系统

## 开发规范

- 采用6层分层架构，严格分离职责
- 使用异步编程模式(asyncio)
- 实现测试驱动开发(TDD)
- 遵循PEP8代码规范
- 使用类型注解(typing)
- 实现依赖注入模式
- 支持配置热重载

## 性能要求

- 内存使用 < 3GB
- CPU使用率 < 40%
- 决策延迟 < 30秒
- 订单执行延迟 < 5秒
- 系统可用性 > 99.5%
- 数据同步延迟容忍 ±200ms

## 风险控制

- 总保证金占用率 < 70%
- Delta敞口控制在 ±0.5以内
- 单日最大亏损 < 5%
- 期权价差 > 5%时暂停交易
- 流动性不足时自动减仓

## 开发阶段

当前处于设计完成阶段，准备开始第一阶段基础设施开发：

1. **阶段1: 基础设施(第1-2周)**
   - 重点：建立系统基础框架和开发环境
   - 关键任务：Celery任务队列、事件总线、配置管理、日志系统
   - 验收标准：基础组件可正常工作，支持分布式计算

2. **阶段2: 数据层(第3-4周)**  
   - 重点：实现数据接入和分析能力
   - 关键任务：交易所连接器、数据同步、因果分析引擎
   - 验收标准：数据同步支持±200ms延迟，因果信号识别准确

3. **阶段3: 策略层(第5-7周)**
   - 重点：实现核心交易策略逻辑
   - 关键任务：期权网格策略、微观结构信号、策略协调
   - 验收标准：策略可正确执行交易决策，风险控制有效

4. **阶段4: 风险和执行层(第8-10周)**
   - 重点：完善风险控制和交易执行
   - 关键任务：风险引擎、订单管理、仓位跟踪、行权处理
   - 验收标准：风险监控实时有效，交易执行稳定可靠

5. **阶段5: 集成测试和部署(第11-12周)**
   - 重点：系统集成和生产部署
   - 关键任务：集成测试、监控面板、容器化部署、安全加固
   - 验收标准：系统整体验证通过，生产环境稳定运行

## 开发最佳实践

- **代码质量**：单元测试覆盖率>80%，代码审查必须通过
- **性能要求**：决策延迟<30秒，订单执行<5秒，系统可用性>99.5%
- **安全规范**：API密钥加密存储，所有操作记录审计日志
- **监控告警**：关键指标实时监控，异常情况立即告警
- **文档维护**：代码注释完整，API文档及时更新
