[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "btc-option-grid-bot"
version = "0.1.0"
description = "BTC智能期权网格策略交易系统"
authors = ["BTC Option Grid Bot Team"]
readme = "README.md"
packages = [{include = "btc_option_grid_bot"}]

[tool.poetry.dependencies]
python = "^3.9"
# 异步框架
aiohttp = "^3.9.0"
websockets = "^12.0"

# 数据处理
pandas = "^2.1.0"
numpy = "^1.24.0"
scipy = "^1.11.0"

# 数据库和缓存
redis = "^5.0.0"
asyncpg = "^0.29.0"
sqlalchemy = "^2.0.0"
alembic = "^1.12.0"

# 分布式任务队列
celery = "^5.3.0"
kombu = "^5.3.0"

# 配置管理
pydantic = "^2.5.0"
python-dotenv = "^1.0.0"
PyYAML = "^6.0.1"

# 日志和监控
loguru = "^0.7.0"
prometheus-client = "^0.19.0"

# 通知服务
python-telegram-bot = "^20.7"

# 测试框架
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"

# 开发工具
black = "^23.11.0"
flake8 = "^6.1.0"
mypy = "^1.7.0"
pre-commit = "^3.6.0"

[tool.poetry.group.dev.dependencies]
jupyter = "^1.0.0"
ipython = "^8.17.0"

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --cov=src --cov-report=term-missing"
testpaths = [
    "tests",
]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"

[tool.coverage.run]
source = ["btc_option_grid_bot/src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__init__.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]