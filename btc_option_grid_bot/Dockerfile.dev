# 开发环境Dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 安装Poetry
RUN pip install poetry

# 复制Poetry配置文件
COPY pyproject.toml poetry.lock* ./

# 配置Poetry
RUN poetry config virtualenvs.create false

# 安装Python依赖
RUN poetry install --no-dev

# 复制应用代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "-m", "btc_option_grid_bot.src.main"]