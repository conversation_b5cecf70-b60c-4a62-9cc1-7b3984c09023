# BTC期权网格交易机器人 - Python依赖包
# 基于pyproject.toml生成的requirements.txt文件

# 异步框架
aiohttp>=3.9.0
websockets>=12.0

# 数据处理
pandas>=2.2.0
numpy>=1.26.0
scipy>=1.12.0

# 数据库和缓存
redis>=5.0.0
asyncpg>=0.29.0
sqlalchemy>=2.0.0
alembic>=1.12.0

# 分布式任务队列
celery>=5.3.0
kombu>=5.3.0

# 配置管理
pydantic>=2.5.0
python-dotenv>=1.0.0
PyYAML>=6.0.1
watchdog>=3.0.0

# 日志和监控
loguru>=0.7.0
prometheus-client>=0.19.0

# 通知服务
python-telegram-bot>=20.7

# 测试框架
pytest>=7.4.3
pytest-asyncio>=0.21.1
pytest-cov>=4.1.0

# 开发工具
black>=23.11.0
flake8>=6.1.0
mypy>=1.7.0
pre-commit>=3.6.0

# 开发依赖
jupyter>=1.0.0
ipython>=8.17.0