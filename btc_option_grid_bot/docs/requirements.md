# BTC智能期权网格策略交易架构 - 需求文档

## 📋 项目简介

本项目旨在构建一个完全独立的BTC智能期权交易系统，核心是用期权工具实现"分批抄底+分批卖出"策略。通过Sell Put/Call期权替代传统现货网格，实现更高的资金利用率，同时融合跨交易所因果信号分析进行策略增强。系统结合期权时间价值收益与方向性判断，在保证金模式下实现多倍资金效率。

**相关文档**：
- 设计文档：#[[file:design.md]]
- 实施任务：#[[file:tasks.md]]
- 微观结构信号模块：#[[file:微观结构信号模块文档.md]]

## 🎯 用户故事与需求

### 1. 数据接入与处理

#### 1.1 多交易所数据源集成
**用户故事**: 作为系统运营者，我希望能够同时接收Binance和Deribit的实时市场数据，以便进行跨交易所的因果分析。

**验收标准**:
1. 系统应当能够通过WebSocket连接到Binance，实时接收BTC现货和永续合约的价格、成交量、Taker Flow数据
2. 系统应当能够通过WebSocket连接到Deribit，实时接收BTC期权链数据、Greeks参数、持仓量(OI)变化
3. 系统应当在数据源断线时自动重连，重连间隔应当从1秒开始指数退避，最大不超过30秒
4. 系统应当实现数据同步缓冲区，允许±200ms的可接受延迟范围，通过智能缓冲机制实现跨交易所数据的时间对齐
5. 系统应当实现三级数据分层存储：热数据存储在内存中（<1秒访问），温数据存储在Redis中（1-300秒访问），冷数据存储在TimescaleDB中（历史分析用）
6. 系统应当使用优化的缓存策略：市场数据(hash类型,TTL=300s)、仓位数据(json类型,TTL=60s)、Greeks数据(hash类型,TTL=120s)、期权链数据(sorted_set类型,TTL=240s)、微观结构信号(json类型,TTL=30s)

#### 1.2 数据同步与一致性保证
**用户故事**: 作为交易策略引擎，我需要获得时间同步的、一致性保证的市场数据，以便做出准确的交易决策。

**验收标准**:
1. 系统应当实现DataSynchronizer组件，确保来自不同交易所的数据按照统一时间线对齐
2. 系统应当检测并过滤异常数据点（如价格跳跃超过5%、成交量异常等）
3. 系统应当在数据缺失时使用插值算法填补空缺，插值时间窗口不超过30秒
4. 系统应当维护数据质量监控指标，包括延迟、丢包率、异常数据比例
5. 系统应当在数据质量下降时向管理员发送告警通知

#### 1.3 期权链数据管理
**用户故事**: 作为期权策略模块，我需要获得完整准确的期权链数据和Greeks参数，以便计算期权策略的风险收益。

**验收标准**:
1. 系统应当实时维护BTC期权链数据，包括所有到期日的Call和Put期权
2. 系统应当实时获取Deribit提供的完整Greeks参数（Delta、Gamma、Theta、Vega、Rho），通过WebSocket ticker订阅实时更新
3. 系统应当实现期权链的智能过滤，仅保留流动性充足的期权合约（成交量>100或持仓量>500）
4. 系统应当跟踪期权到期日，提前7天开始到期风险监控
5. 系统应当基于Deribit实时Greeks数据计算GEX（Gamma Exposure），公式为: GEX = Gamma × Open Interest × Contract Size，实时更新
6. 系统应当计算GEX分布加权均值，定位当前Gamma高密度区位置，并判断价格是否处于潜在Gamma Squeeze区域
7. 系统应当将GEX数据存储在Redis中，使用hash类型，TTL=120秒，与Greeks数据同步更新

### 2. 因果分析引擎

#### 2.1 跨交易所资金流向因果分析
**用户故事**: 作为分支策略引擎，我需要通过分析Binance现货/永续与Deribit期权市场的资金流向差异，识别市场结构性机会并指导期权策略选择。

**验收标准**:
1. **主流资金vs期权防御分析**：
   - 实时监控Binance Taker Buy/Sell Ratio，识别主流资金的方向性偏好
   - 对比Deribit Call/Put OI变化率，分析专业资金的风险对冲行为
   - 当主流资金强势做多但期权市场Put防御增加时，立即识别为"结构分歧"信号
2. **融资成本vs隐含波动率套利机会**：
   - 实时计算Binance永续Funding Rate与Deribit期权IV Skew的背离程度
   - 当Funding Rate极度负值但Put Skew不高时，立即识别为"恐慌错配"信号
   - 为Iron Condor等波动率策略提供实时入场时机判断
3. **清算压力vs Gamma集中区域重叠分析**：
   - 实时整合多交易所清算热力图数据，识别价格密集清算区域
   - 结合Deribit GEX分布，发现清算压力与Gamma集中区域的重叠点
   - 重叠区域往往是价格剧烈波动的触发点，为方向性期权提供精准入场机会
4. **实时信号生成与评分机制**：
   - 基于数据变化实时计算STRUCTURE_DIVERGENCE、VOLATILITY_MISMATCH、GAMMA_LIQUIDATION_OVERLAP三个核心信号
   - 信号强度超过阈值时立即触发策略评估，而非定时执行
   - 基于历史回测结果动态调整信号权重和阈值（初始阈值0.65）
5. **数据存储与访问**：系统应当将分析结果实时存储在Redis中，支持信号历史查询和回测验证

#### 2.2 市场状态识别
**用户故事**: 作为策略协调器，我需要准确识别当前市场状态（趋势/震荡），以便选择合适的交易策略。

**验收标准**:
1. 系统应当实现MainMarketDetector，用于识别稳定趋势状态
2. 系统应当实现BranchMarketDetector，用于识别短期交易机会
3. 系统应当综合价格动量、成交量、IV Rank、Funding Rate等指标进行状态判断
4. 系统应当输出标准化的市场状态：TRENDING_UP、TRENDING_DOWN、SIDEWAYS、HIGH_VOLATILITY
5. 系统应当记录状态切换历史，用于策略回测和优化

### 3. 主策略引擎

#### 3.1 期权版分批抄底/卖出策略
**用户故事**: 作为主策略引擎，我需要实现用期权工具替代现货的分批交易策略，通过Sell Put实现抄底，Sell Call实现高位卖出，获得比传统网格更高的资金效率。

**验收标准**:
1. **下跌抄底模式**：系统应当分批卖出OTM Put期权（Delta 0.1-0.2），每次价格下跌2-5%时卖出一批
   - 若期权被行权：以预设低价获得BTC现货（实现抄底效果）
   - 若期权不被行权：获得权利金收益（时间价值收益）
2. **上涨卖出模式**：系统应当针对持有的现货仓位分批卖出OTM Call期权（Delta 0.1-0.2）
   - 若期权被行权：以预设高价卖出BTC现货（实现高位卖出）
   - 若期权不被行权：保留现货同时获得权利金收益
3. **震荡收益模式**：系统应当在震荡市场中执行Short Strangle/Iron Condor策略，同时卖出Put和Call收获时间价值
4. 系统应当将策略分为3-5个批次，每批使用主策略资金的20%，根据市场状态动态调整批次规模
5. 系统应当在IV Rank>70%时优先考虑卖出期权，在IV Rank<30%时减少卖出频率

#### 3.2 动态价位调整与仓位管理
**用户故事**: 作为期权策略管理器，我需要根据市场波动和关键价位动态调整期权strike价格和仓位分配。

**验收标准**:
1. 系统应当基于最近7-14日VWAP和历史支撑/阻力位确定期权strike价格区间
2. 系统应当识别关键技术位（如前期低点、成交密集区、布林带边缘）作为期权strike选择参考
3. 系统应当动态调整期权strike分布，Put期权选择价格下方8-20%区间，Call期权选择价格上方5-15%区间
4. 系统应当在价格突破历史区间15%时重新评估并调整期权组合
5. 系统应当支持递进式仓位配置：第一批10%、第二批15%、第三批20%，后续批次根据市场状态调整
6. 系统应当在期权Delta值偏离目标范围(0.1-0.2)超过±0.05时考虑调仓或滚动到新合约

#### 3.3 期权到期管理
**用户故事**: 作为风险管理员，我需要系统自动处理期权到期和行权事务，避免意外损失。

**验收标准**:
1. 系统应当实时跟踪所有期权仓位的到期时间，通过ExpiryManager组件实现
2. 系统应当在到期前7天开始发送风险评估报告，每30分钟检查一次到期情况
3. 系统应当在到期前1天自动执行强制平仓或准备行权资金
4. 系统应当自动计算ITM期权的行权概率和所需资金
5. 系统应当在期权被行权后自动调整现货仓位，并可选择执行滚动策略到下个月合约
6. 系统应当将到期数据存储在Redis中，使用json类型，TTL=1800秒（30分钟），支持到期日的快速查询和风险预警

### 4. 分支策略引擎

#### 4.1 微观结构信号精准出场
**用户故事**: 作为分支策略引擎，我需要基于市场微观结构变化实现方向性期权的精准出场，避免错过最佳止盈时机。

**验收标准**:
1. 系统应当实时计算4个核心微观结构信号：VWAP vs LWAP Spread、Order Book Depth 1%、Order Book Turnover Rate、Taker Net Pressure
2. 系统应当在VWAP-LWAP价差< -0.5时识别大单砸盘+小单追涨的危险信号
3. 系统应当监控订单簿1%价格范围内的深度变化，深度下降超过历史均值30%时触发流动性告警
4. 系统应当在订单簿档位变化率>0.4时识别高频刷单主导的不稳定状态
5. 系统应当检测主动买盘压力与价格走势的背离，当买盘主导但价格不涨时触发趋势失效信号
6. 系统应当在多个微观结构信号同时恶化时自动执行方向性期权的止盈平仓
7. 系统应当将微观结构数据存储在Redis中，使用hash类型，TTL=60秒，支持实时查询和历史回溯

#### 4.2 方向性突破策略（集成微观结构出场）
**用户故事**: 作为分支策略引擎，我需要在检测到跨交易所因果信号时执行方向性期权交易，并通过微观结构信号实现精准出场。

**验收标准**:
1. **入场逻辑**：系统应当在检测到"Binance Taker ↑ + Deribit Call OI ↑ + GEX负暴露"时执行买入OTM Call策略
2. **入场逻辑**：系统应当在检测到"Binance Taker ↓ + Deribit Put OI ↑ + 清算密集区"时执行买入OTM Put策略
3. **仓位管理**：系统应当选择Delta 0.1-0.2的期权，剩余期权不少于5天
4. **出场逻辑**：系统应当优先基于微观结构信号进行精准止盈，而非固定200%/300%目标
5. **风险控制**：系统应当设置50%的最大亏损止损线作为最后防线
6. **策略切换**：系统应当在微观结构信号触发出场后，评估是否切换到Iron Condor等波动率策略

#### 4.3 结构冲突套利策略
**用户故事**: 作为套利策略模块，我需要在市场出现结构性分歧时通过波动率策略获得收益。

**验收标准**:
1. 系统应当在检测到"Binance做多强势 + Deribit Put防御增加"时执行Short Iron Condor策略
2. 系统应当构建对称的Short Strangle，Call和Put的Delta均为0.15
3. 系统应当设置保护翼（Buy Wings），距离Sell legs ±$1000-$1500
4. 系统应当确保收入/风险比至少为1:3
5. 系统应当在Gamma加速进入趋势区时立即平仓止损

#### 4.4 策略协调与资金分配机制
**用户故事**: 作为策略协调器，我需要确保主策略（期权卖方）和分支策略（期权买方）之间实现资金效率最优化，避免保证金冲突。

**验收标准**:
1. **动态资金分配**：系统应当维护基础分配比例为主策略70-75%，分支策略20-25%，预留5-10%作为行权准备金
2. **保证金协调管理**：
   - 主策略保证金需求实时监控，确保不超过总资金的60%
   - 分支策略权利金支出控制在总资金的15%内
   - 行权准备金专项管理，确保ITM期权有足额资金履约
3. **市场状态适应性调整**：
   - 高波动期(IV>80%)：增加主策略比重5%，利用时间价值收益
   - 强趋势期：增加分支策略比重5%，捕捉方向性机会
   - 震荡期：保持基础分配，优化Short Strangle策略
4. **策略冲突防护**：系统应当检测主分支策略的Delta敞口，当总Delta敞口超过±0.5时触发自动对冲机制
5. **流动性保护**：系统应当监控期权订单对市场深度的影响，当影响超过20%时分批执行或延迟15分钟

### 5. 风险管理系统

#### 5.1 期权专用风险监控
**用户故事**: 作为风险管理员，我需要实时监控期权卖方策略的特有风险，包括Gamma爆炸、时间价值损失、行权风险等。

**验收标准**:
1. **保证金风险监控**：
   - 总保证金占用率监控，安全区间<50%，警戒线50-70%，强制减仓线>70%
   - 单个期权合约保证金不得超过总资金的8%
   - 预留30%现金作为保证金缓冲和行权准备金
2. **Greeks风险监控**：
   - Delta敞口：组合总Delta控制在±0.5以内，超过±0.3时发送告警
   - Gamma风险：单日Gamma损失不超过总资金3%，极端市场下不超过5%
   - Theta收益追踪：确保每日时间价值收益为正，异常时暂停新开仓
   - Vega风险：隐含波动率变化对组合影响不超过总资金4%
3. **期权到期风险**：
   - ITM期权行权概率>80%时，自动预留行权资金
   - 到期前3天开始每日风险评估，到期前1天强制处理
4. **市场流动性风险**：
   - 监控期权买卖价差，价差>5%时暂停交易
   - 持仓集中度：单个到期日持仓不超过总仓位30%
5. 系统应当基于实时仓位监控，当风险指标超过预设阈值时立即触发告警，同时每小时生成风险汇总报告

#### 5.2 保证金计算与管理
**用户故事**: 作为交易执行模块，我需要准确计算期权交易的保证金要求，避免保证金不足。

**验收标准**:
1. 系统应当实现精确的期权保证金计算，考虑组合保证金优惠
2. 系统应当维护30%以上的现金安全边际
3. 系统应当为ITM期权预留足额的行权准备金
4. 系统应当在保证金占用率超过70%时触发强制减仓
5. 系统应当支持保证金要求的实时更新和缓存

#### 5.3 实时风险告警与紧急处理
**用户故事**: 作为系统管理员，我需要系统在风险指标触及阈值时立即告警，并在紧急情况下自动执行保护措施。

**验收标准**:
1. **实时风险告警机制**：
   - 基于实时仓位和市场数据变化，当任一风险指标触及告警阈值时立即发送通知
   - 告警分级：提醒级（黄色）、警告级（橙色）、紧急级（红色）
   - 多渠道告警：系统界面、邮件、Telegram等，确保及时触达
2. **自动化风险处理**：
   - 总仓位浮亏超过8%时自动减仓30%，Delta敞口超过±0.5时自动对冲
   - 保证金占用率超过70%时停止新开仓，超过80%时强制减仓
   - VaR超过日限额时停止所有新开仓操作
3. **数据源异常保护**：
   - 数据源全部中断时自动进入保护模式，停止交易并评估是否需要紧急平仓
   - 单一数据源中断超过30秒时切换到备用数据源或降级模式
4. **异常行为检测**：系统应当检测异常的价格跳跃、成交量异常、延迟异常等，并暂停相关策略
5. **操作日志记录**：系统应当详细记录所有告警触发、自动处理操作和人工干预，便于事后分析和优化

### 6. 交易执行系统

#### 6.1 智能订单管理
**用户故事**: 作为交易执行引擎，我需要高效准确地执行期权订单，确保最佳执行价格。

**验收标准**:
1. 系统应当支持期权订单的批量提交，减少网络延迟
2. 系统应当实现订单状态的实时跟踪（待成交、部分成交、全部成交、已取消）
3. 系统应当在订单执行失败时自动重试，最多重试3次
4. 系统应当记录每笔订单的执行时间，目标执行延迟<5秒
5. 系统应当支持条件订单（如价格触发、时间触发）

#### 6.2 仓位管理
**用户故事**: 作为仓位管理器，我需要实时跟踪所有现货和期权仓位，确保数据准确性。

**验收标准**:
1. 系统应当实时更新所有现货和期权仓位信息
2. 系统应当计算组合的实时PnL，包括已实现和未实现损益
3. 系统应当跟踪每个策略的仓位分布和风险敞口
4. 系统应当在仓位数据出现异常时发送告警
5. 系统应当支持仓位数据的历史查询和导出

#### 6.3 行权处理
**用户故事**: 作为期权行权处理器，我需要自动处理期权到期行权事务，确保资金安全。

**验收标准**:
1. 系统应当在期权到期日自动判断是否应该行权
2. 系统应当为可能被行权的Short期权准备足额资金或现货
3. 系统应当在行权后自动更新现货仓位和资金余额
4. 系统应当记录所有行权操作的详细信息
5. 系统应当支持手动干预行权决策

### 7. 系统架构要求

#### 7.1 分层架构实现
**用户故事**: 作为系统架构师，我需要系统严格按照6层分层架构实现，确保模块间清晰的职责分离和接口定义。

**验收标准**:
1. 系统应当实现API Gateway Layer，负责所有外部交易所接口和数据源管理，提供统一的数据接入接口
2. 系统应当实现Data Layer，包含DataEngine、CausalEngine、CacheManager，负责数据处理、因果分析和缓存管理
3. 系统应当实现Strategy Layer，包含OptionGridStrategy、BranchStrategy、StrategyCoordinator，负责策略逻辑和协调
4. 系统应当实现Risk Management Layer，包含RiskEngine、ExpiryManager、MarginCalculator，负责风险控制和保证金管理
5. 系统应当实现Execution Layer，包含OrderManager、PositionManager、PnLTracker，负责订单执行和仓位管理
6. 系统应当实现Infrastructure Layer，包含EventBus、StateManager、ConfigManager，负责事件处理、状态管理和配置管理
7. 各层之间应当通过明确定义的接口通信，禁止跨层直接调用
8. 每层应当实现独立的错误处理和日志记录机制

### 8. 系统基础设施

#### 8.1 配置管理
**用户故事**: 作为系统运维人员，我需要灵活配置系统参数，支持不同的交易环境。

**验收标准**:
1. 系统应当支持YAML格式的配置文件，包括策略参数、风险参数、交易所配置
2. 系统应当实现配置的热加载，无需重启系统即可生效
3. 系统应当验证配置参数的合法性，防止错误配置导致系统异常
4. 系统应当支持不同环境的配置（开发、测试、生产）
5. 系统应当记录配置变更历史，支持配置回滚

#### 7.2 日志与监控
**用户故事**: 作为系统管理员，我需要详细的日志记录和监控指标，便于问题诊断和性能优化。

**验收标准**:
1. 系统应当使用结构化日志格式，包含时间戳、级别、模块、消息等字段
2. 系统应当记录所有交易决策、订单执行、风险事件的详细日志
3. 系统应当集成Prometheus监控，收集系统性能指标
4. 系统应当在关键错误发生时通过Telegram/邮件发送告警
5. 系统应当支持日志的自动轮转和压缩存储

#### 7.3 数据存储
**用户故事**: 作为数据管理员，我需要可靠的数据存储方案，支持实时查询和历史分析。

**验收标准**:
1. 系统应当使用Redis Cluster作为实时数据缓存，支持高可用
2. 系统应当使用PostgreSQL + TimescaleDB存储历史交易数据
3. 系统应当实现数据的自动备份，每日备份并保留30天
4. 系统应当支持数据的增量同步和数据压缩
5. 系统应当在数据库连接异常时自动重连，并发送告警通知

#### 7.4 Telegram通知与远程控制
**用户故事**: 作为交易者，我需要通过Telegram实时监控系统状态和接收告警通知，以便随时了解交易情况并进行必要的远程控制。

**验收标准**:
1. 系统应当集成Telegram Bot，支持授权用户的身份验证和权限控制
2. 系统应当支持以下查询命令：/status（系统状态）、/positions（持仓信息）、/risk（风险指标）、/pnl（损益统计）、/signals（交易信号）
3. 系统应当支持以下控制命令：/pause（暂停策略）、/resume（恢复策略）、/emergency（紧急停止）
4. 系统应当在风险告警、策略状态变化、重要交易事件发生时主动推送Telegram消息
5. 系统应当使用分级告警机制：🚨严重、⚠️警告、⚡中等、ℹ️信息、📊状态更新
6. 系统应当提供友好的Markdown格式消息展示，包含必要的emoji图标和数据格式化
7. 系统应当记录所有Telegram交互日志，用于审计和问题追溯
8. 系统应当支持多用户授权，但限制敏感操作权限

### 9. 流动性分析与风险控制

#### 9.1 期权流动性评估
**用户故事**: 作为风险管理员，我需要实时评估期权合约的流动性状况，避免在流动性不足时执行交易。

**验收标准**:
1. 系统应当实时监控期权的Bid-Ask价差，当相对价差>5%时暂停该合约交易
2. 系统应当分析期权成交量和持仓量趋势，成交量<100或持仓量<500的合约自动过滤
3. 系统应当评估市场深度，监控1%、2%、5%价格范围内的订单簿深度变化
4. 系统应当计算综合流动性评分(0-100)，评分<20时停止交易，<40时减少仓位
5. 系统应当在流动性异常时发送告警，支持多级别风险预警

#### 9.2 市场深度分析
**用户故事**: 作为交易执行模块，我需要了解市场深度情况，优化订单执行策略。

**验收标准**:
1. 系统应当实时计算买卖盘不平衡度，识别市场偏向
2. 系统应当监控订单簿档位变化率，>0.4时识别高频刷单主导状态
3. 系统应当评估大单对市场的冲击，当影响>20%时分批执行
4. 系统应当提供深度评分，指导订单大小和执行时机
5. 系统应当记录深度变化历史，用于流动性模式识别

### 10. 策略回测与验证

#### 10.1 历史数据回测
**用户故事**: 作为策略开发者，我需要使用历史数据验证策略的有效性和风险特征。

**验收标准**:
1. 系统应当支持高精度历史数据回放，时间精度达到秒级
2. 系统应当提供模拟交易环境，支持无滑点和真实滑点两种模式
3. 系统应当计算完整的性能指标：总收益率、年化收益率、夏普比率、最大回撤、胜率等
4. 系统应当支持策略参数敏感性分析和优化
5. 系统应当生成详细的回测报告，包括交易明细和风险分析

#### 10.2 实时策略验证
**用户故事**: 作为风险管理员，我需要实时验证策略表现是否符合历史回测预期。

**验收标准**:
1. 系统应当实时计算策略的关键指标，与回测结果对比
2. 系统应当在策略表现偏离预期时发送告警
3. 系统应当支持策略的动态调整和参数优化
4. 系统应当记录策略执行的详细日志，便于事后分析
5. 系统应当支持多策略并行回测和比较

### 11. 系统监控与运维

#### 11.1 实时监控面板
**用户故事**: 作为系统管理员，我需要通过直观的Web界面监控系统状态和交易情况。

**验收标准**:
1. 系统应当提供现代化的Web监控界面，支持实时数据更新
2. 系统应当展示系统概览：运行状态、PnL、活跃策略、保证金使用率等
3. 系统应当提供持仓管理界面，显示所有期权和现货持仓的详细信息
4. 系统应当展示风险监控面板，包括Greeks敞口、VaR、告警信息等
5. 系统应当支持策略控制功能：启动/暂停/停止策略，调整参数

#### 11.2 移动端适配
**用户故事**: 作为交易者，我需要在移动设备上也能监控系统状态和接收告警。

**验收标准**:
1. 系统应当支持响应式设计，在手机和平板上正常显示
2. 系统应当优化移动端的交互体验，支持触摸操作
3. 系统应当在移动端提供核心功能：状态查看、告警接收、紧急控制
4. 系统应当支持离线缓存，网络不稳定时仍能显示基本信息
5. 系统应当集成推送通知，重要事件及时提醒

### 12. 数据存储与管理

#### 12.1 时序数据存储
**用户故事**: 作为数据管理员，我需要高效存储和查询大量的时序交易数据。

**验收标准**:
1. 系统应当使用PostgreSQL + TimescaleDB存储时序数据，支持自动分区
2. 系统应当实现数据压缩策略，7天后自动压缩，90天后归档
3. 系统应当提供高性能查询接口，支持复杂的时序分析查询
4. 系统应当实现数据备份策略，每日增量备份，每周全量备份
5. 系统应当监控数据库性能，慢查询>1秒时发送告警

#### 12.2 数据质量管理
**用户故事**: 作为数据分析师，我需要确保存储的数据质量和完整性。

**验收标准**:
1. 系统应当实现数据验证机制，检测异常值和缺失数据
2. 系统应当提供数据修复功能，自动填补小范围缺失数据
3. 系统应当记录数据血缘，追踪数据的来源和处理过程
4. 系统应当提供数据质量报告，定期评估数据完整性
5. 系统应当支持数据导出功能，便于外部分析和审计

### 13. 性能与可靠性

#### 13.1 系统性能要求
**用户故事**: 作为系统使用者，我需要系统具备高性能，能够及时响应市场变化。

**验收标准**:
1. 系统决策延迟应当<30秒
2. 订单执行延迟应当<5秒
3. 系统可用性应当>99.5%
4. 内存使用应当<3GB（考虑双WebSocket数据流、微观结构信号实时计算、GEX分布计算等负载）
5. CPU使用率应当<40%（保留实时计算能力：微观结构信号、因果分析、风险计算）

#### 13.2 容错与恢复
**用户故事**: 作为系统管理员，我需要系统具备良好的容错能力，能够从异常中快速恢复。

**验收标准**:
1. 系统应当在崩溃后能够从Redis缓存中恢复仓位状态
2. 系统应当在网络中断时自动重连，并同步丢失的数据
3. 系统应当在数据库故障时切换到备用数据源
4. 系统应当支持手动和自动的系统健康检查
5. 系统应当在启动时验证所有组件的完整性

## 🎯 非功能性需求

### 安全性要求
- 所有API密钥应当加密存储
- 系统应当支持IP白名单限制
- 交易操作应当记录操作日志用于审计

### 可扩展性要求
- 系统架构应当支持水平扩展
- 应当预留接口支持新的交易所接入
- 应当支持新策略模块的插件式集成

### 监控要求
- 系统应当提供实时性能监控
- 应当支持自定义告警规则
- 应当提供交易报表和风险报告


## 14. IBIT/IBKR 接入与执行需求（新增）

### 14.1 执行与账户（IBKR）
**用户故事**: 作为策略执行引擎，我需要通过 IBKR 在美股市场交易 IBIT ETF 及其期权，支持美式期权的行权/指派生命周期。

**验收标准**:
1. 系统应当通过 IBKR TWS/Gateway 建立稳定连接，提供心跳/重连机制（指数退避，最大 30 秒）
2. 系统应当支持 IBIT 期权链与报价拉取，至少包含：报价、成交量、OI、Delta/IV（如可得）
3. 系统应当支持下单/撤单（限价/IOC），支持 OCA/分批执行，提供部分成交与状态跟踪
4. 系统应当支持账户 Buying Power 查询与校验，保证 CSP（现金担保卖 PUT）足额资金
5. 系统应当接收并处理 Assignment/Exercise 事件，自动更新 IBIT 现货持仓，并触发后续策略（如 covered call）

### 14.2 市场时段与开盘跳空
**用户故事**: 作为风险控制模块，我需要确保仅在合适的市场时段执行，并处理开盘跳空导致的滑点。

**验收标准**:
1. 支持 market_hours_policy：open_only / prepost_allowed / queue_until_open；默认 queue_until_open
2. 闭市期间的入场信号应入队；开盘后 T+15~60 秒重估价格与流动性再下单
3. 开盘跳空超出阈值（如 > x%）时自动减小下单规模或延迟执行

### 14.3 BTC→IBIT 信号映射与过滤
**用户故事**: 作为信号适配层，我需要把 Binance/Deribit 的 BTC 信号映射为 IBIT 侧的可执行合约与价格。

**验收标准**:
1. 建立滚动回归/比值模型：IBIT_est = a + b · BTCUSD，并维护 tracking_error_band（阈值可配置）
2. 超出误差带时，降低规模或延迟执行；所有映射均需以 IBIT 实时行情二次复核
3. 期权选择遵循目标 Delta 与期限（Long Call 默认 delta 0.6–0.8，期限 30–90 天；CSP strike 基于映射价与技术位）
4. 执行前校验最小流动性/价差/OI 阈值（如价差 < 3~5%，OI/量>最小门槛），不满足则跳过或降级

### 14.4 策略路径与资金约束
**用户故事**: 作为策略协调器，我需要在不同库存与资金条件下选择合适的策略路径。

**验收标准**:
1. 启用策略路径：CSP 为主、Long Call 为辅；禁止裸卖 Call；可选直接买入 IBIT 份额补齐底仓
2. 允许在“现货库存比例低于阈值”时开 Long Call（默认阈值 30%），并控制 theta 风险与展期
3. CSP 名义与日内规模应受限于账户 Buying Power 与配置上限（例如 daily_max_csp_notional）

### 14.5 American 提前指派管理
**用户故事**: 作为到期与生命周期管理器，我需要管理美式期权的提前指派风险。

**验收标准**:
1. 在到期前 N 天（可配）监控短 PUT 的外在价值阈值与 ITM 深度，外在价值趋近 0 或深 ITM 时触发展期/平仓建议
2. 记录并处理 Assignment/Exercise 事件，更新现货持仓与后续策略动作（如自动/半自动 covered call）
3. 生成到期风险报告（含 extrinsic、Delta、Gamma、到期天数、预期现金/持仓影响），频率可配（默认 30 分钟）

### 14.6 配置与审计
**用户故事**: 作为系统管理员，我需要可配置的参数与可审计的执行日志。

**验收标准**:
1. 新增配置项：broker=IBKR、market_hours_policy、enable_csp、enable_long_call_when_spot_ratio_below、tracking_error_band、extrinsic_roll_threshold、max_slippage_bps、min_liquidity_thresholds 等
2. 所有 IBIT/IBKR 相关交易与事件（订单、成交、assignment、展期决策）应完整记录于日志与数据库，支持审计与回放

## 🧪 测试要求

### 测试文件组织规范

所有测试文件必须严格按照以下结构组织，不允许在其他位置创建测试相关文件：

```
btc_option_grid_bot/tests/
├── unit/                    # 单元测试（每个组件对应一个测试文件）
├── integration/             # 集成测试
└── fixtures/                # 测试数据和工具
```

### 测试开发规范

1. **一对一映射**: 每个组件必须对应一个单元测试文件
2. **测试覆盖率**: 单元测试覆盖率必须 > 80%
3. **测试独立性**: 每个测试方法独立运行，不依赖其他测试
4. **资源清理**: 测试完成后自动清理，不产生临时文件
5. **外部依赖模拟**: 使用mock模拟Redis、数据库等外部服务

### 禁止的开发实践

- ❌ 在项目根目录或其他位置创建临时测试脚本
- ❌ 创建任务完成报告文档（如 `TASK_X_COMPLETION_REPORT.md`）
- ❌ 创建演示脚本（如 `demo_*.py`）
- ❌ 在tests目录外创建任何测试相关文件

## 📊 验收标准总结

项目成功的关键指标：
1. 能够稳定运行72小时无重大故障
2. 主要功能模块的单元测试覆盖率>80%
3. 模拟交易环境下策略收益率满足预期
4. 风险控制机制能够有效防范极端行情
5. 系统性能指标达到设计要求
6. 所有测试文件严格按照规范组织，无额外临时文件