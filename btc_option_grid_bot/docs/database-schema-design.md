# BTC期权网格交易系统 - 数据库Schema设计

## 📋 概述

本文档定义了BTC期权网格交易系统的完整数据库架构，基于PostgreSQL + TimescaleDB实现高性能时序数据存储。

## 🏗️ 架构设计

### 数据库选型
- **主数据库**: PostgreSQL 15+
- **时序扩展**: TimescaleDB 2.11+
- **连接池**: PgBouncer
- **读写分离**: 主从复制架构

### 数据分层策略
- **热数据**: 内存缓存 (<1秒访问)
- **温数据**: Redis缓存 (1-300秒)
- **冷数据**: TimescaleDB (历史数据)

## 📊 核心表结构

### 1. 系统配置表

```sql
-- 系统配置表
CREATE TABLE system_config (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value JSONB NOT NULL,
    config_type VARCHAR(50) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 策略配置表
CREATE TABLE strategy_config (
    id SERIAL PRIMARY KEY,
    strategy_name VARCHAR(100) NOT NULL,
    strategy_type VARCHAR(50) NOT NULL,
    parameters JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 2. 市场数据表 (TimescaleDB超表)

```sql
-- 现货价格数据
CREATE TABLE spot_prices (
    timestamp TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    price DECIMAL(20,8) NOT NULL,
    volume DECIMAL(20,8),
    bid DECIMAL(20,8),
    ask DECIMAL(20,8),
    source VARCHAR(20) NOT NULL,
    data_quality VARCHAR(10) DEFAULT 'HIGH'
);

-- 转换为TimescaleDB超表
SELECT create_hypertable('spot_prices', 'timestamp', chunk_time_interval => INTERVAL '1 hour');

-- 期权价格数据
CREATE TABLE option_prices (
    timestamp TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    underlying_symbol VARCHAR(20) NOT NULL,
    strike_price DECIMAL(20,8) NOT NULL,
    expiry_date DATE NOT NULL,
    option_type VARCHAR(4) NOT NULL, -- CALL/PUT
    price DECIMAL(20,8) NOT NULL,
    bid DECIMAL(20,8),
    ask DECIMAL(20,8),
    volume DECIMAL(20,8),
    open_interest DECIMAL(20,8),
    implied_volatility DECIMAL(10,6),
    delta DECIMAL(10,6),
    gamma DECIMAL(10,6),
    theta DECIMAL(10,6),
    vega DECIMAL(10,6),
    rho DECIMAL(10,6),
    source VARCHAR(20) NOT NULL
);

SELECT create_hypertable('option_prices', 'timestamp', chunk_time_interval => INTERVAL '1 hour');

-- 订单簿数据
CREATE TABLE orderbook_data (
    timestamp TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    side VARCHAR(4) NOT NULL, -- BID/ASK
    price DECIMAL(20,8) NOT NULL,
    quantity DECIMAL(20,8) NOT NULL,
    level_index INTEGER NOT NULL,
    source VARCHAR(20) NOT NULL
);

SELECT create_hypertable('orderbook_data', 'timestamp', chunk_time_interval => INTERVAL '30 minutes');
```

### 3. 交易执行表

```sql
-- 订单表
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id VARCHAR(100) UNIQUE NOT NULL,
    strategy_id VARCHAR(100),
    symbol VARCHAR(50) NOT NULL,
    side VARCHAR(4) NOT NULL, -- BUY/SELL
    order_type VARCHAR(20) NOT NULL, -- MARKET/LIMIT/STOP
    quantity DECIMAL(20,8) NOT NULL,
    price DECIMAL(20,8),
    filled_quantity DECIMAL(20,8) DEFAULT 0,
    avg_fill_price DECIMAL(20,8),
    status VARCHAR(20) NOT NULL, -- PENDING/FILLED/CANCELLED/REJECTED
    exchange VARCHAR(20) NOT NULL,
    commission DECIMAL(20,8),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    filled_at TIMESTAMPTZ,
    cancelled_at TIMESTAMPTZ
);

-- 成交记录表
CREATE TABLE trades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trade_id VARCHAR(100) UNIQUE NOT NULL,
    order_id UUID REFERENCES orders(id),
    symbol VARCHAR(50) NOT NULL,
    side VARCHAR(4) NOT NULL,
    quantity DECIMAL(20,8) NOT NULL,
    price DECIMAL(20,8) NOT NULL,
    commission DECIMAL(20,8),
    exchange VARCHAR(20) NOT NULL,
    strategy_id VARCHAR(100),
    executed_at TIMESTAMPTZ DEFAULT NOW()
);

-- 持仓表
CREATE TABLE positions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    symbol VARCHAR(50) NOT NULL,
    strategy_id VARCHAR(100),
    position_type VARCHAR(10) NOT NULL, -- SPOT/OPTION
    quantity DECIMAL(20,8) NOT NULL,
    avg_price DECIMAL(20,8) NOT NULL,
    market_value DECIMAL(20,8),
    unrealized_pnl DECIMAL(20,8),
    realized_pnl DECIMAL(20,8) DEFAULT 0,
    exchange VARCHAR(20) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(symbol, strategy_id, exchange)
);
```

### 4. 风险管理表

```sql
-- 风险指标表
CREATE TABLE risk_metrics (
    timestamp TIMESTAMPTZ NOT NULL,
    strategy_id VARCHAR(100),
    portfolio_value DECIMAL(20,8),
    total_pnl DECIMAL(20,8),
    unrealized_pnl DECIMAL(20,8),
    realized_pnl DECIMAL(20,8),
    delta_exposure DECIMAL(20,8),
    gamma_exposure DECIMAL(20,8),
    theta_exposure DECIMAL(20,8),
    vega_exposure DECIMAL(20,8),
    var_95 DECIMAL(20,8),
    var_99 DECIMAL(20,8),
    max_drawdown DECIMAL(10,6),
    sharpe_ratio DECIMAL(10,6),
    risk_score DECIMAL(10,6)
);

SELECT create_hypertable('risk_metrics', 'timestamp', chunk_time_interval => INTERVAL '1 day');

-- 告警记录表
CREATE TABLE alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    alert_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL, -- LOW/MEDIUM/HIGH/CRITICAL
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    component VARCHAR(100),
    strategy_id VARCHAR(100),
    metadata JSONB,
    is_resolved BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    resolved_at TIMESTAMPTZ
);

-- 保证金记录表
CREATE TABLE margin_requirements (
    timestamp TIMESTAMPTZ NOT NULL,
    strategy_id VARCHAR(100),
    exchange VARCHAR(20) NOT NULL,
    required_margin DECIMAL(20,8) NOT NULL,
    available_margin DECIMAL(20,8) NOT NULL,
    margin_ratio DECIMAL(10,6) NOT NULL,
    maintenance_margin DECIMAL(20,8) NOT NULL
);

SELECT create_hypertable('margin_requirements', 'timestamp', chunk_time_interval => INTERVAL '1 day');
```

### 5. 分析数据表

```sql
-- 信号分析表
CREATE TABLE signal_analysis (
    timestamp TIMESTAMPTZ NOT NULL,
    signal_type VARCHAR(50) NOT NULL,
    signal_strength DECIMAL(10,6) NOT NULL,
    confidence DECIMAL(10,6) NOT NULL,
    metadata JSONB,
    strategy_triggered VARCHAR(100),
    is_active BOOLEAN DEFAULT true
);

SELECT create_hypertable('signal_analysis', 'timestamp', chunk_time_interval => INTERVAL '1 hour');

-- 流动性分析表
CREATE TABLE liquidity_metrics (
    timestamp TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    bid_ask_spread DECIMAL(20,8),
    spread_percentage DECIMAL(10,6),
    volume_24h DECIMAL(20,8),
    open_interest DECIMAL(20,8),
    market_depth DECIMAL(20,8),
    liquidity_score DECIMAL(10,6),
    liquidity_grade VARCHAR(2)
);

SELECT create_hypertable('liquidity_metrics', 'timestamp', chunk_time_interval => INTERVAL '1 hour');

-- 定价验证表
CREATE TABLE pricing_validation (
    timestamp TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    market_price DECIMAL(20,8) NOT NULL,
    theoretical_price DECIMAL(20,8),
    price_deviation DECIMAL(10,6),
    implied_volatility DECIMAL(10,6),
    validation_score DECIMAL(10,6),
    anomaly_flags TEXT[],
    is_valid BOOLEAN DEFAULT true
);

SELECT create_hypertable('pricing_validation', 'timestamp', chunk_time_interval => INTERVAL '1 hour');
```

### 6. 回测数据表

```sql
-- 回测配置表
CREATE TABLE backtest_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    strategy_type VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    initial_capital DECIMAL(20,8) NOT NULL,
    parameters JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 回测结果表
CREATE TABLE backtest_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_id UUID REFERENCES backtest_configs(id),
    total_return DECIMAL(10,6),
    annualized_return DECIMAL(10,6),
    volatility DECIMAL(10,6),
    max_drawdown DECIMAL(10,6),
    sharpe_ratio DECIMAL(10,6),
    sortino_ratio DECIMAL(10,6),
    total_trades INTEGER,
    win_rate DECIMAL(10,6),
    profit_factor DECIMAL(10,6),
    completed_at TIMESTAMPTZ DEFAULT NOW()
);

-- 回测交易记录表
CREATE TABLE backtest_trades (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    config_id UUID REFERENCES backtest_configs(id),
    timestamp TIMESTAMPTZ NOT NULL,
    symbol VARCHAR(50) NOT NULL,
    side VARCHAR(4) NOT NULL,
    quantity DECIMAL(20,8) NOT NULL,
    price DECIMAL(20,8) NOT NULL,
    pnl DECIMAL(20,8),
    commission DECIMAL(20,8)
);
```

## 🔧 索引优化策略

### 时序数据索引
```sql
-- 现货价格索引
CREATE INDEX idx_spot_prices_symbol_time ON spot_prices (symbol, timestamp DESC);
CREATE INDEX idx_spot_prices_source ON spot_prices (source, timestamp DESC);

-- 期权价格索引
CREATE INDEX idx_option_prices_symbol_time ON option_prices (symbol, timestamp DESC);
CREATE INDEX idx_option_prices_underlying ON option_prices (underlying_symbol, expiry_date, timestamp DESC);
CREATE INDEX idx_option_prices_strike ON option_prices (strike_price, option_type, timestamp DESC);

-- 订单簿索引
CREATE INDEX idx_orderbook_symbol_time ON orderbook_data (symbol, timestamp DESC);
CREATE INDEX idx_orderbook_level ON orderbook_data (symbol, side, level_index, timestamp DESC);
```

### 交易数据索引
```sql
-- 订单索引
CREATE INDEX idx_orders_strategy ON orders (strategy_id, created_at DESC);
CREATE INDEX idx_orders_symbol ON orders (symbol, status, created_at DESC);
CREATE INDEX idx_orders_status ON orders (status, created_at DESC);

-- 成交记录索引
CREATE INDEX idx_trades_strategy ON trades (strategy_id, executed_at DESC);
CREATE INDEX idx_trades_symbol ON trades (symbol, executed_at DESC);

-- 持仓索引
CREATE INDEX idx_positions_strategy ON positions (strategy_id, updated_at DESC);
CREATE INDEX idx_positions_symbol ON positions (symbol, exchange);
```

### 分析数据索引
```sql
-- 风险指标索引
CREATE INDEX idx_risk_metrics_strategy ON risk_metrics (strategy_id, timestamp DESC);

-- 信号分析索引
CREATE INDEX idx_signal_analysis_type ON signal_analysis (signal_type, timestamp DESC);
CREATE INDEX idx_signal_analysis_active ON signal_analysis (is_active, timestamp DESC);

-- 流动性指标索引
CREATE INDEX idx_liquidity_symbol ON liquidity_metrics (symbol, timestamp DESC);

-- 定价验证索引
CREATE INDEX idx_pricing_validation_symbol ON pricing_validation (symbol, timestamp DESC);
CREATE INDEX idx_pricing_validation_valid ON pricing_validation (is_valid, timestamp DESC);
```

## 📈 数据保留策略

### 自动数据压缩
```sql
-- 现货价格数据压缩 (7天后压缩)
SELECT add_compression_policy('spot_prices', INTERVAL '7 days');

-- 期权价格数据压缩 (3天后压缩)
SELECT add_compression_policy('option_prices', INTERVAL '3 days');

-- 订单簿数据压缩 (1天后压缩)
SELECT add_compression_policy('orderbook_data', INTERVAL '1 day');

-- 风险指标压缩 (30天后压缩)
SELECT add_compression_policy('risk_metrics', INTERVAL '30 days');
```

### 数据保留策略
```sql
-- 订单簿数据保留7天
SELECT add_retention_policy('orderbook_data', INTERVAL '7 days');

-- 信号分析数据保留90天
SELECT add_retention_policy('signal_analysis', INTERVAL '90 days');

-- 流动性指标保留180天
SELECT add_retention_policy('liquidity_metrics', INTERVAL '180 days');

-- 定价验证数据保留180天
SELECT add_retention_policy('pricing_validation', INTERVAL '180 days');
```

## 🔄 连接池配置

### PgBouncer配置
```ini
[databases]
trading_system = host=localhost port=5432 dbname=trading_system

[pgbouncer]
pool_mode = transaction
max_client_conn = 100
default_pool_size = 25
max_db_connections = 50
```

### 读写分离配置
```python
# 数据库连接配置
DATABASE_CONFIG = {
    'master': {
        'host': 'master.db.local',
        'port': 5432,
        'database': 'trading_system',
        'pool_size': 20
    },
    'slave': {
        'host': 'slave.db.local', 
        'port': 5432,
        'database': 'trading_system',
        'pool_size': 30
    }
}
```

## 🚀 性能优化

### 分区策略
- 按时间分区：每小时一个chunk
- 按symbol分区：热门交易对独立分区
- 压缩策略：历史数据自动压缩

### 查询优化
- 使用时间范围查询
- 避免全表扫描
- 合理使用索引
- 批量插入优化

### 监控指标
- 查询响应时间 < 100ms
- 插入吞吐量 > 10000 TPS
- 存储压缩率 > 70%
- 连接池利用率 < 80%

---

**总结**：本Schema设计支持高频交易数据存储、实时风险监控、策略回测和性能分析，通过TimescaleDB实现时序数据的高效存储和查询。
