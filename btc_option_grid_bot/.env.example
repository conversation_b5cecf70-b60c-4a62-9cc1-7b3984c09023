# BTC期权网格交易机器人 - 环境变量配置示例
# 复制此文件为 .env 并填入实际配置

# =============================================================================
# 系统环境配置
# =============================================================================
ENVIRONMENT=development
LOG_LEVEL=INFO
MAX_WORKERS=8
DEBUG_MODE=true
USE_TESTNET=true

# =============================================================================
# 交易所API配置
# =============================================================================
# Binance API配置
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here

# Deribit API配置
DERIBIT_API_KEY=your_deribit_api_key_here
DERIBIT_API_SECRET=your_deribit_api_secret_here

# =============================================================================
# 数据库配置
# =============================================================================
# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# PostgreSQL配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=btc_option_grid_bot
DB_USERNAME=btc_option_grid_bot
DB_PASSWORD=btc_option_grid_bot_password

# =============================================================================
# 通知服务配置
# =============================================================================
# Telegram Bot配置
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# 告警Webhook配置
ALERT_WEBHOOK_URL=your_alert_webhook_url_here