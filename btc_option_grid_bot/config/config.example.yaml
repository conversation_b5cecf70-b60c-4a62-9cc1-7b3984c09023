# BTC期权网格交易机器人 - 配置文件示例
# 复制此文件为 config.yaml 并填入实际配置
#
# 配置策略说明：
# - 敏感信息（API密钥、密码）通过环境变量 .env 文件配置
# - 系统配置、策略参数通过此 YAML 文件配置
# - ${VAR_NAME} 占位符会自动替换为对应的环境变量值

# =============================================================================
# 系统基础配置
# =============================================================================
system:
  environment: "${ENVIRONMENT}"  # development, testing, production
  log_level: "${LOG_LEVEL}"      # DEBUG, INFO, WARNING, ERROR, CRITICAL
  max_workers: ${MAX_WORKERS}    # 最大工作线程数
  debug_mode: ${DEBUG_MODE}      # 调试模式开关
  use_testnet: ${USE_TESTNET}    # 是否使用测试网

# =============================================================================
# 交易所配置
# =============================================================================
exchanges:
  binance:
    api_key: "${BINANCE_API_KEY}"
    api_secret: "${BINANCE_API_SECRET}"
    testnet: ${USE_TESTNET}  # 根据环境变量决定是否使用测试网
    rate_limits:
      orders_per_second: 10
      requests_per_minute: 1200
      weight_per_minute: 6000
    endpoints:
      # 测试网端点
      testnet:
        spot_ws: "wss://testnet.binance.vision/ws"
        futures_ws: "wss://testnet.binancefuture.com/ws"
        spot_api: "https://testnet.binance.vision/api"
        futures_api: "https://testnet.binancefuture.com/fapi"
      # 生产网端点
      mainnet:
        spot_ws: "wss://stream.binance.com:9443/ws"
        futures_ws: "wss://fstream.binance.com/ws"
        spot_api: "https://api.binance.com/api"
        futures_api: "https://fapi.binance.com/fapi"

  deribit:
    api_key: "${DERIBIT_API_KEY}"
    api_secret: "${DERIBIT_API_SECRET}"
    testnet: ${USE_TESTNET}  # 根据环境变量决定是否使用测试网
    rate_limits:
      requests_per_second: 20
      requests_per_minute: 1000
    endpoints:
      # 测试网端点
      testnet:
        ws: "wss://test.deribit.com/ws/api/v2"
        api: "https://test.deribit.com/api/v2"
      # 生产网端点
      mainnet:
        ws: "wss://www.deribit.com/ws/api/v2"
        api: "https://www.deribit.com/api/v2"
  ibkr:
    enabled: false
    account_id: ""
    market_hours_policy: "queue_until_open"  # open_only | prepost_allowed | queue_until_open
    pretrade_filters:
      max_spread_bps: 300
      min_oi: 200
      min_volume: 50
      daily_csp_notional_cap: 100000
    strategy:
      enable_csp: true
      enable_long_call_when_spot_ratio_below: 0.3
      long_call_target_delta: 0.65
      long_call_tenor_days: 60


# =============================================================================
# 数据存储配置
# =============================================================================
redis:
  host: "${REDIS_HOST}"
  port: ${REDIS_PORT}
  db: ${REDIS_DB}
  password: "${REDIS_PASSWORD}"
  cluster_mode: false
  connection_pool:
    max_connections: 50
    retry_on_timeout: true

database:
  host: "${DB_HOST}"
  port: ${DB_PORT}
  database: "${DB_NAME}"
  username: "${DB_USERNAME}"
  password: "${DB_PASSWORD}"
  connection_pool:
    min_size: 5
    max_size: 20
  timescaledb:
    chunk_time_interval: "1 day"
    compression_policy: "7 days"

strategies:
  option_grid:
    type: "OptionGridStrategy"
    capital_allocation: 0.70
    is_active: true
    mode: "accumulation"
    parameters:
      delta_range: [0.1, 0.2]
      strike_range: [-0.20, -0.08]
      iv_threshold: 0.6
    risk_limits:
      max_position_size: 1000
      max_daily_trades: 50

  branch_strategy:
    type: "BranchStrategy"
    capital_allocation: 0.25
    is_active: true
    parameters:
      signal_thresholds:
        structure_divergence: 0.65
        volatility_mismatch: 0.60
        gamma_liquidation_overlap: 0.70
      max_position_hold_time: 3600  # 1小时
    risk_limits:
      max_position_size: 500

risk_limits:
  max_delta: 0.5
  max_gamma: 0.05
  max_margin_usage: 0.70
  max_daily_loss: 0.05
  min_liquidity_score: 0.20

telegram:
  bot_token: "${TELEGRAM_BOT_TOKEN}"
  authorized_users: [123456789, 987654321]  # 替换为实际的Telegram用户ID

monitoring:
  prometheus_port: 9090
  grafana_port: 3000
  alert_webhook: "${ALERT_WEBHOOK_URL}"

logging:
  level: "INFO"
  format: "json"
  file: "logs/btc_option_grid_bot.log"
  max_size: "100MB"
  backup_count: 5

# Celery配置
celery:
  broker_url: "redis://${REDIS_HOST}:${REDIS_PORT}/1"
  result_backend: "redis://${REDIS_HOST}:${REDIS_PORT}/1"
  task_serializer: "json"
  accept_content: ["json"]
  result_serializer: "json"
  timezone: "UTC"
  enable_utc: true
  worker_pool: "threads"
  worker_concurrency: 8
  task_routes:
    "btc_option_grid_bot.tasks.gex_calculation":
      queue: "high_priority"
    "btc_option_grid_bot.tasks.data_compression":
      queue: "medium_priority"
    "btc_option_grid_bot.tasks.data_archival":
      queue: "low_priority"
  task_annotations:
    "*":
      rate_limit: "100/m"
      time_limit: 300
      soft_time_limit: 240
    "btc_option_grid_bot.tasks.gex_calculation":
      rate_limit: "200/m"
      time_limit: 30
      soft_time_limit: 25

# =============================================================================
# 因果分析引擎配置
# =============================================================================
causal_engine:
  analysis_interval: 30  # 分析间隔（秒）
  max_history_size: 1000  # 最大历史信号数量
  thresholds:
    signal_threshold: 0.65  # 信号强度阈值
    confidence_threshold: 0.7  # 置信度阈值
    adaptation_rate: 0.1  # 阈值自适应速率
    lookback_period: 14  # 回看周期（天）
    min_threshold: 0.5  # 最小阈值
    max_threshold: 0.9  # 最大阈值
  machine_learning:
    model_type: "random_forest"  # 模型类型：random_forest, xgboost
    feature_window: 60  # 特征窗口（分钟）
    retrain_interval: 24  # 重训练间隔（小时）
    min_samples: 100  # 最小训练样本数

# 结构分歧分析器配置
structure_analyzer:
  analysis_interval: 30  # 分析间隔（秒）
  lookback_window: 20  # 回看窗口大小
  correlation_threshold: -0.3  # 负相关阈值
  divergence_threshold: 0.6  # 分歧信号阈值
  min_volume_threshold: 1000000  # 最小成交量阈值（USD）
  min_oi_change_threshold: 0.05  # 最小OI变化阈值（5%）
  confidence_decay_factor: 0.95  # 置信度衰减因子
  signal_cooldown_minutes: 30  # 信号冷却时间（分钟）

# 波动率错配分析器配置
volatility_analyzer:
  analysis_interval: 60  # 分析间隔（秒）
  lookback_window: 20  # 回看窗口（数据点）
  vol_threshold: 0.05  # 波动率阈值（5%）
  min_arbitrage_profit: 0.001  # 最小套利利润（0.1%）
  signal_cooldown_minutes: 15  # 信号冷却时间（分钟）

  # 期限结构参数
  term_structure_threshold: 0.1  # 期限结构阈值
  inversion_threshold: 0.05  # 倒挂阈值

  # 偏度分析参数
  skew_threshold: 0.2  # 偏度阈值
  skew_window: 10  # 偏度计算窗口

  # IV vs RV分析参数
  iv_rv_threshold: 0.15  # IV-RV差异阈值
  rv_window: 24  # 实现波动率窗口（小时）

  # 套利参数
  transaction_cost: 0.0005  # 交易成本（0.05%）
  min_profit_margin: 0.002  # 最小利润边际（0.2%）

# Gamma清算分析器配置
gamma_analyzer:
  analysis_interval: 120  # 分析间隔（秒）
  gamma_threshold: 1000000  # Gamma阈值（美元）
  liquidation_threshold: 0.05  # 清算阈值（5%）
  price_impact_threshold: 0.02  # 价格影响阈值（2%）
  signal_cooldown_minutes: 30  # 信号冷却时间（分钟）

  # 风险参数
  max_position_size: 10000  # 最大持仓规模
  time_decay_factor: 0.3  # 时间衰减因子
  delta_risk_factor: 0.2  # Delta风险因子
  size_risk_factor: 0.1  # 规模风险因子

  # 价格影响模型
  gamma_impact_factor: 0.01  # Gamma影响因子
  notional_impact_factor: 0.005  # 名义价值影响因子
  squeeze_threshold: 1000  # 挤压阈值（美元）