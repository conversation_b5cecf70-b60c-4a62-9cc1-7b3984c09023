# 🚀 BTC期权网格交易机器人

一个基于期权工具的智能网格交易系统，通过创新的期权策略实现高资金效率的分批抄底和卖出。

## 🎯 核心特性

- **期权网格策略**: 通过Sell Put/Call替代传统现货网格，实现3-5倍资金效率提升
- **跨交易所因果分析**: 分析Binance vs Deribit的资金流向差异，识别结构性机会
- **微观结构精准出场**: 基于VWAP-LWAP价差、订单簿深度等信号实现精准止盈
- **智能风险控制**: 期权专用风险监控，包括Greeks风险、保证金管理、到期风险等
- **实时监控告警**: Telegram Bot + Web面板，支持远程监控和控制

## 🏗️ 系统架构

采用6层分层架构设计：

```
┌─────────────────────────────────────────────────────────────┐
│                    Infrastructure Layer                     │
│  EventBus | ConfigManager | AsyncLogger | CeleryWorkerPool │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     Execution Layer                        │
│  OrderManager | PositionManager | PnLTracker | ExerciseHandler │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                  Risk Management Layer                     │
│  RiskEngine | ExpiryManager | MarginCalculator | AlertManager │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                     Strategy Layer                         │
│  OptionGridStrategy | BranchStrategy | StrategyCoordinator │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                      Data Layer                            │
│  DataEngine | CausalEngine | MicrostructureSignals        │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   API Gateway Layer                        │
│  BinanceClient | DeribitClient | DataSynchronizer          │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 环境要求

- Python 3.9+
- Redis 7.0+
- PostgreSQL 15+ with TimescaleDB
- Docker & Docker Compose (可选)

### 安装

```bash
# 克隆项目
git clone <repository-url>
cd btc_option_grid_bot

# 安装依赖
poetry install

# 复制配置文件
cp config/config.example.yaml config/config.yaml

# 编辑配置文件，填入API密钥等信息
vim config/config.yaml
```

### 配置

在 `config/config.yaml` 中配置：

```yaml
exchanges:
  binance:
    api_key: "your_binance_api_key"
    api_secret: "your_binance_api_secret"
  deribit:
    api_key: "your_deribit_api_key"
    api_secret: "your_deribit_api_secret"

  ibkr:
    enabled: true
    # IBIT/IBKR 执行样例（示范用，真实值请依据账户权限与市场数据）
    account_id: "your_ibkr_account"
    market_hours_policy: "queue_until_open"  # open_only | prepost_allowed | queue_until_open
    pretrade_filters:
      max_spread_bps: 300
      min_oi: 200
      min_volume: 50
      daily_csp_notional_cap: 100000
    strategy:
      enable_csp: true
      enable_long_call_when_spot_ratio_below: 0.3
      long_call_target_delta: 0.65
      long_call_tenor_days: 60

telegram:
  bot_token: "your_telegram_bot_token"
  authorized_users: [*********]
```

### 运行

```bash
# 启动Redis和PostgreSQL (使用Docker)
docker-compose up -d redis postgres

# 初始化数据库
python scripts/init_database.py

# 启动Celery worker
celery -A btc_option_grid_bot.src.core.celery_manager worker --loglevel=info

# 启动主程序
python -m btc_option_grid_bot.src.main
```

## 📊 策略说明

### 主策略：期权网格

- **抄底模式**: 分批卖出OTM Put期权，Delta控制在0.1-0.2
- **卖出模式**: 针对现货持仓分批卖出OTM Call期权
- **震荡模式**: Short Strangle/Iron Condor收获时间价值

### 分支策略：因果信号驱动

- **入场**: 基于跨交易所因果信号识别方向性机会
- **出场**: 基于微观结构信号实现精准止盈

## 🛡️ 风险控制

- 总保证金占用率 < 70%
- Delta敞口控制在 ±0.5以内
- 单日最大亏损 < 5%
- 期权价差 > 5%时暂停交易
- 流动性不足时自动减仓

## 📈 监控和告警

### Telegram Bot命令

- `/status` - 系统状态概览
- `/positions` - 持仓信息
- `/risk` - 风险指标
- `/pnl` - 损益统计
- `/pause` - 暂停策略
- `/emergency` - 紧急停止

### Web监控面板

访问 `http://localhost:3000` 查看实时监控面板

## 🧪 测试

```bash
# 运行单元测试
pytest tests/unit/

# 运行集成测试
pytest tests/integration/

# 运行回测
python scripts/run_backtest.py --start-date 2024-01-01 --end-date 2024-12-31
```

## 📚 文档

- [需求文档](docs/requirements.md)
- [设计文档](docs/design.md)
- [实施指南](docs/implementation-guide.md)
- [API文档](docs/api.md)

## 🤝 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## ⚠️ 免责声明

本软件仅供教育和研究目的使用。使用本软件进行实际交易存在风险，可能导致资金损失。使用者应当充分了解相关风险，并在使用前进行充分的测试和验证。开发者不对使用本软件造成的任何损失承担责任。

## 📞 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: [<EMAIL>]
