"""
监控和运维工具

提供实时系统状态监控、关键指标可视化、日志查询和系统健康检查功能
"""

import asyncio
import json
import logging
import time
import weakref
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

import psutil
from aiohttp import WSMsgType, web

# 导入系统组件


@dataclass
class SystemMetrics:
    """系统指标"""

    timestamp: datetime

    # 系统资源
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    disk_usage: float = 0.0
    network_io: Dict[str, int] = field(default_factory=dict)

    # 应用指标
    active_orders: int = 0
    total_positions: int = 0
    current_pnl: float = 0.0
    risk_score: float = 0.0

    # 性能指标
    order_latency_ms: float = 0.0
    position_sync_latency_ms: float = 0.0
    event_processing_rate: float = 0.0

    # 错误统计
    error_count_1h: int = 0
    warning_count_1h: int = 0

    # 连接状态
    exchange_connections: Dict[str, bool] = field(default_factory=dict)
    database_connection: bool = True
    cache_connection: bool = True


@dataclass
class Alert:
    """告警"""

    id: str
    level: str  # info, warning, error, critical
    title: str
    message: str
    timestamp: datetime
    component: str
    resolved: bool = False
    resolved_at: Optional[datetime] = None


class MetricsCollector:
    """指标收集器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.system_components: Dict[str, Any] = {}
        self.metrics_history: List[SystemMetrics] = []
        self.max_history_size = 1440  # 24小时（每分钟一个点）

    def register_component(self, name: str, component: Any):
        """注册系统组件"""
        self.system_components[name] = weakref.ref(component)

    async def collect_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            timestamp = datetime.now(timezone.utc)

            # 系统资源指标
            cpu_usage = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage("/")
            network = psutil.net_io_counters()

            # 应用指标
            active_orders = await self._get_active_orders_count()
            total_positions = await self._get_total_positions_count()
            current_pnl = await self._get_current_pnl()
            risk_score = await self._get_risk_score()

            # 性能指标
            order_latency = await self._get_order_latency()
            position_latency = await self._get_position_sync_latency()
            event_rate = await self._get_event_processing_rate()

            # 错误统计
            error_count = await self._get_error_count_1h()
            warning_count = await self._get_warning_count_1h()

            # 连接状态
            exchange_connections = await self._check_exchange_connections()
            db_connection = await self._check_database_connection()
            cache_connection = await self._check_cache_connection()

            metrics = SystemMetrics(
                timestamp=timestamp,
                cpu_usage=cpu_usage,
                memory_usage=memory.percent,
                disk_usage=disk.percent,
                network_io={
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                },
                active_orders=active_orders,
                total_positions=total_positions,
                current_pnl=current_pnl,
                risk_score=risk_score,
                order_latency_ms=order_latency,
                position_sync_latency_ms=position_latency,
                event_processing_rate=event_rate,
                error_count_1h=error_count,
                warning_count_1h=warning_count,
                exchange_connections=exchange_connections,
                database_connection=db_connection,
                cache_connection=cache_connection,
            )

            # 保存到历史记录
            self.metrics_history.append(metrics)
            if len(self.metrics_history) > self.max_history_size:
                self.metrics_history.pop(0)

            return metrics

        except Exception as e:
            self.logger.error(f"Metrics collection failed: {e}")
            return SystemMetrics(timestamp=datetime.now(timezone.utc))

    async def _get_active_orders_count(self) -> int:
        """获取活跃订单数量"""
        try:
            order_manager_ref = self.system_components.get("order_manager")
            if order_manager_ref:
                order_manager = order_manager_ref()
                if order_manager:
                    stats = await order_manager.get_order_statistics()
                    return stats.get("active_orders", 0)
        except Exception:
            pass
        return 0

    async def _get_total_positions_count(self) -> int:
        """获取总仓位数量"""
        try:
            position_manager_ref = self.system_components.get("position_manager")
            if position_manager_ref:
                position_manager = position_manager_ref()
                if position_manager:
                    positions = await position_manager.get_all_positions()
                    return len(positions)
        except Exception:
            pass
        return 0

    async def _get_current_pnl(self) -> float:
        """获取当前PnL"""
        try:
            pnl_tracker_ref = self.system_components.get("pnl_tracker")
            if pnl_tracker_ref:
                pnl_tracker = pnl_tracker_ref()
                if pnl_tracker:
                    pnl_data = await pnl_tracker.get_current_pnl()
                    return pnl_data.get("total_pnl", 0.0)
        except Exception:
            pass
        return 0.0

    async def _get_risk_score(self) -> float:
        """获取风险评分"""
        try:
            risk_engine_ref = self.system_components.get("risk_engine")
            if risk_engine_ref:
                risk_engine = risk_engine_ref()
                if risk_engine:
                    risk_metrics = await risk_engine.get_current_risk_metrics()
                    return risk_metrics.get("overall_risk_score", 0.0)
        except Exception:
            pass
        return 0.0

    async def _get_order_latency(self) -> float:
        """获取订单延迟"""
        # 模拟实现，实际需要从订单管理器获取
        return 50.0

    async def _get_position_sync_latency(self) -> float:
        """获取仓位同步延迟"""
        # 模拟实现
        return 20.0

    async def _get_event_processing_rate(self) -> float:
        """获取事件处理速率"""
        # 模拟实现
        return 100.0

    async def _get_error_count_1h(self) -> int:
        """获取1小时内错误数量"""
        # 实际实现需要从日志系统获取
        return 0

    async def _get_warning_count_1h(self) -> int:
        """获取1小时内警告数量"""
        # 实际实现需要从日志系统获取
        return 0

    async def _check_exchange_connections(self) -> Dict[str, bool]:
        """检查交易所连接状态"""
        return {"deribit": True, "binance": True}

    async def _check_database_connection(self) -> bool:
        """检查数据库连接"""
        return True

    async def _check_cache_connection(self) -> bool:
        """检查缓存连接"""
        return True


class AlertManager:
    """告警管理器"""

    def __init__(self):
        self.alerts: List[Alert] = []
        self.alert_rules: Dict[str, Dict] = {}
        self.logger = logging.getLogger(__name__)

        # 默认告警规则
        self._setup_default_rules()

    def _setup_default_rules(self):
        """设置默认告警规则"""
        self.alert_rules = {
            "high_cpu": {
                "condition": lambda metrics: metrics.cpu_usage > 80,
                "level": "warning",
                "title": "High CPU Usage",
                "message": "CPU usage is above 80%",
            },
            "high_memory": {
                "condition": lambda metrics: metrics.memory_usage > 85,
                "level": "warning",
                "title": "High Memory Usage",
                "message": "Memory usage is above 85%",
            },
            "exchange_disconnected": {
                "condition": lambda metrics: not all(
                    metrics.exchange_connections.values()
                ),
                "level": "error",
                "title": "Exchange Connection Lost",
                "message": "One or more exchange connections are down",
            },
            "high_risk": {
                "condition": lambda metrics: metrics.risk_score > 0.8,
                "level": "critical",
                "title": "High Risk Score",
                "message": "Risk score is above 80%",
            },
            "high_order_latency": {
                "condition": lambda metrics: metrics.order_latency_ms > 5000,
                "level": "warning",
                "title": "High Order Latency",
                "message": "Order latency is above 5 seconds",
            },
        }

    def check_alerts(self, metrics: SystemMetrics) -> List[Alert]:
        """检查告警条件"""
        new_alerts = []

        for rule_name, rule in self.alert_rules.items():
            try:
                if rule["condition"](metrics):
                    alert = Alert(
                        id=f"{rule_name}_{int(time.time())}",
                        level=rule["level"],
                        title=rule["title"],
                        message=rule["message"],
                        timestamp=metrics.timestamp,
                        component="system",
                    )
                    new_alerts.append(alert)
                    self.alerts.append(alert)

            except Exception as e:
                self.logger.error(f"Alert rule {rule_name} failed: {e}")

        return new_alerts

    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        return [alert for alert in self.alerts if not alert.resolved]

    def resolve_alert(self, alert_id: str):
        """解决告警"""
        for alert in self.alerts:
            if alert.id == alert_id:
                alert.resolved = True
                alert.resolved_at = datetime.now(timezone.utc)
                break


class LogAnalyzer:
    """日志分析器"""

    def __init__(self, log_path: str = "logs"):
        self.log_path = Path(log_path)
        self.logger = logging.getLogger(__name__)

    async def search_logs(
        self,
        query: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        level: Optional[str] = None,
    ) -> List[Dict]:
        """搜索日志"""
        try:
            results = []

            # 模拟日志搜索
            # 实际实现需要解析日志文件或查询日志数据库

            sample_logs = [
                {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "level": "INFO",
                    "component": "OrderManager",
                    "message": "Order submitted successfully",
                    "details": {"order_id": "order_123", "symbol": "BTC-USDT"},
                },
                {
                    "timestamp": (
                        datetime.now(timezone.utc) - timedelta(minutes=5)
                    ).isoformat(),
                    "level": "WARNING",
                    "component": "RiskEngine",
                    "message": "Risk threshold exceeded",
                    "details": {"risk_score": 0.85, "threshold": 0.8},
                },
            ]

            # 过滤日志
            for log in sample_logs:
                if query.lower() in log["message"].lower():
                    if level is None or log["level"] == level.upper():
                        results.append(log)

            return results

        except Exception as e:
            self.logger.error(f"Log search failed: {e}")
            return []

    async def get_error_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取错误摘要"""
        try:
            # 模拟错误统计
            return {
                "total_errors": 5,
                "total_warnings": 12,
                "error_by_component": {
                    "OrderManager": 2,
                    "RiskEngine": 2,
                    "PositionManager": 1,
                },
                "error_trends": [
                    {"hour": i, "errors": max(0, 5 - i)} for i in range(hours)
                ],
            }

        except Exception as e:
            self.logger.error(f"Error summary failed: {e}")
            return {}


class HealthChecker:
    """健康检查器"""

    def __init__(self):
        self.system_components: Dict[str, Any] = {}
        self.logger = logging.getLogger(__name__)

    def register_component(self, name: str, component: Any):
        """注册组件"""
        self.system_components[name] = weakref.ref(component)

    async def check_system_health(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        health_status = {
            "overall_status": "healthy",
            "components": {},
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        unhealthy_count = 0

        for name, component_ref in self.system_components.items():
            try:
                component = component_ref()
                if component and hasattr(component, "health_check"):
                    result = await component.health_check()
                    health_status["components"][name] = {
                        "status": result.status.value,
                        "message": result.message,
                    }

                    if result.status.value != "healthy":
                        unhealthy_count += 1
                else:
                    health_status["components"][name] = {
                        "status": "unknown",
                        "message": "Component not available",
                    }
                    unhealthy_count += 1

            except Exception as e:
                health_status["components"][name] = {
                    "status": "error",
                    "message": str(e),
                }
                unhealthy_count += 1

        # 确定整体状态
        total_components = len(self.system_components)
        if unhealthy_count == 0:
            health_status["overall_status"] = "healthy"
        elif unhealthy_count < total_components / 2:
            health_status["overall_status"] = "degraded"
        else:
            health_status["overall_status"] = "unhealthy"

        return health_status

    async def auto_heal(self) -> Dict[str, Any]:
        """自动修复"""
        heal_results = {}

        for name, component_ref in self.system_components.items():
            try:
                component = component_ref()
                if component and hasattr(component, "health_check"):
                    health_result = await component.health_check()

                    if health_result.status.value != "healthy":
                        # 尝试重启组件
                        if hasattr(component, "restart"):
                            await component.restart()
                            heal_results[name] = "restarted"
                        else:
                            heal_results[name] = "no_auto_heal"
                    else:
                        heal_results[name] = "healthy"

            except Exception as e:
                heal_results[name] = f"heal_failed: {e}"

        return heal_results


class MonitoringDashboard:
    """监控面板"""

    def __init__(self, host: str = "localhost", port: int = 8080):
        self.host = host
        self.port = port
        self.app = web.Application()
        self.websockets: Set[web.WebSocketResponse] = set()

        # 核心组件
        self.metrics_collector = MetricsCollector()
        self.alert_manager = AlertManager()
        self.log_analyzer = LogAnalyzer()
        self.health_checker = HealthChecker()

        # 监控任务
        self._monitor_task: Optional[asyncio.Task] = None

        # 设置路由
        self._setup_routes()

        self.logger = logging.getLogger(__name__)

    def _setup_routes(self):
        """设置路由"""
        # API路由
        self.app.router.add_get("/api/metrics", self.get_metrics)
        self.app.router.add_get("/api/alerts", self.get_alerts)
        self.app.router.add_post("/api/alerts/{alert_id}/resolve", self.resolve_alert)
        self.app.router.add_get("/api/health", self.get_health)
        self.app.router.add_post("/api/heal", self.auto_heal)
        self.app.router.add_get("/api/logs", self.search_logs)

        # WebSocket
        self.app.router.add_get("/ws", self.websocket_handler)

        # 静态文件（前端）
        self.app.router.add_static("/", path="frontend/build", name="static")

    def register_component(self, name: str, component: Any):
        """注册系统组件"""
        self.metrics_collector.register_component(name, component)
        self.health_checker.register_component(name, component)

    async def start(self):
        """启动监控面板"""
        try:
            # 启动监控任务
            self._monitor_task = asyncio.create_task(self._monitoring_loop())

            # 启动Web服务器
            runner = web.AppRunner(self.app)
            await runner.setup()
            site = web.TCPSite(runner, self.host, self.port)
            await site.start()

            self.logger.info(
                f"Monitoring dashboard started at http://{self.host}:{self.port}"
            )

        except Exception as e:
            self.logger.error(f"Failed to start monitoring dashboard: {e}")
            raise

    async def stop(self):
        """停止监控面板"""
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass

    async def _monitoring_loop(self):
        """监控循环"""
        while True:
            try:
                # 收集指标
                metrics = await self.metrics_collector.collect_metrics()

                # 检查告警
                new_alerts = self.alert_manager.check_alerts(metrics)

                # 广播更新
                await self._broadcast_update(
                    {"type": "metrics_update", "data": self._metrics_to_dict(metrics)}
                )

                if new_alerts:
                    await self._broadcast_update(
                        {
                            "type": "new_alerts",
                            "data": [
                                self._alert_to_dict(alert) for alert in new_alerts
                            ],
                        }
                    )

                await asyncio.sleep(60)  # 每分钟更新一次

            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {e}")
                await asyncio.sleep(60)

    async def _broadcast_update(self, message: Dict):
        """广播更新"""
        if self.websockets:
            message_str = json.dumps(message)
            disconnected = set()

            for ws in self.websockets:
                try:
                    await ws.send_str(message_str)
                except Exception:
                    disconnected.add(ws)

            # 清理断开的连接
            self.websockets -= disconnected

    # API处理器

    async def get_metrics(self, request):
        """获取指标"""
        try:
            metrics = await self.metrics_collector.collect_metrics()
            return web.json_response(self._metrics_to_dict(metrics))
        except Exception as e:
            return web.json_response({"error": str(e)}, status=500)

    async def get_alerts(self, request):
        """获取告警"""
        try:
            alerts = self.alert_manager.get_active_alerts()
            return web.json_response([self._alert_to_dict(alert) for alert in alerts])
        except Exception as e:
            return web.json_response({"error": str(e)}, status=500)

    async def resolve_alert(self, request):
        """解决告警"""
        try:
            alert_id = request.match_info["alert_id"]
            self.alert_manager.resolve_alert(alert_id)
            return web.json_response({"status": "resolved"})
        except Exception as e:
            return web.json_response({"error": str(e)}, status=500)

    async def get_health(self, request):
        """获取健康状态"""
        try:
            health = await self.health_checker.check_system_health()
            return web.json_response(health)
        except Exception as e:
            return web.json_response({"error": str(e)}, status=500)

    async def auto_heal(self, request):
        """自动修复"""
        try:
            results = await self.health_checker.auto_heal()
            return web.json_response(results)
        except Exception as e:
            return web.json_response({"error": str(e)}, status=500)

    async def search_logs(self, request):
        """搜索日志"""
        try:
            query = request.query.get("q", "")
            level = request.query.get("level")

            logs = await self.log_analyzer.search_logs(query, level=level)
            return web.json_response(logs)
        except Exception as e:
            return web.json_response({"error": str(e)}, status=500)

    async def websocket_handler(self, request):
        """WebSocket处理器"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)

        self.websockets.add(ws)

        try:
            async for msg in ws:
                if msg.type == WSMsgType.TEXT:
                    # 处理客户端消息
                    pass
                elif msg.type == WSMsgType.ERROR:
                    self.logger.error(f"WebSocket error: {ws.exception()}")
        except Exception as e:
            self.logger.error(f"WebSocket handler error: {e}")
        finally:
            self.websockets.discard(ws)

        return ws

    def _metrics_to_dict(self, metrics: SystemMetrics) -> Dict[str, Any]:
        """指标转字典"""
        return {
            "timestamp": metrics.timestamp.isoformat(),
            "cpu_usage": metrics.cpu_usage,
            "memory_usage": metrics.memory_usage,
            "disk_usage": metrics.disk_usage,
            "network_io": metrics.network_io,
            "active_orders": metrics.active_orders,
            "total_positions": metrics.total_positions,
            "current_pnl": metrics.current_pnl,
            "risk_score": metrics.risk_score,
            "order_latency_ms": metrics.order_latency_ms,
            "position_sync_latency_ms": metrics.position_sync_latency_ms,
            "event_processing_rate": metrics.event_processing_rate,
            "error_count_1h": metrics.error_count_1h,
            "warning_count_1h": metrics.warning_count_1h,
            "exchange_connections": metrics.exchange_connections,
            "database_connection": metrics.database_connection,
            "cache_connection": metrics.cache_connection,
        }

    def _alert_to_dict(self, alert: Alert) -> Dict[str, Any]:
        """告警转字典"""
        return {
            "id": alert.id,
            "level": alert.level,
            "title": alert.title,
            "message": alert.message,
            "timestamp": alert.timestamp.isoformat(),
            "component": alert.component,
            "resolved": alert.resolved,
            "resolved_at": alert.resolved_at.isoformat() if alert.resolved_at else None,
        }


# 使用示例
async def main():
    """主函数"""
    dashboard = MonitoringDashboard(host="0.0.0.0", port=8080)

    # 注册系统组件（示例）
    # dashboard.register_component('order_manager', order_manager)
    # dashboard.register_component('position_manager', position_manager)

    try:
        await dashboard.start()

        # 保持运行
        while True:
            await asyncio.sleep(1)

    except KeyboardInterrupt:
        print("Shutting down...")
    finally:
        await dashboard.stop()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
