.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 自定义样式 */
.monitoring-dashboard {
  min-height: 100vh;
  background-color: #f0f2f5;
}

.dashboard-header {
  background: #fff;
  padding: 0 24px;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.status-card {
  margin-bottom: 16px;
}

.status-card .ant-card-body {
  padding: 16px;
}

.metric-card {
  text-align: center;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
  background: #fff;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}

.metric-label {
  color: #666;
  font-size: 12px;
}

.alert-item {
  margin-bottom: 8px;
}

.position-table .ant-table-tbody > tr:hover > td {
  background: #f5f5f5;
}

.pnl-positive {
  color: #52c41a;
  font-weight: 600;
}

.pnl-negative {
  color: #ff4d4f;
  font-weight: 600;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.connection-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #52c41a;
}

.connection-indicator.disconnected {
  background-color: #ff4d4f;
}

.strategy-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-container {
  padding: 16px;
}

.responsive-chart {
  width: 100%;
  height: 300px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 0 16px;
  }
  
  .dashboard-header h3 {
    font-size: 18px;
  }
  
  .ant-col {
    margin-bottom: 16px;
  }
  
  .responsive-chart {
    height: 250px;
  }
  
  .metric-card {
    padding: 12px;
  }
  
  .metric-value {
    font-size: 20px;
  }
}

@media (max-width: 576px) {
  .dashboard-header {
    padding: 0 12px;
  }
  
  .dashboard-header h3 {
    font-size: 16px;
  }
  
  .responsive-chart {
    height: 200px;
  }
  
  .ant-statistic-title {
    font-size: 12px;
  }
  
  .ant-statistic-content {
    font-size: 18px;
  }
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
