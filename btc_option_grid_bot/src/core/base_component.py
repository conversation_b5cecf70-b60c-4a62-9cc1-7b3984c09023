#!/usr/bin/env python3
"""
基础组件类 - BaseComponent

定义系统中所有组件的统一生命周期接口和基础功能，包括：
- 异步初始化、启动、停止流程
- 组件状态管理和监控
- 统一错误处理机制
- 日志记录和指标收集
- 健康检查和自愈机制

设计要点：
- 提供标准的组件生命周期管理
- 支持优雅启动和关闭
- 集成异步日志和错误处理
- 支持组件状态监控和健康检查
"""

import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import TYPE_CHECKING, Any, Dict, List, Optional

if TYPE_CHECKING:
    from ..utils.async_logger import AsyncLogger
else:
    try:
        from ..utils.async_logger import AsyncLogger
    except ImportError:
        # For testing and standalone usage
        import os
        import sys

        sys.path.append(os.path.join(os.path.dirname(__file__), ".."))
        from utils.async_logger import AsyncLogger


class ComponentState(Enum):
    """组件状态枚举"""

    UNINITIALIZED = "uninitialized"  # 未初始化
    INITIALIZING = "initializing"  # 初始化中
    INITIALIZED = "initialized"  # 已初始化
    STARTING = "starting"  # 启动中
    RUNNING = "running"  # 运行中
    STOPPING = "stopping"  # 停止中
    STOPPED = "stopped"  # 已停止
    ERROR = "error"  # 错误状态
    FAILED = "failed"  # 失败状态


class HealthStatus(Enum):
    """健康状态枚举"""

    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class ComponentMetrics:
    """组件指标数据类"""

    start_time: Optional[datetime] = None
    last_health_check: Optional[datetime] = None
    uptime_seconds: float = 0.0
    error_count: int = 0
    warning_count: int = 0
    restart_count: int = 0
    custom_metrics: Dict[str, Any] = field(default_factory=dict)


@dataclass
class HealthCheckResult:
    """健康检查结果"""

    status: HealthStatus
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


class BaseComponent(ABC):
    """
    基础组件抽象类

    所有系统组件的基类，提供统一的生命周期管理、状态监控、
    错误处理和日志记录功能。

    子类需要实现：
    - _initialize_impl(): 具体初始化逻辑
    - _start_impl(): 具体启动逻辑
    - _stop_impl(): 具体停止逻辑
    - _health_check_impl(): 具体健康检查逻辑
    """

    def __init__(self, component_name: str, config: Optional[Dict[str, Any]] = None):
        self.component_name = component_name
        self.config = config or {}
        self.logger: Optional["AsyncLogger"] = None

        # 组件状态管理
        self._state = ComponentState.UNINITIALIZED
        self._state_lock = asyncio.Lock()

        # 指标收集
        self.metrics = ComponentMetrics()

        # 错误处理
        self._error_handlers: Dict[type, callable] = {}
        self._last_error: Optional[Exception] = None

        # 健康检查配置
        self._health_check_interval = self.config.get(
            "health_check_interval", 0
        )  # 默认禁用自动健康检查
        self._health_check_task: Optional[asyncio.Task] = None
        self._last_health_status = HealthStatus.UNKNOWN

        # 生命周期钩子
        self._lifecycle_hooks: Dict[str, List[callable]] = {
            "before_initialize": [],
            "after_initialize": [],
            "before_start": [],
            "after_start": [],
            "before_stop": [],
            "after_stop": [],
        }

    @property
    def state(self) -> ComponentState:
        """获取当前组件状态"""
        return self._state

    @property
    def is_running(self) -> bool:
        """检查组件是否正在运行"""
        return self._state == ComponentState.RUNNING

    @property
    def is_healthy(self) -> bool:
        """检查组件是否健康"""
        return self._last_health_status == HealthStatus.HEALTHY

    def set_logger(self, logger: "AsyncLogger"):
        """设置日志记录器"""
        self.logger = logger

    def add_error_handler(self, exception_type: type, handler: callable):
        """添加错误处理器"""
        self._error_handlers[exception_type] = handler

    def add_lifecycle_hook(self, event: str, hook: callable):
        """添加生命周期钩子"""
        if event in self._lifecycle_hooks:
            self._lifecycle_hooks[event].append(hook)

    async def _execute_hooks(self, event: str):
        """执行生命周期钩子"""
        hooks = self._lifecycle_hooks.get(event, [])
        for hook in hooks:
            try:
                if asyncio.iscoroutinefunction(hook):
                    await hook(self)
                else:
                    hook(self)
            except Exception as e:
                if self.logger:
                    await self.logger.warning(
                        f"Hook execution failed for {event}: {str(e)}"
                    )

    async def _set_state(self, new_state: ComponentState):
        """安全地设置组件状态"""
        old_state = self._state
        self._state = new_state

        if self.logger:
            try:
                await self.logger.info(
                    f"{self.component_name} state changed: {old_state.value} -> {new_state.value}"
                )
            except Exception:
                # Fallback to print if logger fails
                print(
                    f"{self.component_name} state changed: {old_state.value} -> {new_state.value}"
                )

    async def _handle_error(self, error: Exception, context: str = ""):
        """统一错误处理"""
        self._last_error = error
        self.metrics.error_count += 1

        # 记录错误日志
        if self.logger:
            try:
                await self.logger.error(
                    f"{self.component_name} error in {context}: {str(error)}"
                )
            except Exception:
                # Fallback to print if logger fails
                print(f"{self.component_name} error in {context}: {str(error)}")

        # 执行自定义错误处理器
        error_type = type(error)
        if error_type in self._error_handlers:
            try:
                handler = self._error_handlers[error_type]
                if asyncio.iscoroutinefunction(handler):
                    await handler(error, context)
                else:
                    handler(error, context)
            except Exception as handler_error:
                if self.logger:
                    await self.logger.error(
                        f"Error handler failed: {str(handler_error)}"
                    )

    async def initialize(self) -> bool:
        """
        初始化组件

        Returns:
            bool: 初始化是否成功
        """
        async with self._state_lock:
            if self._state != ComponentState.UNINITIALIZED:
                if self.logger:
                    await self.logger.warning(
                        f"{self.component_name} already initialized, current state: {self._state.value}"
                    )
                return self._state == ComponentState.INITIALIZED

            await self._set_state(ComponentState.INITIALIZING)

            try:
                # 执行初始化前钩子
                await self._execute_hooks("before_initialize")

                # 执行具体初始化逻辑
                success = await self._initialize_impl()

                if success:
                    await self._set_state(ComponentState.INITIALIZED)
                    # 执行初始化后钩子
                    await self._execute_hooks("after_initialize")

                    if self.logger:
                        await self.logger.info(
                            f"{self.component_name} initialized successfully"
                        )
                    return True
                else:
                    await self._set_state(ComponentState.FAILED)
                    if self.logger:
                        await self.logger.error(
                            f"{self.component_name} initialization failed"
                        )
                    return False

            except Exception as e:
                await self._handle_error(e, "initialize")
                await self._set_state(ComponentState.ERROR)
                return False

    async def start(self) -> bool:
        """
        启动组件

        Returns:
            bool: 启动是否成功
        """
        async with self._state_lock:
            if self._state not in [ComponentState.INITIALIZED, ComponentState.STOPPED]:
                if self.logger:
                    await self.logger.warning(
                        f"{self.component_name} cannot start from state: {self._state.value}"
                    )
                return False

            await self._set_state(ComponentState.STARTING)

            try:
                # 执行启动前钩子
                await self._execute_hooks("before_start")

                # 执行具体启动逻辑
                success = await self._start_impl()

                if success:
                    await self._set_state(ComponentState.RUNNING)
                    self.metrics.start_time = datetime.now(timezone.utc)
                    self.metrics.restart_count += 1

                    # 启动健康检查任务
                    await self._start_health_check()

                    # 执行启动后钩子
                    await self._execute_hooks("after_start")

                    if self.logger:
                        await self.logger.info(
                            f"{self.component_name} started successfully"
                        )
                    return True
                else:
                    await self._set_state(ComponentState.FAILED)
                    if self.logger:
                        await self.logger.error(f"{self.component_name} start failed")
                    return False

            except Exception as e:
                await self._handle_error(e, "start")
                await self._set_state(ComponentState.ERROR)
                return False

    async def stop(self) -> bool:
        """
        停止组件

        Returns:
            bool: 停止是否成功
        """
        async with self._state_lock:
            if self._state not in [ComponentState.RUNNING, ComponentState.ERROR]:
                if self.logger:
                    await self.logger.warning(
                        f"{self.component_name} cannot stop from state: {self._state.value}"
                    )
                return self._state == ComponentState.STOPPED

            await self._set_state(ComponentState.STOPPING)

            try:
                # 执行停止前钩子
                await self._execute_hooks("before_stop")

                # 停止健康检查任务
                await self._stop_health_check()

                # 执行具体停止逻辑
                success = await self._stop_impl()

                if success:
                    await self._set_state(ComponentState.STOPPED)

                    # 更新运行时间指标
                    if self.metrics.start_time:
                        self.metrics.uptime_seconds = (
                            datetime.now(timezone.utc) - self.metrics.start_time
                        ).total_seconds()

                    # 执行停止后钩子
                    await self._execute_hooks("after_stop")

                    if self.logger:
                        await self.logger.info(
                            f"{self.component_name} stopped successfully"
                        )
                    return True
                else:
                    await self._set_state(ComponentState.ERROR)
                    if self.logger:
                        await self.logger.error(f"{self.component_name} stop failed")
                    return False

            except Exception as e:
                await self._handle_error(e, "stop")
                await self._set_state(ComponentState.ERROR)
                return False

    async def restart(self) -> bool:
        """
        重启组件

        Returns:
            bool: 重启是否成功
        """
        if self.logger:
            await self.logger.info(f"Restarting {self.component_name}")

        # 先停止
        stop_success = await self.stop()
        if not stop_success:
            return False

        # 等待一小段时间
        await asyncio.sleep(1)

        # 再启动
        return await self.start()

    async def health_check(self) -> HealthCheckResult:
        """
        执行健康检查

        Returns:
            HealthCheckResult: 健康检查结果
        """
        try:
            # 基础状态检查
            if self._state != ComponentState.RUNNING:
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message=f"Component not running, current state: {self._state.value}",
                )

            # 执行具体健康检查逻辑
            result = await self._health_check_impl()

            # 更新健康状态和指标
            self._last_health_status = result.status
            self.metrics.last_health_check = datetime.now(timezone.utc)

            return result

        except Exception as e:
            await self._handle_error(e, "health_check")
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {str(e)}"
            )

    async def get_metrics(self) -> Dict[str, Any]:
        """
        获取组件指标

        Returns:
            Dict[str, Any]: 组件指标数据
        """
        current_time = datetime.now(timezone.utc)

        # 计算当前运行时间
        current_uptime = 0.0
        if self.metrics.start_time and self._state == ComponentState.RUNNING:
            current_uptime = (current_time - self.metrics.start_time).total_seconds()

        return {
            "component_name": self.component_name,
            "state": self._state.value,
            "health_status": self._last_health_status.value,
            "start_time": self.metrics.start_time.isoformat()
            if self.metrics.start_time
            else None,
            "last_health_check": self.metrics.last_health_check.isoformat()
            if self.metrics.last_health_check
            else None,
            "uptime_seconds": current_uptime,
            "total_uptime_seconds": self.metrics.uptime_seconds,
            "error_count": self.metrics.error_count,
            "warning_count": self.metrics.warning_count,
            "restart_count": self.metrics.restart_count,
            "last_error": str(self._last_error) if self._last_error else None,
            "custom_metrics": self.metrics.custom_metrics.copy(),
        }

    async def _start_health_check(self):
        """启动健康检查任务"""
        if self._health_check_interval > 0:
            self._health_check_task = asyncio.create_task(self._health_check_loop())

    async def _stop_health_check(self):
        """停止健康检查任务"""
        if self._health_check_task and not self._health_check_task.done():
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass

    async def _health_check_loop(self):
        """健康检查循环"""
        while self._state == ComponentState.RUNNING:
            try:
                await self.health_check()
                await asyncio.sleep(self._health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                await self._handle_error(e, "health_check_loop")
                await asyncio.sleep(self._health_check_interval)

    # 抽象方法 - 子类必须实现

    @abstractmethod
    async def _initialize_impl(self) -> bool:
        """
        具体初始化实现

        子类需要实现具体的初始化逻辑

        Returns:
            bool: 初始化是否成功
        """
        pass

    @abstractmethod
    async def _start_impl(self) -> bool:
        """
        具体启动实现

        子类需要实现具体的启动逻辑

        Returns:
            bool: 启动是否成功
        """
        pass

    @abstractmethod
    async def _stop_impl(self) -> bool:
        """
        具体停止实现

        子类需要实现具体的停止逻辑

        Returns:
            bool: 停止是否成功
        """
        pass

    @abstractmethod
    async def _health_check_impl(self) -> HealthCheckResult:
        """
        具体健康检查实现

        子类需要实现具体的健康检查逻辑

        Returns:
            HealthCheckResult: 健康检查结果
        """
        pass


class SimpleComponent(BaseComponent):
    """
    简单组件实现示例

    提供基础的组件实现，可用于测试或简单场景
    """

    def __init__(self, component_name: str, config: Optional[Dict[str, Any]] = None):
        super().__init__(component_name, config)
        self._is_initialized = False
        self._is_started = False

    async def _initialize_impl(self) -> bool:
        """简单初始化实现"""
        await asyncio.sleep(0.1)  # 模拟初始化时间
        self._is_initialized = True
        return True

    async def _start_impl(self) -> bool:
        """简单启动实现"""
        if not self._is_initialized:
            return False
        await asyncio.sleep(0.1)  # 模拟启动时间
        self._is_started = True
        return True

    async def _stop_impl(self) -> bool:
        """简单停止实现"""
        await asyncio.sleep(0.1)  # 模拟停止时间
        self._is_started = False
        return True

    async def _health_check_impl(self) -> HealthCheckResult:
        """简单健康检查实现"""
        if self._is_started:
            return HealthCheckResult(
                status=HealthStatus.HEALTHY, message="Component is running normally"
            )
        else:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message="Component is not started"
            )
