#!/usr/bin/env python3
"""
异步事件总线 - EventBus

实现高性能的发布-订阅模式事件系统，支持：
- 异步事件发布和订阅
- 事件类型定义和路由机制
- 事件持久化和重放功能
- 事件监控和性能指标收集
- 并发安全的事件处理

设计要点：
- 基于asyncio的高性能异步处理
- 支持事件过滤和条件订阅
- 提供事件历史记录和重放
- 集成性能监控和错误处理
- 支持事件优先级和批量处理
"""

import asyncio
import time
import uuid
from abc import ABC
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Union

from .base_component import BaseComponent, HealthCheckResult, HealthStatus

try:
    pass  # Logger import handled elsewhere
except ImportError:
    # For testing and standalone usage
    import os
    import sys

    sys.path.append(os.path.join(os.path.dirname(__file__), ".."))


class EventPriority(Enum):
    """事件优先级枚举"""

    LOW = 0
    NORMAL = 1
    HIGH = 2
    CRITICAL = 3


class EventStatus(Enum):
    """事件状态枚举"""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"


@dataclass
class BaseEvent(ABC):
    """基础事件类"""

    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str = field(default="")
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    priority: EventPriority = field(default=EventPriority.NORMAL)
    source: str = field(default="")
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        if not self.event_type:
            self.event_type = self.__class__.__name__


@dataclass
class DataUpdateEvent(BaseEvent):
    """数据更新事件"""

    data: Dict[str, Any] = field(default_factory=dict)
    data_type: str = ""
    exchange: str = ""


@dataclass
class SignalEvent(BaseEvent):
    """信号事件"""

    signal_data: Dict[str, Any] = field(default_factory=dict)
    signal_type: str = ""
    confidence: float = 0.0


@dataclass
class SystemEvent(BaseEvent):
    """系统事件"""

    component: str = ""
    action: str = ""
    status: str = ""
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ErrorEvent(BaseEvent):
    """错误事件"""

    error_type: str = ""
    error_message: str = ""
    component: str = ""
    stack_trace: str = ""
    context: Dict[str, Any] = field(default_factory=dict)


@dataclass
class EventSubscription:
    """事件订阅信息"""

    subscriber_id: str
    event_types: Set[str]
    callback: Callable
    filter_func: Optional[Callable] = None
    max_retries: int = 3
    retry_delay: float = 1.0
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    is_active: bool = True


@dataclass
class EventMetrics:
    """事件指标"""

    total_published: int = 0
    total_processed: int = 0
    total_failed: int = 0
    total_retries: int = 0
    avg_processing_time: float = 0.0
    events_per_second: float = 0.0
    last_event_time: Optional[datetime] = None
    event_type_counts: Dict[str, int] = field(default_factory=dict)


@dataclass
class EventRecord:
    """事件记录"""

    event: BaseEvent
    status: EventStatus
    processing_time: float = 0.0
    retry_count: int = 0
    error_message: str = ""
    processed_at: Optional[datetime] = None


class EventBus(BaseComponent):
    """
    异步事件总线

    提供高性能的发布-订阅模式事件系统，支持：
    - 异步事件发布和处理
    - 灵活的订阅和过滤机制
    - 事件持久化和重放
    - 性能监控和错误处理
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("EventBus", config)

        # 订阅管理
        self._subscribers: Dict[str, EventSubscription] = {}
        self._event_type_subscribers: Dict[str, Set[str]] = defaultdict(set)

        # 事件队列和处理
        self._event_queue: asyncio.Queue = asyncio.Queue(
            maxsize=self.config.get("max_queue_size", 10000)
        )
        self._processing_tasks: Set[asyncio.Task] = set()
        self._worker_count = self.config.get("worker_count", 4)

        # 事件历史和重放
        self._event_history: deque = deque(
            maxlen=self.config.get("max_history_size", 1000)
        )
        self._enable_persistence = self.config.get("enable_persistence", True)

        # 性能监控 - 使用单独的事件指标
        self._event_metrics = EventMetrics()
        self._metrics_lock = asyncio.Lock()

        # 错误处理
        self._error_handlers: Dict[str, Callable] = {}
        self._dead_letter_queue: deque = deque(
            maxlen=self.config.get("max_dead_letter_size", 100)
        )

        # 批量处理配置
        self._batch_size = self.config.get("batch_size", 10)
        self._batch_timeout = self.config.get("batch_timeout", 1.0)

    async def _initialize_impl(self) -> bool:
        """初始化事件总线"""
        try:
            # 初始化事件队列
            if self._event_queue.qsize() > 0:
                # 清空队列
                while not self._event_queue.empty():
                    try:
                        self._event_queue.get_nowait()
                    except asyncio.QueueEmpty:
                        break

            # 重置指标
            self._event_metrics = EventMetrics()

            if self.logger:
                await self.logger.info("EventBus initialized successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"EventBus initialization failed: {str(e)}")
            return False

    async def _start_impl(self) -> bool:
        """启动事件总线"""
        try:
            # 启动事件处理工作协程
            for i in range(self._worker_count):
                task = asyncio.create_task(self._event_worker(f"worker-{i}"))
                self._processing_tasks.add(task)

            if self.logger:
                await self.logger.info(
                    f"EventBus started with {self._worker_count} workers"
                )

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"EventBus start failed: {str(e)}")
            return False

    async def _stop_impl(self) -> bool:
        """停止事件总线"""
        try:
            # 停止所有工作协程
            for task in self._processing_tasks:
                task.cancel()

            # 等待所有任务完成
            if self._processing_tasks:
                await asyncio.gather(*self._processing_tasks, return_exceptions=True)

            self._processing_tasks.clear()

            if self.logger:
                await self.logger.info("EventBus stopped successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"EventBus stop failed: {str(e)}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            # 检查队列状态
            queue_size = self._event_queue.qsize()
            max_queue_size = self.config.get("max_queue_size", 10000)

            # 检查工作协程状态
            active_workers = sum(
                1 for task in self._processing_tasks if not task.done()
            )

            # 计算健康状态
            if queue_size > max_queue_size * 0.9:
                status = HealthStatus.DEGRADED
                message = f"Event queue nearly full: {queue_size}/{max_queue_size}"
            elif active_workers < self._worker_count:
                status = HealthStatus.DEGRADED
                message = (
                    f"Some workers inactive: {active_workers}/{self._worker_count}"
                )
            else:
                status = HealthStatus.HEALTHY
                message = "EventBus operating normally"

            return HealthCheckResult(
                status=status,
                message=message,
                details={
                    "queue_size": queue_size,
                    "max_queue_size": max_queue_size,
                    "active_workers": active_workers,
                    "total_workers": self._worker_count,
                    "total_subscribers": len(self._subscribers),
                    "events_processed": self._event_metrics.total_processed,
                    "events_failed": self._event_metrics.total_failed,
                },
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {str(e)}"
            )

    async def subscribe(
        self,
        event_types: Union[str, List[str]],
        callback: Callable,
        subscriber_id: Optional[str] = None,
        filter_func: Optional[Callable] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ) -> str:
        """
        订阅事件

        Args:
            event_types: 事件类型或类型列表
            callback: 回调函数
            subscriber_id: 订阅者ID（可选）
            filter_func: 事件过滤函数（可选）
            max_retries: 最大重试次数
            retry_delay: 重试延迟

        Returns:
            str: 订阅ID
        """
        if subscriber_id is None:
            subscriber_id = str(uuid.uuid4())

        # 标准化事件类型
        if isinstance(event_types, str):
            event_types = [event_types]

        event_types_set = set(event_types)

        # 创建订阅
        subscription = EventSubscription(
            subscriber_id=subscriber_id,
            event_types=event_types_set,
            callback=callback,
            filter_func=filter_func,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )

        # 存储订阅
        self._subscribers[subscriber_id] = subscription

        # 更新事件类型索引
        for event_type in event_types_set:
            self._event_type_subscribers[event_type].add(subscriber_id)

        if self.logger:
            await self.logger.info(
                f"Subscription created: {subscriber_id} for {event_types}"
            )

        return subscriber_id

    async def unsubscribe(self, subscriber_id: str) -> bool:
        """
        取消订阅

        Args:
            subscriber_id: 订阅者ID

        Returns:
            bool: 是否成功取消订阅
        """
        if subscriber_id not in self._subscribers:
            return False

        subscription = self._subscribers[subscriber_id]

        # 从事件类型索引中移除
        for event_type in subscription.event_types:
            self._event_type_subscribers[event_type].discard(subscriber_id)

            # 如果该事件类型没有订阅者了，删除索引
            if not self._event_type_subscribers[event_type]:
                del self._event_type_subscribers[event_type]

        # 删除订阅
        del self._subscribers[subscriber_id]

        if self.logger:
            await self.logger.info(f"Subscription removed: {subscriber_id}")

        return True

    async def publish(self, event: BaseEvent) -> bool:
        """
        发布事件

        Args:
            event: 事件对象

        Returns:
            bool: 是否成功发布
        """
        try:
            # 验证事件
            if not isinstance(event, BaseEvent):
                raise ValueError("Event must be an instance of BaseEvent")

            # 设置事件元数据
            if not event.source:
                event.source = self.component_name

            # 添加到队列
            await self._event_queue.put(event)

            # 更新指标
            async with self._metrics_lock:
                self._event_metrics.total_published += 1
                self._event_metrics.last_event_time = datetime.now(timezone.utc)

                # 更新事件类型计数
                event_type = event.event_type
                self._event_metrics.event_type_counts[event_type] = (
                    self._event_metrics.event_type_counts.get(event_type, 0) + 1
                )

            if self.logger:
                await self.logger.debug(
                    f"Event published: {event.event_type} ({event.event_id})"
                )

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to publish event: {str(e)}")
            return False

    async def publish_batch(self, events: List[BaseEvent]) -> int:
        """
        批量发布事件

        Args:
            events: 事件列表

        Returns:
            int: 成功发布的事件数量
        """
        success_count = 0

        for event in events:
            if await self.publish(event):
                success_count += 1

        return success_count

    async def get_event_history(
        self,
        event_type: Optional[str] = None,
        limit: Optional[int] = None,
        since: Optional[datetime] = None,
    ) -> List[EventRecord]:
        """
        获取事件历史

        Args:
            event_type: 事件类型过滤
            limit: 返回数量限制
            since: 时间过滤

        Returns:
            List[EventRecord]: 事件记录列表
        """
        filtered_events = []

        for record in self._event_history:
            # 事件类型过滤
            if event_type and record.event.event_type != event_type:
                continue

            # 时间过滤
            if since and record.event.timestamp < since:
                continue

            filtered_events.append(record)

        # 按时间倒序排序
        filtered_events.sort(key=lambda x: x.event.timestamp, reverse=True)

        # 应用数量限制
        if limit:
            filtered_events = filtered_events[:limit]

        return filtered_events

    async def replay_events(
        self,
        event_type: Optional[str] = None,
        since: Optional[datetime] = None,
        target_subscriber: Optional[str] = None,
    ) -> int:
        """
        重放事件

        Args:
            event_type: 事件类型过滤
            since: 时间过滤
            target_subscriber: 目标订阅者

        Returns:
            int: 重放的事件数量
        """
        events_to_replay = await self.get_event_history(event_type, since=since)
        replayed_count = 0

        for record in events_to_replay:
            event = record.event

            # 如果指定了目标订阅者，只发送给该订阅者
            if target_subscriber:
                if target_subscriber in self._subscribers:
                    subscription = self._subscribers[target_subscriber]
                    if event.event_type in subscription.event_types:
                        await self._process_event_for_subscriber(event, subscription)
                        replayed_count += 1
            else:
                # 重新发布事件
                await self.publish(event)
                replayed_count += 1

        if self.logger:
            await self.logger.info(f"Replayed {replayed_count} events")

        return replayed_count

    async def get_metrics(self) -> Dict[str, Any]:
        """获取事件总线指标"""
        base_metrics = await super().get_metrics()

        async with self._metrics_lock:
            event_metrics = {
                "total_published": self._event_metrics.total_published,
                "total_processed": self._event_metrics.total_processed,
                "total_failed": self._event_metrics.total_failed,
                "total_retries": self._event_metrics.total_retries,
                "avg_processing_time": self._event_metrics.avg_processing_time,
                "events_per_second": self._event_metrics.events_per_second,
                "last_event_time": self._event_metrics.last_event_time.isoformat()
                if self._event_metrics.last_event_time
                else None,
                "event_type_counts": self._event_metrics.event_type_counts.copy(),
                "queue_size": self._event_queue.qsize(),
                "active_subscribers": len(self._subscribers),
                "dead_letter_queue_size": len(self._dead_letter_queue),
                "history_size": len(self._event_history),
            }

        base_metrics.update(event_metrics)
        return base_metrics

    async def _event_worker(self, worker_id: str):
        """事件处理工作协程"""
        if self.logger:
            await self.logger.info(f"Event worker {worker_id} started")

        try:
            while self.is_running:
                try:
                    # 从队列获取事件
                    event = await asyncio.wait_for(self._event_queue.get(), timeout=1.0)

                    # 处理事件
                    await self._process_event(event)

                except asyncio.TimeoutError:
                    # 超时是正常的，继续循环
                    continue
                except asyncio.CancelledError:
                    # 工作协程被取消
                    break
                except Exception as e:
                    if self.logger:
                        await self.logger.error(f"Worker {worker_id} error: {str(e)}")

        except asyncio.CancelledError:
            pass
        finally:
            if self.logger:
                await self.logger.info(f"Event worker {worker_id} stopped")

    async def _process_event(self, event: BaseEvent):
        """处理单个事件"""
        start_time = time.time()
        event_record = EventRecord(event=event, status=EventStatus.PROCESSING)

        try:
            # 查找订阅者
            subscribers = self._event_type_subscribers.get(event.event_type, set())

            if not subscribers:
                # 没有订阅者，记录并返回
                event_record.status = EventStatus.COMPLETED
                event_record.processing_time = time.time() - start_time
                await self._record_event(event_record)
                return

            # 并发处理所有订阅者
            tasks = []
            for subscriber_id in subscribers:
                if subscriber_id in self._subscribers:
                    subscription = self._subscribers[subscriber_id]
                    if subscription.is_active:
                        task = asyncio.create_task(
                            self._process_event_for_subscriber(event, subscription)
                        )
                        tasks.append(task)

            # 等待所有处理完成
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # 检查是否有失败
                failed_count = sum(
                    1 for result in results if isinstance(result, Exception)
                )

                if failed_count > 0:
                    event_record.status = EventStatus.FAILED
                    event_record.error_message = f"{failed_count} subscribers failed"

                    # 更新失败指标
                    async with self._metrics_lock:
                        self._event_metrics.total_failed += 1
                else:
                    event_record.status = EventStatus.COMPLETED
            else:
                event_record.status = EventStatus.COMPLETED

            # 更新指标
            async with self._metrics_lock:
                self._event_metrics.total_processed += 1
                processing_time = time.time() - start_time

                # 更新平均处理时间
                if self._event_metrics.total_processed == 1:
                    self._event_metrics.avg_processing_time = processing_time
                else:
                    self._event_metrics.avg_processing_time = (
                        self._event_metrics.avg_processing_time
                        * (self._event_metrics.total_processed - 1)
                        + processing_time
                    ) / self._event_metrics.total_processed

        except Exception as e:
            event_record.status = EventStatus.FAILED
            event_record.error_message = str(e)

            async with self._metrics_lock:
                self._event_metrics.total_failed += 1

            if self.logger:
                await self.logger.error(f"Event processing failed: {str(e)}")

        finally:
            event_record.processing_time = time.time() - start_time
            event_record.processed_at = datetime.now(timezone.utc)
            await self._record_event(event_record)

    async def _process_event_for_subscriber(
        self, event: BaseEvent, subscription: EventSubscription
    ):
        """为特定订阅者处理事件"""
        try:
            # 应用过滤器
            if subscription.filter_func:
                if not subscription.filter_func(event):
                    return

            # 调用回调函数
            if asyncio.iscoroutinefunction(subscription.callback):
                await subscription.callback(event)
            else:
                subscription.callback(event)

        except Exception as e:
            # 处理重试逻辑
            if subscription.max_retries > 0:
                await self._retry_event_processing(event, subscription, e)
            else:
                # 添加到死信队列
                self._dead_letter_queue.append(
                    {
                        "event": event,
                        "subscriber_id": subscription.subscriber_id,
                        "error": str(e),
                        "timestamp": datetime.now(timezone.utc),
                    }
                )

            raise e

    async def _retry_event_processing(
        self, event: BaseEvent, subscription: EventSubscription, error: Exception
    ):
        """重试事件处理"""
        # 这里可以实现更复杂的重试逻辑
        # 为简化，暂时只记录错误
        async with self._metrics_lock:
            self._event_metrics.total_retries += 1

        if self.logger:
            await self.logger.warning(
                f"Event processing failed for subscriber {subscription.subscriber_id}: {str(error)}"
            )

    async def _record_event(self, event_record: EventRecord):
        """记录事件到历史"""
        if self._enable_persistence:
            self._event_history.append(event_record)

    def add_error_handler(self, event_type: str, handler: Callable):
        """添加错误处理器"""
        self._error_handlers[event_type] = handler

    def get_subscribers(
        self, event_type: Optional[str] = None
    ) -> Dict[str, EventSubscription]:
        """获取订阅者信息"""
        if event_type:
            subscriber_ids = self._event_type_subscribers.get(event_type, set())
            return {
                sid: self._subscribers[sid]
                for sid in subscriber_ids
                if sid in self._subscribers
            }
        else:
            return self._subscribers.copy()

    def get_dead_letter_queue(self) -> List[Dict[str, Any]]:
        """获取死信队列"""
        return list(self._dead_letter_queue)
