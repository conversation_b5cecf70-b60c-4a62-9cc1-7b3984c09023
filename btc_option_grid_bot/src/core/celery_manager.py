#!/usr/bin/env python3
"""
Celery分布式任务管理器

负责处理CPU密集型任务，包括：
- GEX（Gamma Exposure）分布计算
- 历史数据压缩和归档
- 复杂数学计算和分析任务

设计要点：
- 支持分布式计算，可横向扩展
- 任务优先级队列管理
- 自动重试机制和错误处理
- 性能监控和健康检查
"""

import json
import time
import zlib
from dataclasses import dataclass
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional

import numpy as np
import redis
from celery import Celery
from celery.result import AsyncResult
from celery.signals import worker_ready, worker_shutdown
from kombu import Queue

from ..utils.async_logger import AsyncLogger
from .base_component import BaseComponent, HealthCheckResult, HealthStatus


class TaskPriority(Enum):
    """任务优先级枚举"""

    HIGH = "high"  # 实时计算任务
    MEDIUM = "medium"  # 数据处理任务
    LOW = "low"  # 归档任务


@dataclass
class TaskResult:
    """任务结果数据类"""

    task_id: str
    status: str
    result: Any
    execution_time: float
    error: Optional[str] = None
    retry_count: int = 0


@dataclass
class WorkerStats:
    """Worker统计信息"""

    active_tasks: int
    processed_tasks: int
    failed_tasks: int
    avg_execution_time: float
    memory_usage: float
    cpu_usage: float


class CeleryTaskManager(BaseComponent):
    """
    Celery分布式任务管理器

    功能：
    1. 管理Celery应用配置和生命周期
    2. 提供GEX计算、数据压缩等任务接口
    3. 监控任务执行状态和性能指标
    4. 支持动态调整worker数量和任务优先级
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(component_name="CeleryTaskManager")
        self.config = config or {}
        self.redis_client: Optional[redis.Redis] = None

        # 创建Celery应用
        self.celery_app = Celery("btc_option_grid_bot")
        self._configure_celery()

        # 任务统计
        self.task_stats = {
            "total_tasks": 0,
            "successful_tasks": 0,
            "failed_tasks": 0,
            "avg_execution_time": 0.0,
        }

        # 注册任务
        self._register_tasks()

    def _configure_celery(self):
        """配置Celery应用"""
        # 默认配置
        default_config = {
            "broker_url": "redis://localhost:6379/1",
            "result_backend": "redis://localhost:6379/1",
            "task_serializer": "json",
            "accept_content": ["json"],
            "result_serializer": "json",
            "timezone": "UTC",
            "enable_utc": True,
            "worker_pool": "threads",
            "worker_concurrency": 8,
            "task_routes": {
                "btc_option_grid_bot.tasks.gex_calculation": {"queue": "high_priority"},
                "btc_option_grid_bot.tasks.data_compression": {
                    "queue": "medium_priority"
                },
                "btc_option_grid_bot.tasks.data_archival": {"queue": "low_priority"},
            },
            "task_default_retry_delay": 60,  # 1分钟
            "task_max_retries": 3,
            "task_acks_late": True,
            "worker_prefetch_multiplier": 1,
        }

        # 合并用户配置
        config = {**default_config, **self.config}

        # 配置队列
        self.celery_app.conf.task_routes = config["task_routes"]
        self.celery_app.conf.update(config)

        # 定义队列
        self.celery_app.conf.task_queues = (
            Queue("high_priority", routing_key="high_priority"),
            Queue("medium_priority", routing_key="medium_priority"),
            Queue("low_priority", routing_key="low_priority"),
        )

        # 配置重试策略
        self.celery_app.conf.task_annotations = {
            "*": {
                "rate_limit": "100/m",  # 每分钟最多100个任务
                "time_limit": 300,  # 5分钟超时
                "soft_time_limit": 240,  # 4分钟软超时
            },
            "btc_option_grid_bot.tasks.gex_calculation": {
                "rate_limit": "200/m",  # GEX计算任务更高频率
                "time_limit": 30,  # 30秒超时
                "soft_time_limit": 25,  # 25秒软超时
            },
        }

    def configure(self, config: Dict[str, Any]):
        """配置任务管理器"""
        self.config.update(config)
        self._configure_celery()

        # 初始化Redis客户端
        redis_config = config.get("redis", {})
        self.redis_client = redis.Redis(
            host=redis_config.get("host", "localhost"),
            port=redis_config.get("port", 6379),
            db=redis_config.get("db", 1),
            password=redis_config.get("password"),
            decode_responses=True,
        )

    def set_logger(self, logger: AsyncLogger):
        """设置日志记录器"""
        self.logger = logger

    def _register_tasks(self):
        """注册Celery任务"""

        @self.celery_app.task(
            bind=True, name="btc_option_grid_bot.tasks.gex_calculation"
        )
        def calculate_gex_distribution(
            task_self, option_chain_data: Dict[str, Any]
        ) -> Dict[str, Any]:
            """
            基于Deribit实时Greeks计算GEX分布

            Args:
                option_chain_data: 期权链数据，包含Greeks信息

            Returns:
                GEX分布数据
            """
            start_time = time.time()

            try:
                gex_levels = {}
                total_gex = 0.0

                for strike_str, option_data in option_chain_data.items():
                    strike = float(strike_str)

                    # 获取Greeks数据
                    greeks = option_data.get("greeks", {})
                    gamma = greeks.get("gamma", 0.0)
                    open_interest = option_data.get("open_interest", 0.0)
                    contract_size = option_data.get("contract_size", 1.0)

                    # 计算GEX: GEX = Gamma × Open Interest × Contract Size
                    gex = gamma * open_interest * contract_size
                    gex_levels[strike] = gex
                    total_gex += abs(gex)

                # 计算GEX分布统计
                if gex_levels:
                    strikes = list(gex_levels.keys())
                    gex_values = list(gex_levels.values())

                    # 加权平均strike（GEX密度中心）
                    if total_gex > 0:
                        weighted_strike = (
                            sum(strike * abs(gex) for strike, gex in gex_levels.items())
                            / total_gex
                        )
                    else:
                        weighted_strike = np.mean(strikes) if strikes else 0.0

                    # 找到最大GEX区域
                    max_gex_strike = (
                        max(gex_levels.items(), key=lambda x: abs(x[1]))[0]
                        if gex_levels
                        else 0.0
                    )

                    # 高级GEX分析
                    sorted_strikes = sorted(strikes)
                    price_ranges = self._calculate_gex_price_ranges(
                        gex_levels, sorted_strikes
                    )
                    gex_density = self._calculate_gex_density(
                        gex_levels, sorted_strikes
                    )
                    support_resistance = self._identify_support_resistance_levels(
                        gex_levels
                    )

                    result = {
                        "gex_levels": gex_levels,
                        "total_gex": total_gex,
                        "weighted_strike": weighted_strike,
                        "max_gex_strike": max_gex_strike,
                        "gex_concentration": max(abs(gex) for gex in gex_values)
                        / total_gex
                        if total_gex > 0
                        else 0.0,
                        "price_ranges": price_ranges,
                        "gex_density": gex_density,
                        "support_resistance": support_resistance,
                        "gamma_profile": self._create_gamma_profile(gex_levels),
                        "calculation_time": time.time() - start_time,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    }

                    # 存储到Redis
                    if self.redis_client:
                        self.redis_client.hset(
                            "gex_distribution",
                            mapping={
                                "data": json.dumps(result),
                                "updated_at": result["timestamp"],
                            },
                        )
                        self.redis_client.expire("gex_distribution", 120)  # TTL=120秒

                    return result
                else:
                    return {
                        "gex_levels": {},
                        "total_gex": 0.0,
                        "weighted_strike": 0.0,
                        "max_gex_strike": 0.0,
                        "gex_concentration": 0.0,
                        "calculation_time": time.time() - start_time,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    }

            except Exception as e:
                # 记录错误并重试
                error_msg = f"GEX计算失败: {str(e)}"
                if hasattr(task_self, "retry"):
                    # 指数退避重试
                    countdown = 2**task_self.request.retries
                    raise task_self.retry(exc=e, countdown=countdown, max_retries=3)
                else:
                    raise Exception(error_msg)

        @self.celery_app.task(
            bind=True, name="btc_option_grid_bot.tasks.data_compression"
        )
        def compress_historical_data(
            task_self, data_batch: List[Dict[str, Any]]
        ) -> Dict[str, Any]:
            """
            压缩历史数据

            Args:
                data_batch: 待压缩的数据批次

            Returns:
                压缩结果信息
            """
            start_time = time.time()

            try:
                # 序列化数据
                json_data = json.dumps(data_batch, separators=(",", ":"))
                original_size = len(json_data.encode("utf-8"))

                # 使用zlib压缩
                compressed_data = zlib.compress(json_data.encode("utf-8"), level=6)
                compressed_size = len(compressed_data)

                # 计算压缩比
                compression_ratio = (
                    compressed_size / original_size if original_size > 0 else 0.0
                )

                result = {
                    "original_size": original_size,
                    "compressed_size": compressed_size,
                    "compression_ratio": compression_ratio,
                    "records_count": len(data_batch),
                    "compression_time": time.time() - start_time,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

                return result

            except Exception as e:
                error_msg = f"数据压缩失败: {str(e)}"
                if hasattr(task_self, "retry"):
                    countdown = 2**task_self.request.retries
                    raise task_self.retry(exc=e, countdown=countdown, max_retries=3)
                else:
                    raise Exception(error_msg)

        @self.celery_app.task(bind=True, name="btc_option_grid_bot.tasks.data_archival")
        def archive_old_data(
            task_self, cutoff_date: str, table_name: str
        ) -> Dict[str, Any]:
            """
            归档旧数据

            Args:
                cutoff_date: 截止日期
                table_name: 表名

            Returns:
                归档结果信息
            """
            start_time = time.time()

            try:
                # 这里应该连接数据库进行归档操作
                # 为了演示，我们模拟归档过程

                archived_records = 0  # 实际应该从数据库查询

                result = {
                    "table_name": table_name,
                    "cutoff_date": cutoff_date,
                    "archived_records": archived_records,
                    "archival_time": time.time() - start_time,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

                return result

            except Exception as e:
                error_msg = f"数据归档失败: {str(e)}"
                if hasattr(task_self, "retry"):
                    countdown = 2**task_self.request.retries
                    raise task_self.retry(exc=e, countdown=countdown, max_retries=3)
                else:
                    raise Exception(error_msg)

        @self.celery_app.task(
            bind=True, name="btc_option_grid_bot.tasks.taker_flow_calculation"
        )
        def calculate_taker_flow(
            task_self, trade_data: List[Dict[str, Any]], window_minutes: int = 5
        ) -> Dict[str, Any]:
            """
            计算Taker Flow数据

            Args:
                trade_data: 交易数据列表
                window_minutes: 时间窗口（分钟）

            Returns:
                dict: Taker Flow计算结果
            """
            try:
                from datetime import datetime, timezone

                start_time = time.time()

                if not trade_data:
                    return {
                        "buy_volume": 0,
                        "sell_volume": 0,
                        "net_flow": 0,
                        "buy_ratio": 0,
                        "trade_count": 0,
                        "window_minutes": window_minutes,
                        "calculation_time": time.time() - start_time,
                    }

                # 计算买入和卖出量
                buy_volume = sum(
                    float(trade.get("quantity", 0))
                    for trade in trade_data
                    if not trade.get("is_buyer_maker", True)  # Taker买入
                )

                sell_volume = sum(
                    float(trade.get("quantity", 0))
                    for trade in trade_data
                    if trade.get("is_buyer_maker", True)  # Taker卖出
                )

                total_volume = buy_volume + sell_volume
                buy_ratio = buy_volume / total_volume if total_volume > 0 else 0
                net_flow = buy_volume - sell_volume

                result = {
                    "buy_volume": buy_volume,
                    "sell_volume": sell_volume,
                    "net_flow": net_flow,
                    "buy_ratio": buy_ratio,
                    "trade_count": len(trade_data),
                    "window_minutes": window_minutes,
                    "calculation_time": time.time() - start_time,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

                return result

            except Exception as e:
                error_msg = f"Taker Flow计算失败: {str(e)}"
                if hasattr(task_self, "retry"):
                    countdown = 2**task_self.request.retries
                    raise task_self.retry(exc=e, countdown=countdown, max_retries=3)
                else:
                    raise Exception(error_msg)

        # 存储任务引用
        self.calculate_gex_distribution = calculate_gex_distribution
        self.compress_historical_data = compress_historical_data
        self.archive_old_data = archive_old_data
        self.calculate_taker_flow = calculate_taker_flow

    async def submit_gex_calculation(self, option_chain_data: Dict[str, Any]) -> str:
        """
        提交GEX计算任务

        Args:
            option_chain_data: 期权链数据

        Returns:
            任务ID
        """
        try:
            result = self.calculate_gex_distribution.apply_async(
                args=[option_chain_data], queue="high_priority"
            )

            if self.logger:
                await self.logger.debug(f"提交GEX计算任务: {result.id}")

            return result.id

        except Exception as e:
            if self.logger:
                await self.logger.error(f"提交GEX计算任务失败: {e}")
            raise

    async def submit_taker_flow_calculation(
        self, trade_data: List[Dict[str, Any]], window_minutes: int = 5
    ) -> str:
        """
        提交Taker Flow计算任务

        Args:
            trade_data: 交易数据列表
            window_minutes: 时间窗口（分钟）

        Returns:
            任务ID
        """
        try:
            result = self.calculate_taker_flow.apply_async(
                args=[trade_data, window_minutes], queue="medium_priority"
            )

            if self.logger:
                await self.logger.debug(f"提交Taker Flow计算任务: {result.id}")

            return result.id

        except Exception as e:
            if self.logger:
                await self.logger.error(f"提交Taker Flow计算任务失败: {e}")
            raise

    async def submit_data_compression(self, data_batch: List[Dict[str, Any]]) -> str:
        """
        提交数据压缩任务

        Args:
            data_batch: 数据批次

        Returns:
            任务ID
        """
        try:
            result = self.compress_historical_data.apply_async(
                args=[data_batch], queue="medium_priority"
            )

            if self.logger:
                await self.logger.debug(f"提交数据压缩任务: {result.id}")

            return result.id

        except Exception as e:
            if self.logger:
                await self.logger.error(f"提交数据压缩任务失败: {e}")
            raise

    async def submit_data_archival(self, cutoff_date: str, table_name: str) -> str:
        """
        提交数据归档任务

        Args:
            cutoff_date: 截止日期
            table_name: 表名

        Returns:
            任务ID
        """
        try:
            result = self.archive_old_data.apply_async(
                args=[cutoff_date, table_name], queue="low_priority"
            )

            if self.logger:
                await self.logger.debug(f"提交数据归档任务: {result.id}")

            return result.id

        except Exception as e:
            if self.logger:
                await self.logger.error(f"提交数据归档任务失败: {e}")
            raise

    async def get_task_result(self, task_id: str) -> TaskResult:
        """
        获取任务结果

        Args:
            task_id: 任务ID

        Returns:
            任务结果
        """
        try:
            async_result = AsyncResult(task_id, app=self.celery_app)

            # 获取任务状态
            status = async_result.status
            result = None
            error = None
            execution_time = 0.0

            if status == "SUCCESS":
                result = async_result.result
                if isinstance(result, dict):
                    execution_time = result.get("calculation_time", 0.0)
            elif status == "FAILURE":
                error = str(async_result.info)

            return TaskResult(
                task_id=task_id,
                status=status,
                result=result,
                execution_time=execution_time,
                error=error,
                retry_count=getattr(async_result.info, "retries", 0)
                if hasattr(async_result, "info")
                else 0,
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"获取任务结果失败: {e}")

            return TaskResult(
                task_id=task_id,
                status="ERROR",
                result=None,
                execution_time=0.0,
                error=str(e),
            )

    async def get_worker_stats(self) -> Dict[str, WorkerStats]:
        """
        获取Worker统计信息

        Returns:
            Worker统计信息字典
        """
        try:
            # 获取活跃worker信息
            inspect = self.celery_app.control.inspect()

            # 获取统计信息
            stats = inspect.stats()
            active_tasks = inspect.active()

            worker_stats = {}

            if stats:
                for worker_name, worker_info in stats.items():
                    # 获取基本统计
                    pool_info = worker_info.get("pool", {})

                    # 计算活跃任务数
                    active_count = (
                        len(active_tasks.get(worker_name, [])) if active_tasks else 0
                    )

                    worker_stats[worker_name] = WorkerStats(
                        active_tasks=active_count,
                        processed_tasks=worker_info.get("total", {}).get(
                            "tasks.received", 0
                        ),
                        failed_tasks=worker_info.get("total", {}).get(
                            "tasks.failed", 0
                        ),
                        avg_execution_time=0.0,  # 需要从任务历史计算
                        memory_usage=pool_info.get("memory_usage", 0.0),
                        cpu_usage=pool_info.get("cpu_usage", 0.0),
                    )

            return worker_stats

        except Exception as e:
            if self.logger:
                await self.logger.error(f"获取Worker统计失败: {e}")
            return {}

    async def health_check(self) -> Dict[str, Any]:
        """
        健康检查

        Returns:
            健康状态信息
        """
        try:
            # 检查Celery连接
            inspect = self.celery_app.control.inspect()
            stats = inspect.stats()

            # 检查Redis连接
            redis_healthy = False
            if self.redis_client:
                try:
                    self.redis_client.ping()
                    redis_healthy = True
                except Exception:
                    redis_healthy = False

            # 检查活跃worker数量
            active_workers = len(stats) if stats else 0

            # 计算整体健康状态
            healthy = active_workers > 0 and redis_healthy

            return {
                "healthy": healthy,
                "active_workers": active_workers,
                "redis_connected": redis_healthy,
                "task_stats": self.task_stats,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"健康检查失败: {e}")

            return {
                "healthy": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    # BaseComponent抽象方法实现
    async def _initialize_impl(self) -> None:
        """初始化实现"""
        # 初始化Redis连接
        redis_config = self.config.get("redis", {})
        self.redis_client = redis.Redis(
            host=redis_config.get("host", "localhost"),
            port=redis_config.get("port", 6379),
            db=redis_config.get("db", 1),
            decode_responses=True,
        )

        # 测试Redis连接
        try:
            self.redis_client.ping()
        except Exception as e:
            raise RuntimeError(f"Failed to connect to Redis: {e}")

    async def _start_impl(self) -> None:
        """启动实现"""
        if self.logger:
            await self.logger.info("🚀 Celery任务管理器启动中...")

        # 这里不直接启动worker，worker应该通过命令行启动
        # 我们只是确保配置正确

        if self.logger:
            await self.logger.info("✅ Celery任务管理器配置完成")

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            if self.logger:
                await self.logger.info("🛑 正在停止Celery任务管理器...")

            # 关闭Redis连接
            if self.redis_client:
                self.redis_client.close()

            if self.logger:
                await self.logger.info("✅ Celery任务管理器已停止")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"❌ 停止Celery任务管理器失败: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            # 检查Redis连接
            redis_healthy = False
            if self.redis_client:
                try:
                    self.redis_client.ping()
                    redis_healthy = True
                except Exception:
                    pass

            # 检查Celery应用状态
            celery_healthy = self.celery_app is not None

            if redis_healthy and celery_healthy:
                status = HealthStatus.HEALTHY
                message = "Celery task manager is healthy"
            else:
                status = HealthStatus.UNHEALTHY
                message = "Celery task manager has issues"

            details = {
                "redis_connected": redis_healthy,
                "celery_app_configured": celery_healthy,
                "task_stats": self.task_stats,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            return HealthCheckResult(status=status, message=message, details=details)

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {str(e)}",
                details={
                    "error": str(e),
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                },
            )

    def _calculate_gex_price_ranges(
        self, gex_levels: Dict[float, float], strikes: List[float]
    ) -> Dict[str, Any]:
        """计算GEX价格区间分布"""
        if not strikes:
            return {}

        min_strike = min(strikes)
        max_strike = max(strikes)
        range_size = (max_strike - min_strike) / 10  # 分为10个区间

        ranges = {}
        for i in range(10):
            range_start = min_strike + i * range_size
            range_end = min_strike + (i + 1) * range_size
            range_gex = sum(
                abs(gex_levels.get(strike, 0))
                for strike in strikes
                if range_start <= strike < range_end
            )
            ranges[f"{range_start:.0f}-{range_end:.0f}"] = range_gex

        return ranges

    def _calculate_gex_density(
        self, gex_levels: Dict[float, float], strikes: List[float]
    ) -> Dict[str, float]:
        """计算GEX密度分布"""
        if not strikes:
            return {}

        # 计算标准差作为密度衡量
        mean_strike = sum(strikes) / len(strikes)
        variance = sum(
            (strike - mean_strike) ** 2 * abs(gex_levels.get(strike, 0))
            for strike in strikes
        ) / len(strikes)
        std_dev = variance**0.5

        return {
            "mean_strike": mean_strike,
            "std_deviation": std_dev,
            "density_score": 1 / (1 + std_dev),  # 密度评分（0-1）
        }

    def _identify_support_resistance_levels(
        self, gex_levels: Dict[float, float]
    ) -> Dict[str, List[float]]:
        """识别支撑/阻力位"""
        if not gex_levels:
            return {"support": [], "resistance": []}

        # 按GEX绝对值排序，取前5个作为关键位
        sorted_levels = sorted(
            gex_levels.items(), key=lambda x: abs(x[1]), reverse=True
        )[:5]

        support_levels = []
        resistance_levels = []

        for strike, gex in sorted_levels:
            if gex > 0:  # 正GEX通常形成支撑
                support_levels.append(strike)
            else:  # 负GEX通常形成阻力
                resistance_levels.append(strike)

        return {
            "support": sorted(support_levels),
            "resistance": sorted(resistance_levels),
        }

    def _create_gamma_profile(self, gex_levels: Dict[float, float]) -> Dict[str, Any]:
        """创建Gamma分布轮廓"""
        if not gex_levels:
            return {}

        strikes = sorted(gex_levels.keys())

        # 找到正负GEX的分界点
        positive_gex = {s: g for s, g in gex_levels.items() if g > 0}
        negative_gex = {s: g for s, g in gex_levels.items() if g < 0}

        return {
            "strike_range": [min(strikes), max(strikes)],
            "positive_gex_center": sum(s * abs(g) for s, g in positive_gex.items())
            / sum(abs(g) for g in positive_gex.values())
            if positive_gex
            else 0,
            "negative_gex_center": sum(s * abs(g) for s, g in negative_gex.items())
            / sum(abs(g) for g in negative_gex.values())
            if negative_gex
            else 0,
            "gamma_wall_strikes": [
                s
                for s, g in sorted(
                    gex_levels.items(), key=lambda x: abs(x[1]), reverse=True
                )[:3]
            ],
        }


# 全局Celery应用实例（用于worker启动）
celery_app = None


def get_celery_app() -> Celery:
    """获取全局Celery应用实例"""
    global celery_app
    if celery_app is None:
        manager = CeleryTaskManager()
        celery_app = manager.celery_app
    return celery_app


# Worker信号处理
@worker_ready.connect
def worker_ready_handler(sender=None, **kwargs):
    """Worker启动完成信号处理"""
    print(f"🚀 Celery Worker {sender} 已启动")


@worker_shutdown.connect
def worker_shutdown_handler(sender=None, **kwargs):
    """Worker关闭信号处理"""
    print(f"🛑 Celery Worker {sender} 已关闭")
