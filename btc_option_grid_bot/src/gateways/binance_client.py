"""
Binance WebSocket客户端模块
实现现货、永续合约数据订阅和交易执行功能
"""

import asyncio
import hashlib
import hmac
import json
import logging
import time
from dataclasses import dataclass, field
from datetime import datetime, timezone
from decimal import Decimal
from typing import Any, Callable, Dict, List, Optional, Union
from urllib.parse import urlencode

import aiohttp
import websockets
from websockets.exceptions import ConnectionClosed

from ..core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from ..core.celery_manager import CeleryTaskManager
from ..core.config_manager import ConfigManager
from ..data.cache_manager import CacheManager

logger = logging.getLogger(__name__)


@dataclass
class BinanceMarketData:
    """Binance市场数据模型"""

    symbol: str
    price: Decimal
    volume: Decimal
    timestamp: datetime
    exchange: str = "binance"
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BinanceTrade:
    """Binance交易数据模型"""

    symbol: str
    price: Decimal
    quantity: Decimal
    is_buyer_maker: bool
    timestamp: datetime
    trade_id: int


@dataclass
class BinanceOrderBook:
    """Binance订单簿数据模型"""

    symbol: str
    bids: List[List[str]]
    asks: List[List[str]]
    timestamp: datetime


@dataclass
class BinanceOrder:
    """Binance订单数据模型"""

    symbol: str
    order_id: int
    client_order_id: str
    side: str
    order_type: str
    quantity: Decimal
    price: Optional[Decimal]
    status: str
    executed_qty: Decimal
    cumulative_quote_qty: Decimal
    timestamp: datetime


@dataclass
class RateLimitInfo:
    """限频信息"""

    limit_type: str
    interval: str
    limit: int
    count: int


class BinanceClient(BaseComponent):
    """
    Binance WebSocket客户端
    负责连接Binance WebSocket API，获取现货和永续合约数据
    """

    def __init__(
        self,
        api_key: Optional[str] = None,
        api_secret: Optional[str] = None,
        celery_manager: Optional[CeleryTaskManager] = None,
        config_manager: Optional[ConfigManager] = None,
        cache_manager: Optional[CacheManager] = None,
    ):
        super().__init__("binance_client")

        # API认证信息
        self.api_key = api_key
        self.api_secret = api_secret

        # 基础设施组件
        self.celery_manager = celery_manager
        self.config_manager = config_manager

        # WebSocket连接配置
        self.spot_ws_url = "wss://stream.binance.com:9443/ws"
        self.futures_ws_url = "wss://fstream.binance.com/ws"
        self.rest_api_url = "https://api.binance.com"
        self.futures_api_url = "https://fapi.binance.com"

        # 连接管理
        self.spot_ws = None
        self.futures_ws = None
        self.session = None
        self._reconnect_attempts = 0
        self._max_reconnect_attempts = 5
        self._reconnect_delay = 1.0

        # 订阅管理
        self.subscribed_streams = set()
        self.stream_callbacks = {}

        # 缓存管理器
        self.cache_manager = cache_manager

        # 数据缓存（仅保留必要的实时交易缓冲区）
        self.trade_buffer = []
        self.max_trade_buffer_size = 1000

        # 任务管理
        self.message_handlers = {}
        self.heartbeat_tasks = []

        # 限频控制
        self.rate_limits = {}
        self.last_request_time = {}
        self.request_weights = {
            "spot": {
                "order": 1,
                "cancel_order": 1,
                "query_order": 2,
                "account_info": 10,
            },
            "futures": {
                "order": 1,
                "cancel_order": 1,
                "query_order": 1,
                "account_info": 5,
            },
        }

    async def _initialize_impl(self) -> None:
        """初始化实现"""
        # 创建HTTP会话
        self.session = aiohttp.ClientSession()

        # 连接WebSocket
        await self._connect_websockets()

        logger.info("BinanceClient初始化完成")

    async def _start_impl(self) -> None:
        """启动实现"""
        # 启动消息处理任务
        self.message_handlers["spot"] = asyncio.create_task(
            self._handle_spot_messages()
        )
        self.message_handlers["futures"] = asyncio.create_task(
            self._handle_futures_messages()
        )

        # 启动心跳任务
        self.heartbeat_tasks.append(asyncio.create_task(self._heartbeat_task()))

        logger.info("BinanceClient启动成功")

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            # 取消所有任务
            for task_name, task in self.message_handlers.items():
                if task and not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            for task in self.heartbeat_tasks:
                if task and not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            # 关闭WebSocket连接
            if self.spot_ws:
                await self.spot_ws.close()
            if self.futures_ws:
                await self.futures_ws.close()

            # 关闭HTTP会话
            if self.session:
                await self.session.close()

            logger.info("BinanceClient已停止")
            return True
        except Exception as e:
            logger.error(f"BinanceClient停止失败: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""

        # 检查连接状态
        spot_connected = self.spot_ws and not self.spot_ws.closed
        futures_connected = self.futures_ws and not self.futures_ws.closed

        # 确定健康状态
        if spot_connected and futures_connected:
            status = HealthStatus.HEALTHY
            message = "All connections active"
        elif spot_connected or futures_connected:
            status = HealthStatus.DEGRADED
            message = "Partial connections active"
        else:
            status = HealthStatus.UNHEALTHY
            message = "No active connections"

        details = {
            "spot_ws_connected": spot_connected,
            "futures_ws_connected": futures_connected,
            "subscribed_streams": len(self.subscribed_streams),
            "reconnect_attempts": self._reconnect_attempts,
            "trade_buffer_size": len(self.trade_buffer),
            "cache_manager_connected": self.cache_manager is not None,
        }

        return HealthCheckResult(status=status, message=message, details=details)

    async def _connect_websockets(self) -> None:
        """连接WebSocket"""
        try:
            # 连接现货WebSocket
            self.spot_ws = await websockets.connect(
                self.spot_ws_url, ping_interval=20, ping_timeout=10, close_timeout=10
            )
            logger.info("现货WebSocket连接成功")

            # 连接期货WebSocket
            self.futures_ws = await websockets.connect(
                self.futures_ws_url, ping_interval=20, ping_timeout=10, close_timeout=10
            )
            logger.info("期货WebSocket连接成功")

            self._reconnect_attempts = 0

        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
            raise

    async def _reconnect_websockets(self) -> bool:
        """重连WebSocket"""
        if self._reconnect_attempts >= self._max_reconnect_attempts:
            logger.error("达到最大重连次数，停止重连")
            return False

        self._reconnect_attempts += 1
        delay = self._reconnect_delay * (
            2 ** (self._reconnect_attempts - 1)
        )  # 指数退避

        logger.info(
            f"尝试重连WebSocket (第{self._reconnect_attempts}次)，等待{delay}秒"
        )
        await asyncio.sleep(delay)

        try:
            await self._connect_websockets()

            # 重新订阅之前的流
            if self.subscribed_streams:
                await self._resubscribe_streams()

            return True

        except Exception as e:
            logger.error(f"WebSocket重连失败: {e}")
            return False

    async def _resubscribe_streams(self) -> None:
        """重新订阅流"""
        try:
            streams_list = list(self.subscribed_streams)
            for stream in streams_list:
                if "futures" in stream:
                    await self._subscribe_futures_stream(stream)
                else:
                    await self._subscribe_spot_stream(stream)

            logger.info(f"重新订阅{len(streams_list)}个数据流")

        except Exception as e:
            logger.error(f"重新订阅失败: {e}")

    async def _handle_spot_messages(self) -> None:
        """处理现货WebSocket消息"""
        while self.is_running():
            try:
                if not self.spot_ws or self.spot_ws.closed:
                    logger.warning("现货WebSocket连接断开，尝试重连")
                    if not await self._reconnect_websockets():
                        break
                    continue

                message = await asyncio.wait_for(self.spot_ws.recv(), timeout=30.0)

                data = json.loads(message)
                await self._process_spot_message(data)

            except asyncio.TimeoutError:
                logger.warning("现货WebSocket接收超时")
                continue
            except ConnectionClosed:
                logger.warning("现货WebSocket连接关闭")
                if not await self._reconnect_websockets():
                    break
            except Exception as e:
                logger.error(f"处理现货消息失败: {e}")
                await asyncio.sleep(1)

    async def _handle_futures_messages(self) -> None:
        """处理期货WebSocket消息"""
        while self.is_running():
            try:
                if not self.futures_ws or self.futures_ws.closed:
                    logger.warning("期货WebSocket连接断开，尝试重连")
                    if not await self._reconnect_websockets():
                        break
                    continue

                message = await asyncio.wait_for(self.futures_ws.recv(), timeout=30.0)

                data = json.loads(message)
                await self._process_futures_message(data)

            except asyncio.TimeoutError:
                logger.warning("期货WebSocket接收超时")
                continue
            except ConnectionClosed:
                logger.warning("期货WebSocket连接关闭")
                if not await self._reconnect_websockets():
                    break
            except Exception as e:
                logger.error(f"处理期货消息失败: {e}")
                await asyncio.sleep(1)

    async def _process_spot_message(self, data: Dict) -> None:
        """处理现货消息"""
        try:
            stream = data.get("stream", "")
            event_data = data.get("data", {})

            if "@ticker" in stream:
                await self._handle_ticker_data(event_data, "spot")
            elif "@trade" in stream:
                await self._handle_trade_data(event_data, "spot")
            elif "@depth" in stream:
                await self._handle_depth_data(event_data, "spot")

        except Exception as e:
            logger.error(f"处理现货消息失败: {e}")

    async def _process_futures_message(self, data: Dict) -> None:
        """处理期货消息"""
        try:
            stream = data.get("stream", "")
            event_data = data.get("data", {})

            if "@ticker" in stream:
                await self._handle_ticker_data(event_data, "futures")
            elif "@aggTrade" in stream:
                await self._handle_trade_data(event_data, "futures")
            elif "@depth" in stream:
                await self._handle_depth_data(event_data, "futures")

        except Exception as e:
            logger.error(f"处理期货消息失败: {e}")

    async def _handle_ticker_data(self, data: Dict, market_type: str) -> None:
        """处理ticker数据"""
        try:
            symbol = data.get("s", "")
            price = Decimal(str(data.get("c", "0")))
            volume = Decimal(str(data.get("v", "0")))
            timestamp = datetime.fromtimestamp(data.get("E", 0) / 1000)

            market_data = BinanceMarketData(
                symbol=symbol,
                price=price,
                volume=volume,
                timestamp=timestamp,
                metadata={
                    "market_type": market_type,
                    "high": data.get("h"),
                    "low": data.get("l"),
                    "open": data.get("o"),
                    "change": data.get("P"),
                    "count": data.get("c"),
                },
            )

            # 缓存数据到CacheManager
            if self.cache_manager:
                cache_key = f"binance:{market_type}:{symbol}:ticker"
                await self.cache_manager.set(cache_key, market_data, ttl=30)

            # 调用回调函数
            callback_key = f"ticker:{market_type}:{symbol}"
            if callback_key in self.stream_callbacks:
                for callback in self.stream_callbacks[callback_key]:
                    try:
                        await callback(market_data)
                    except Exception as e:
                        logger.error(f"回调函数执行失败: {e}")

        except Exception as e:
            logger.error(f"处理ticker数据失败: {e}")

    async def _handle_trade_data(self, data: Dict, market_type: str) -> None:
        """处理交易数据"""
        try:
            symbol = data.get("s", "")
            price = Decimal(str(data.get("p", "0")))
            quantity = Decimal(str(data.get("q", "0")))
            is_buyer_maker = data.get("m", False)
            timestamp = datetime.fromtimestamp(data.get("T", 0) / 1000)
            trade_id = data.get("t", 0)

            trade = BinanceTrade(
                symbol=symbol,
                price=price,
                quantity=quantity,
                is_buyer_maker=is_buyer_maker,
                timestamp=timestamp,
                trade_id=trade_id,
            )

            # 添加到交易缓冲区
            self.trade_buffer.append(trade)
            if len(self.trade_buffer) > self.max_trade_buffer_size:
                self.trade_buffer.pop(0)

            # 调用回调函数
            callback_key = f"trade:{market_type}:{symbol}"
            if callback_key in self.stream_callbacks:
                for callback in self.stream_callbacks[callback_key]:
                    try:
                        await callback(trade)
                    except Exception as e:
                        logger.error(f"回调函数执行失败: {e}")

        except Exception as e:
            logger.error(f"处理交易数据失败: {e}")

    async def _handle_depth_data(self, data: Dict, market_type: str) -> None:
        """处理深度数据"""
        try:
            symbol = data.get("s", "")
            bids = data.get("b", [])
            asks = data.get("a", [])
            timestamp = datetime.fromtimestamp(data.get("E", 0) / 1000)

            orderbook = BinanceOrderBook(
                symbol=symbol, bids=bids, asks=asks, timestamp=timestamp
            )

            # 缓存订单簿数据到CacheManager
            if self.cache_manager:
                cache_key = f"binance:{market_type}:{symbol}:orderbook"
                await self.cache_manager.set(cache_key, orderbook, ttl=5)

            # 调用回调函数
            callback_key = f"depth:{market_type}:{symbol}"
            if callback_key in self.stream_callbacks:
                for callback in self.stream_callbacks[callback_key]:
                    try:
                        await callback(orderbook)
                    except Exception as e:
                        logger.error(f"回调函数执行失败: {e}")

        except Exception as e:
            logger.error(f"处理深度数据失败: {e}")

    async def _heartbeat_task(self) -> None:
        """心跳任务"""
        while self.is_running():
            try:
                await asyncio.sleep(30)  # 30秒心跳间隔

                # 检查连接状态
                if self.spot_ws and self.spot_ws.closed:
                    logger.warning("现货WebSocket连接断开")
                    await self._reconnect_websockets()

                if self.futures_ws and self.futures_ws.closed:
                    logger.warning("期货WebSocket连接断开")
                    await self._reconnect_websockets()

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"心跳任务失败: {e}")

    # 公共API方法

    async def subscribe_ticker(
        self, symbol: str, market_type: str = "spot", callback: Callable = None
    ) -> bool:
        """订阅ticker数据"""
        try:
            stream = f"{symbol.lower()}@ticker"

            if market_type == "spot":
                success = await self._subscribe_spot_stream(stream)
            else:
                success = await self._subscribe_futures_stream(stream)

            if success and callback:
                callback_key = f"ticker:{market_type}:{symbol}"
                if callback_key not in self.stream_callbacks:
                    self.stream_callbacks[callback_key] = []
                self.stream_callbacks[callback_key].append(callback)

            return success

        except Exception as e:
            logger.error(f"订阅ticker失败: {e}")
            return False

    async def subscribe_trades(
        self, symbol: str, market_type: str = "spot", callback: Callable = None
    ) -> bool:
        """订阅交易数据"""
        try:
            if market_type == "spot":
                stream = f"{symbol.lower()}@trade"
                success = await self._subscribe_spot_stream(stream)
            else:
                stream = f"{symbol.lower()}@aggTrade"
                success = await self._subscribe_futures_stream(stream)

            if success and callback:
                callback_key = f"trade:{market_type}:{symbol}"
                if callback_key not in self.stream_callbacks:
                    self.stream_callbacks[callback_key] = []
                self.stream_callbacks[callback_key].append(callback)

            return success

        except Exception as e:
            logger.error(f"订阅交易数据失败: {e}")
            return False

    async def subscribe_orderbook(
        self, symbol: str, market_type: str = "spot", callback: Callable = None
    ) -> bool:
        """订阅订单簿数据"""
        try:
            stream = f"{symbol.lower()}@depth@100ms"

            if market_type == "spot":
                success = await self._subscribe_spot_stream(stream)
            else:
                success = await self._subscribe_futures_stream(stream)

            if success and callback:
                callback_key = f"depth:{market_type}:{symbol}"
                if callback_key not in self.stream_callbacks:
                    self.stream_callbacks[callback_key] = []
                self.stream_callbacks[callback_key].append(callback)

            return success

        except Exception as e:
            logger.error(f"订阅订单簿失败: {e}")
            return False

    async def _subscribe_spot_stream(self, stream: str) -> bool:
        """订阅现货流"""
        try:
            if not self.spot_ws or self.spot_ws.closed:
                await self._reconnect_websockets()

            subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": [stream],
                "id": int(time.time()),
            }

            await self.spot_ws.send(json.dumps(subscribe_msg))
            self.subscribed_streams.add(stream)

            logger.info(f"订阅现货流成功: {stream}")
            return True

        except Exception as e:
            logger.error(f"订阅现货流失败: {e}")
            return False

    async def _subscribe_futures_stream(self, stream: str) -> bool:
        """订阅期货流"""
        try:
            if not self.futures_ws or self.futures_ws.closed:
                await self._reconnect_websockets()

            subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": [stream],
                "id": int(time.time()),
            }

            await self.futures_ws.send(json.dumps(subscribe_msg))
            self.subscribed_streams.add(f"futures:{stream}")

            logger.info(f"订阅期货流成功: {stream}")
            return True

        except Exception as e:
            logger.error(f"订阅期货流失败: {e}")
            return False

    async def get_latest_price(
        self, symbol: str, market_type: str = "spot"
    ) -> Optional[Decimal]:
        """获取最新价格"""
        # 从CacheManager获取缓存价格
        if self.cache_manager:
            cache_key = f"binance:{market_type}:{symbol}:ticker"
            cached_data = await self.cache_manager.get(cache_key)
            if cached_data and hasattr(cached_data, "price"):
                return cached_data.price
        return None

    async def get_recent_trades(
        self, symbol: str, limit: int = 100
    ) -> List[BinanceTrade]:
        """获取最近交易记录"""
        symbol_trades = [trade for trade in self.trade_buffer if trade.symbol == symbol]
        return symbol_trades[-limit:] if symbol_trades else []

    async def get_orderbook(
        self, symbol: str, market_type: str = "spot"
    ) -> Optional[BinanceOrderBook]:
        """获取订单簿"""
        # 从CacheManager获取缓存OrderBook
        if self.cache_manager:
            cache_key = f"binance:{market_type}:{symbol}:orderbook"
            return await self.cache_manager.get(cache_key)
        return None

    async def calculate_taker_flow(
        self, symbol: str, window_minutes: int = 5
    ) -> Dict[str, Any]:
        """计算Taker Flow数据（通过CeleryManager分布式计算）"""
        try:
            current_time = datetime.now(timezone.utc)
            window_start = current_time.timestamp() - (window_minutes * 60)

            # 获取时间窗口内的交易数据
            recent_trades = [
                {
                    "symbol": trade.symbol,
                    "quantity": float(trade.quantity),
                    "is_buyer_maker": trade.is_buyer_maker,
                    "timestamp": trade.timestamp.isoformat(),
                }
                for trade in self.trade_buffer
                if (
                    trade.symbol == symbol
                    and trade.timestamp.timestamp() >= window_start
                )
            ]

            # 通过CeleryManager提交Taker Flow计算任务
            if hasattr(self, "celery_manager") and self.celery_manager:
                task_id = await self.celery_manager.submit_taker_flow_calculation(
                    recent_trades, window_minutes
                )
                if task_id:
                    # 等待任务完成（最多等待10秒）
                    result = await self.celery_manager.get_task_result(
                        task_id, timeout=10
                    )
                    if result and result.status == "SUCCESS":
                        return result.result
                    else:
                        logger.warning(
                            f"Taker Flow calculation task failed or timed out: {task_id}"
                        )

            # CeleryManager不可用时返回空结果
            logger.error("CeleryManager not available, cannot calculate Taker Flow")
            return {
                "buy_volume": 0,
                "sell_volume": 0,
                "net_flow": 0,
                "buy_ratio": 0,
                "trade_count": 0,
                "window_minutes": window_minutes,
                "error": "CeleryManager not available",
            }

        except Exception as e:
            logger.error(f"计算Taker Flow失败: {e}")
            return {}

    async def get_funding_rate(self, symbol: str) -> Optional[Decimal]:
        """获取资金费率（期货）"""
        try:
            if not self.session:
                return None

            url = f"{self.futures_api_url}/fapi/v1/premiumIndex"
            params = {"symbol": symbol}

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return Decimal(str(data.get("lastFundingRate", "0")))

        except Exception as e:
            logger.error(f"获取资金费率失败: {e}")

        return None

    def _generate_signature(self, query_string: str) -> str:
        """生成API签名"""
        if not self.api_secret:
            raise ValueError("API secret is required for signed requests")

        return hmac.new(
            self.api_secret.encode("utf-8"),
            query_string.encode("utf-8"),
            hashlib.sha256,
        ).hexdigest()

    async def _check_rate_limit(self, market_type: str, endpoint: str) -> bool:
        """检查限频"""
        try:
            current_time = time.time()
            weight = self.request_weights.get(market_type, {}).get(endpoint, 1)

            # 检查上次请求时间，确保不超过限频
            last_time = self.last_request_time.get(f"{market_type}:{endpoint}", 0)
            min_interval = weight / 10  # 基于权重计算最小间隔

            if current_time - last_time < min_interval:
                wait_time = min_interval - (current_time - last_time)
                await asyncio.sleep(wait_time)

            self.last_request_time[f"{market_type}:{endpoint}"] = time.time()
            return True

        except Exception as e:
            logger.error(f"限频检查失败: {e}")
            return False

    async def _make_signed_request(
        self, method: str, endpoint: str, params: Dict = None, market_type: str = "spot"
    ) -> Optional[Dict]:
        """发送签名请求"""
        try:
            if not self.api_key or not self.api_secret:
                raise ValueError("API credentials required for signed requests")

            # 检查限频
            await self._check_rate_limit(market_type, endpoint.split("/")[-1])

            # 准备参数
            params = params or {}
            params["timestamp"] = int(time.time() * 1000)
            params["recvWindow"] = 5000

            # 生成查询字符串和签名
            query_string = urlencode(params)
            signature = self._generate_signature(query_string)
            params["signature"] = signature

            # 选择API基础URL
            base_url = (
                self.rest_api_url if market_type == "spot" else self.futures_api_url
            )
            url = f"{base_url}{endpoint}"

            # 设置请求头
            headers = {
                "X-MBX-APIKEY": self.api_key,
                "Content-Type": "application/x-www-form-urlencoded",
            }

            # 发送请求
            if method.upper() == "GET":
                async with self.session.get(
                    url, params=params, headers=headers
                ) as response:
                    return await self._handle_response(response)
            elif method.upper() == "POST":
                async with self.session.post(
                    url, data=params, headers=headers
                ) as response:
                    return await self._handle_response(response)
            elif method.upper() == "DELETE":
                async with self.session.delete(
                    url, data=params, headers=headers
                ) as response:
                    return await self._handle_response(response)

        except Exception as e:
            logger.error(f"签名请求失败: {e}")
            return None

    async def _handle_response(
        self, response: aiohttp.ClientResponse
    ) -> Optional[Dict]:
        """处理API响应"""
        try:
            # 更新限频信息
            rate_limit_headers = [
                "x-mbx-used-weight",
                "x-mbx-used-weight-1m",
                "x-mbx-order-count-10s",
                "x-mbx-order-count-1m",
            ]

            for header in rate_limit_headers:
                if header in response.headers:
                    self.rate_limits[header] = int(response.headers[header])

            if response.status == 200:
                return await response.json()
            elif response.status == 429:
                logger.warning("API rate limit exceeded")
                # 等待后重试
                await asyncio.sleep(1)
                return None
            else:
                error_data = await response.text()
                logger.error(f"API请求失败: {response.status} - {error_data}")
                return None

        except Exception as e:
            logger.error(f"处理响应失败: {e}")
            return None

    async def place_order(
        self,
        symbol: str,
        side: str,
        order_type: str,
        quantity: Decimal,
        price: Optional[Decimal] = None,
        market_type: str = "spot",
        **kwargs,
    ) -> Optional[Dict]:
        """下单"""
        try:
            endpoint = "/api/v3/order" if market_type == "spot" else "/fapi/v1/order"

            params = {
                "symbol": symbol,
                "side": side.upper(),
                "type": order_type.upper(),
                "quantity": str(quantity),
            }

            # 添加价格（限价单）
            if price is not None:
                params["price"] = str(price)

            # 添加其他参数
            for key, value in kwargs.items():
                if value is not None:
                    params[key] = str(value)

            result = await self._make_signed_request(
                "POST", endpoint, params, market_type
            )

            if result:
                logger.info(f"下单成功: {symbol} {side} {quantity}")
                return result
            else:
                logger.error(f"下单失败: {symbol} {side} {quantity}")
                return None

        except Exception as e:
            logger.error(f"下单异常: {e}")
            return None

    async def cancel_order(
        self,
        symbol: str,
        order_id: Optional[int] = None,
        client_order_id: Optional[str] = None,
        market_type: str = "spot",
    ) -> Optional[Dict]:
        """撤单"""
        try:
            if not order_id and not client_order_id:
                raise ValueError("Must provide either order_id or client_order_id")

            endpoint = "/api/v3/order" if market_type == "spot" else "/fapi/v1/order"

            params = {"symbol": symbol}
            if order_id:
                params["orderId"] = order_id
            if client_order_id:
                params["origClientOrderId"] = client_order_id

            result = await self._make_signed_request(
                "DELETE", endpoint, params, market_type
            )

            if result:
                logger.info(f"撤单成功: {symbol} order_id={order_id}")
                return result
            else:
                logger.error(f"撤单失败: {symbol} order_id={order_id}")
                return None

        except Exception as e:
            logger.error(f"撤单异常: {e}")
            return None

    async def query_order(
        self,
        symbol: str,
        order_id: Optional[int] = None,
        client_order_id: Optional[str] = None,
        market_type: str = "spot",
    ) -> Optional[Dict]:
        """查询订单"""
        try:
            if not order_id and not client_order_id:
                raise ValueError("Must provide either order_id or client_order_id")

            endpoint = "/api/v3/order" if market_type == "spot" else "/fapi/v1/order"

            params = {"symbol": symbol}
            if order_id:
                params["orderId"] = order_id
            if client_order_id:
                params["origClientOrderId"] = client_order_id

            result = await self._make_signed_request(
                "GET", endpoint, params, market_type
            )
            return result

        except Exception as e:
            logger.error(f"查询订单异常: {e}")
            return None

    async def get_open_orders(
        self, symbol: Optional[str] = None, market_type: str = "spot"
    ) -> Optional[List[Dict]]:
        """获取未成交订单"""
        try:
            endpoint = (
                "/api/v3/openOrders" if market_type == "spot" else "/fapi/v1/openOrders"
            )

            params = {}
            if symbol:
                params["symbol"] = symbol

            result = await self._make_signed_request(
                "GET", endpoint, params, market_type
            )
            return result if result else []

        except Exception as e:
            logger.error(f"获取未成交订单异常: {e}")
            return []

    async def get_account_info(self, market_type: str = "spot") -> Optional[Dict]:
        """获取账户信息"""
        try:
            endpoint = (
                "/api/v3/account" if market_type == "spot" else "/fapi/v2/account"
            )

            result = await self._make_signed_request("GET", endpoint, {}, market_type)
            return result

        except Exception as e:
            logger.error(f"获取账户信息异常: {e}")
            return None

    async def get_position_info(
        self, symbol: Optional[str] = None
    ) -> Optional[List[Dict]]:
        """获取持仓信息（期货）"""
        try:
            endpoint = "/fapi/v2/positionRisk"

            params = {}
            if symbol:
                params["symbol"] = symbol

            result = await self._make_signed_request("GET", endpoint, params, "futures")
            return result if result else []

        except Exception as e:
            logger.error(f"获取持仓信息异常: {e}")
            return []

    async def get_exchange_info(self, market_type: str = "spot") -> Optional[Dict]:
        """获取交易所信息"""
        try:
            base_url = (
                self.rest_api_url if market_type == "spot" else self.futures_api_url
            )
            endpoint = (
                "/api/v3/exchangeInfo"
                if market_type == "spot"
                else "/fapi/v1/exchangeInfo"
            )
            url = f"{base_url}{endpoint}"

            async with self.session.get(url) as response:
                if response.status == 200:
                    return await response.json()

        except Exception as e:
            logger.error(f"获取交易所信息失败: {e}")

        return None

    async def get_24hr_ticker(
        self, symbol: Optional[str] = None, market_type: str = "spot"
    ) -> Optional[Union[Dict, List[Dict]]]:
        """获取24小时价格变动统计"""
        try:
            base_url = (
                self.rest_api_url if market_type == "spot" else self.futures_api_url
            )
            endpoint = (
                "/api/v3/ticker/24hr"
                if market_type == "spot"
                else "/fapi/v1/ticker/24hr"
            )
            url = f"{base_url}{endpoint}"

            params = {}
            if symbol:
                params["symbol"] = symbol

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    return await response.json()

        except Exception as e:
            logger.error(f"获取24小时统计失败: {e}")

        return None
