"""
数据同步器模块
实现跨交易所数据时间对齐和质量控制
"""

import asyncio
import logging
import statistics
from collections import deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Tuple

from ..core.base_component import BaseComponent
from ..core.config_manager import ConfigManager

logger = logging.getLogger(__name__)


class DataQuality(Enum):
    """数据质量等级"""

    HIGH = "high"  # <50ms延迟
    MEDIUM = "medium"  # 50-200ms延迟
    LOW = "low"  # 200-500ms延迟
    STALE = "stale"  # >500ms延迟


@dataclass
class SyncedData:
    """同步后的数据"""

    timestamp: datetime
    binance_data: Optional[Dict[str, Any]] = None
    deribit_data: Optional[Dict[str, Any]] = None
    quality: DataQuality = DataQuality.HIGH
    latency_ms: float = 0.0
    interpolated: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DataBuffer:
    """数据缓冲区"""

    exchange: str
    symbol: str
    data: deque = field(default_factory=lambda: deque(maxlen=1000))
    last_update: Optional[datetime] = None

    def add_data(self, timestamp: datetime, data: Dict[str, Any]):
        """添加数据到缓冲区"""
        self.data.append((timestamp, data))
        self.last_update = datetime.now(timezone.utc)

    def get_data_at_time(
        self, target_time: datetime, tolerance_ms: int = 200
    ) -> Optional[Tuple[datetime, Dict[str, Any]]]:
        """获取指定时间附近的数据"""
        tolerance_seconds = tolerance_ms / 1000.0
        best_match = None
        min_diff = float("inf")

        for timestamp, data in self.data:
            diff = abs((target_time - timestamp).total_seconds())
            if diff <= tolerance_seconds and diff < min_diff:
                min_diff = diff
                best_match = (timestamp, data)

        return best_match


@dataclass
class SyncMetrics:
    """同步指标"""

    total_synced: int = 0
    high_quality_count: int = 0
    medium_quality_count: int = 0
    low_quality_count: int = 0
    stale_count: int = 0
    interpolated_count: int = 0
    avg_latency_ms: float = 0.0
    max_latency_ms: float = 0.0
    sync_success_rate: float = 0.0

    @property
    def quality_distribution(self) -> Dict[str, float]:
        """质量分布百分比"""
        total = self.total_synced
        if total == 0:
            return {quality.value: 0.0 for quality in DataQuality}

        return {
            DataQuality.HIGH.value: self.high_quality_count / total * 100,
            DataQuality.MEDIUM.value: self.medium_quality_count / total * 100,
            DataQuality.LOW.value: self.low_quality_count / total * 100,
            DataQuality.STALE.value: self.stale_count / total * 100,
        }


class DataSynchronizer(BaseComponent):
    """
    数据同步器
    负责跨交易所数据的时间对齐和质量控制
    """

    def __init__(self, config_manager: Optional[ConfigManager] = None):
        super().__init__("data_synchronizer")

        # 基础设施组件
        self.config_manager = config_manager

        # 数据缓冲区
        self.buffers: Dict[str, DataBuffer] = {}

        # 同步配置
        self.sync_tolerance_ms = 200  # ±200ms延迟容忍
        self.sync_window_ms = 1000  # 1秒同步窗口
        self.buffer_size = 1000  # 缓冲区大小

        # 自适应参数
        self.adaptive_window = True
        self.min_window_ms = 500
        self.max_window_ms = 2000
        self.window_adjustment_factor = 0.1

        # 数据质量阈值
        self.quality_thresholds = {
            DataQuality.HIGH: 50,  # <50ms
            DataQuality.MEDIUM: 200,  # 50-200ms
            DataQuality.LOW: 500,  # 200-500ms
            DataQuality.STALE: float("inf"),  # >500ms
        }

        # 插值配置
        self.enable_interpolation = True
        self.max_interpolation_gap_ms = 30000  # 30秒最大插值间隔

        # 回调函数
        self.sync_callbacks: List[Callable[[SyncedData], None]] = []
        self.quality_callbacks: List[Callable[[DataQuality, Dict], None]] = []

        # 性能监控
        self.metrics = SyncMetrics()
        self.latency_history = deque(maxlen=1000)

        # 同步任务
        self.sync_task: Optional[asyncio.Task] = None
        self.sync_interval = 0.1  # 100ms同步间隔

    async def _initialize_impl(self) -> None:
        """初始化实现"""
        logger.info("DataSynchronizer初始化完成")

    async def _start_impl(self) -> None:
        """启动实现"""
        # 启动同步任务
        self.sync_task = asyncio.create_task(self._sync_loop())
        logger.info("DataSynchronizer启动成功")

    async def _stop_impl(self) -> None:
        """停止实现"""
        if self.sync_task and not self.sync_task.done():
            self.sync_task.cancel()
            try:
                await self.sync_task
            except asyncio.CancelledError:
                pass
        logger.info("DataSynchronizer已停止")

    async def _health_check_impl(self) -> Dict[str, Any]:
        """健康检查实现"""
        return {
            "buffer_count": len(self.buffers),
            "sync_tolerance_ms": self.sync_tolerance_ms,
            "sync_window_ms": self.sync_window_ms,
            "total_synced": self.metrics.total_synced,
            "avg_latency_ms": self.metrics.avg_latency_ms,
            "sync_success_rate": self.metrics.sync_success_rate,
            "quality_distribution": self.metrics.quality_distribution,
        }

    def register_data_source(self, exchange: str, symbol: str) -> None:
        """注册数据源"""
        buffer_key = f"{exchange}:{symbol}"
        if buffer_key not in self.buffers:
            self.buffers[buffer_key] = DataBuffer(exchange=exchange, symbol=symbol)
            logger.info(f"注册数据源: {buffer_key}")

    def add_data(
        self, exchange: str, symbol: str, timestamp: datetime, data: Dict[str, Any]
    ) -> None:
        """添加数据到缓冲区"""
        buffer_key = f"{exchange}:{symbol}"

        if buffer_key not in self.buffers:
            self.register_data_source(exchange, symbol)

        self.buffers[buffer_key].add_data(timestamp, data)

    async def _sync_loop(self) -> None:
        """同步循环"""
        while self.is_running:
            try:
                await self._perform_sync()
                await asyncio.sleep(self.sync_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"同步循环错误: {e}")
                await asyncio.sleep(1)

    async def _perform_sync(self) -> None:
        """执行数据同步"""
        current_time = datetime.now(timezone.utc)
        # 同步稍早的时间点，给数据到达留出时间
        sync_time = current_time - timedelta(milliseconds=500)  # 500ms前的数据

        # 按symbol分组同步
        symbols = set()
        for buffer_key in self.buffers.keys():
            _, symbol = buffer_key.split(":", 1)
            symbols.add(symbol)

        for symbol in symbols:
            await self._sync_symbol_data(symbol, sync_time)

    async def _sync_symbol_data(self, symbol: str, sync_time: datetime) -> None:
        """同步特定symbol的数据"""
        binance_key = f"binance:{symbol}"
        deribit_key = f"deribit:{symbol}"

        binance_buffer = self.buffers.get(binance_key)
        deribit_buffer = self.buffers.get(deribit_key)

        if not binance_buffer and not deribit_buffer:
            return

        # 获取同步时间点的数据
        binance_data = None
        deribit_data = None
        max_latency = 0.0

        if binance_buffer:
            binance_match = binance_buffer.get_data_at_time(
                sync_time, self.sync_tolerance_ms
            )
            if binance_match:
                timestamp, data = binance_match
                latency = abs((sync_time - timestamp).total_seconds() * 1000)
                max_latency = max(max_latency, latency)
                binance_data = data

        if deribit_buffer:
            deribit_match = deribit_buffer.get_data_at_time(
                sync_time, self.sync_tolerance_ms
            )
            if deribit_match:
                timestamp, data = deribit_match
                latency = abs((sync_time - timestamp).total_seconds() * 1000)
                max_latency = max(max_latency, latency)
                deribit_data = data

        # 如果没有数据，尝试插值
        if not binance_data and not deribit_data:
            if self.enable_interpolation:
                binance_data, deribit_data = await self._interpolate_missing_data(
                    symbol, sync_time, binance_buffer, deribit_buffer
                )
                if binance_data or deribit_data:
                    self.metrics.interpolated_count += 1

        # 如果仍然没有数据，跳过
        if not binance_data and not deribit_data:
            return

        # 确定数据质量
        quality = self._determine_quality(max_latency)

        # 创建同步数据
        synced_data = SyncedData(
            timestamp=sync_time,
            binance_data=binance_data,
            deribit_data=deribit_data,
            quality=quality,
            latency_ms=max_latency,
            interpolated=self.metrics.interpolated_count > 0,
        )

        # 更新指标
        self._update_metrics(synced_data)

        # 自适应窗口调整
        if self.adaptive_window:
            await self._adjust_sync_window(max_latency)

        # 调用回调函数
        await self._notify_callbacks(synced_data)

    def _determine_quality(self, latency_ms: float) -> DataQuality:
        """确定数据质量"""
        if latency_ms < self.quality_thresholds[DataQuality.HIGH]:
            return DataQuality.HIGH
        elif latency_ms < self.quality_thresholds[DataQuality.MEDIUM]:
            return DataQuality.MEDIUM
        elif latency_ms < self.quality_thresholds[DataQuality.LOW]:
            return DataQuality.LOW
        else:
            return DataQuality.STALE

    async def _interpolate_missing_data(
        self,
        symbol: str,
        target_time: datetime,
        binance_buffer: Optional[DataBuffer],
        deribit_buffer: Optional[DataBuffer],
    ) -> Tuple[Optional[Dict], Optional[Dict]]:
        """插值缺失数据"""
        binance_data = None
        deribit_data = None

        if binance_buffer:
            binance_data = await self._interpolate_buffer_data(
                binance_buffer, target_time
            )

        if deribit_buffer:
            deribit_data = await self._interpolate_buffer_data(
                deribit_buffer, target_time
            )

        return binance_data, deribit_data

    async def _interpolate_buffer_data(
        self, buffer: DataBuffer, target_time: datetime
    ) -> Optional[Dict[str, Any]]:
        """插值缓冲区数据"""
        if len(buffer.data) < 2:
            return None

        # 找到目标时间前后的数据点
        before_data = None
        after_data = None

        for timestamp, data in buffer.data:
            if timestamp <= target_time:
                before_data = (timestamp, data)
            elif timestamp > target_time and after_data is None:
                after_data = (timestamp, data)
                break

        if not before_data or not after_data:
            return None

        before_time, before_values = before_data
        after_time, after_values = after_data

        # 检查时间间隔是否在允许范围内
        time_gap = (after_time - before_time).total_seconds() * 1000
        if time_gap > self.max_interpolation_gap_ms:
            return None

        # 线性插值
        ratio = (target_time - before_time).total_seconds() / (
            after_time - before_time
        ).total_seconds()

        interpolated_data = {}
        for key in before_values:
            if key in after_values and isinstance(
                before_values[key], (int, float, Decimal)
            ):
                before_val = float(before_values[key])
                after_val = float(after_values[key])
                interpolated_val = before_val + (after_val - before_val) * ratio
                interpolated_data[key] = interpolated_val
            else:
                # 非数值数据使用最近的值
                interpolated_data[key] = before_values[key]

        return interpolated_data

    async def _adjust_sync_window(self, current_latency: float) -> None:
        """自适应调整同步窗口"""
        if not self.adaptive_window:
            return

        # 基于延迟调整窗口大小
        if current_latency > self.sync_tolerance_ms:
            # 延迟过高，增加窗口
            adjustment = self.sync_window_ms * self.window_adjustment_factor
            self.sync_window_ms = min(
                self.max_window_ms, self.sync_window_ms + adjustment
            )
        elif current_latency < self.sync_tolerance_ms * 0.5:
            # 延迟很低，减少窗口
            adjustment = self.sync_window_ms * self.window_adjustment_factor
            self.sync_window_ms = max(
                self.min_window_ms, self.sync_window_ms - adjustment
            )

    def _update_metrics(self, synced_data: SyncedData) -> None:
        """更新同步指标"""
        self.metrics.total_synced += 1

        # 更新质量计数
        if synced_data.quality == DataQuality.HIGH:
            self.metrics.high_quality_count += 1
        elif synced_data.quality == DataQuality.MEDIUM:
            self.metrics.medium_quality_count += 1
        elif synced_data.quality == DataQuality.LOW:
            self.metrics.low_quality_count += 1
        else:
            self.metrics.stale_count += 1

        # 更新延迟统计
        self.latency_history.append(synced_data.latency_ms)
        if self.latency_history:
            self.metrics.avg_latency_ms = statistics.mean(self.latency_history)
            self.metrics.max_latency_ms = max(self.latency_history)

        # 更新成功率
        success_count = (
            self.metrics.high_quality_count
            + self.metrics.medium_quality_count
            + self.metrics.low_quality_count
        )
        self.metrics.sync_success_rate = success_count / self.metrics.total_synced * 100

    async def _notify_callbacks(self, synced_data: SyncedData) -> None:
        """通知回调函数"""
        # 同步数据回调
        for callback in self.sync_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(synced_data)
                else:
                    callback(synced_data)
            except Exception as e:
                logger.error(f"同步回调执行失败: {e}")

        # 质量告警回调
        if synced_data.quality in [DataQuality.LOW, DataQuality.STALE]:
            quality_info = {
                "symbol": "unknown",  # 可以从metadata中获取
                "latency_ms": synced_data.latency_ms,
                "timestamp": synced_data.timestamp,
            }

            for callback in self.quality_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(synced_data.quality, quality_info)
                    else:
                        callback(synced_data.quality, quality_info)
                except Exception as e:
                    logger.error(f"质量回调执行失败: {e}")

    def add_sync_callback(self, callback: Callable[[SyncedData], None]) -> None:
        """添加同步数据回调"""
        self.sync_callbacks.append(callback)
        logger.info(
            f"添加同步回调: {callback.__name__ if hasattr(callback, '__name__') else 'anonymous'}"
        )

    def add_quality_callback(
        self, callback: Callable[[DataQuality, Dict], None]
    ) -> None:
        """添加数据质量回调"""
        self.quality_callbacks.append(callback)
        logger.info(
            f"添加质量回调: {callback.__name__ if hasattr(callback, '__name__') else 'anonymous'}"
        )

    def remove_sync_callback(self, callback: Callable[[SyncedData], None]) -> None:
        """移除同步数据回调"""
        if callback in self.sync_callbacks:
            self.sync_callbacks.remove(callback)

    def remove_quality_callback(
        self, callback: Callable[[DataQuality, Dict], None]
    ) -> None:
        """移除数据质量回调"""
        if callback in self.quality_callbacks:
            self.quality_callbacks.remove(callback)

    def get_sync_statistics(self) -> Dict[str, Any]:
        """获取同步统计信息"""
        return {
            "metrics": {
                "total_synced": self.metrics.total_synced,
                "avg_latency_ms": self.metrics.avg_latency_ms,
                "max_latency_ms": self.metrics.max_latency_ms,
                "sync_success_rate": self.metrics.sync_success_rate,
                "interpolated_count": self.metrics.interpolated_count,
            },
            "quality_distribution": self.metrics.quality_distribution,
            "configuration": {
                "sync_tolerance_ms": self.sync_tolerance_ms,
                "sync_window_ms": self.sync_window_ms,
                "adaptive_window": self.adaptive_window,
                "enable_interpolation": self.enable_interpolation,
            },
            "buffer_status": {
                buffer_key: {
                    "size": len(buffer.data),
                    "last_update": buffer.last_update.isoformat()
                    if buffer.last_update
                    else None,
                }
                for buffer_key, buffer in self.buffers.items()
            },
        }

    def configure_sync_parameters(
        self,
        tolerance_ms: Optional[int] = None,
        window_ms: Optional[int] = None,
        adaptive: Optional[bool] = None,
        interpolation: Optional[bool] = None,
    ) -> None:
        """配置同步参数"""
        if tolerance_ms is not None:
            self.sync_tolerance_ms = tolerance_ms
            logger.info(f"同步容忍度设置为: {tolerance_ms}ms")

        if window_ms is not None:
            self.sync_window_ms = window_ms
            logger.info(f"同步窗口设置为: {window_ms}ms")

        if adaptive is not None:
            self.adaptive_window = adaptive
            logger.info(f"自适应窗口: {'启用' if adaptive else '禁用'}")

        if interpolation is not None:
            self.enable_interpolation = interpolation
            logger.info(f"数据插值: {'启用' if interpolation else '禁用'}")

    async def force_sync(
        self, symbol: str, timestamp: Optional[datetime] = None
    ) -> Optional[SyncedData]:
        """强制同步指定symbol的数据"""
        if timestamp is None:
            timestamp = datetime.now(timezone.utc)

        await self._sync_symbol_data(symbol, timestamp)

        # 返回最近的同步结果（这里简化处理）
        return None

    def clear_buffers(self, symbol: Optional[str] = None) -> None:
        """清空缓冲区"""
        if symbol:
            # 清空特定symbol的缓冲区
            keys_to_clear = [
                key for key in self.buffers.keys() if key.endswith(f":{symbol}")
            ]
            for key in keys_to_clear:
                self.buffers[key].data.clear()
                logger.info(f"清空缓冲区: {key}")
        else:
            # 清空所有缓冲区
            for buffer in self.buffers.values():
                buffer.data.clear()
            logger.info("清空所有缓冲区")
