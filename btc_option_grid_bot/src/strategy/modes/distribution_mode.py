"""
卖出模式 (DistributionMode)

实现Covered Call策略逻辑，通过卖出OTM Call期权实现期权版分批卖出：
1. 现货持仓检测和Call选择
2. 收益最大化的strike选择算法
3. 行权后的资金释放处理
4. Delta管理和风险控制

核心策略：
- 上涨时针对现货持仓分批卖出OTM Call期权（Delta 0.1-0.2）
- 若被行权：以预设高价卖出BTC现货（实现高位卖出）
- 若不被行权：保留现货同时获得权利金收益
"""

from dataclasses import dataclass, field
from datetime import datetime, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional

from ...core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from ...core.config_manager import ConfigManager
from ...core.event_bus import EventBus
from ...data.cache_manager import CacheManager


@dataclass
class CallOption:
    """Call期权数据"""

    symbol: str
    strike: Decimal
    expiry: datetime
    premium: Decimal
    delta: float
    gamma: float
    theta: float
    vega: float
    iv: float
    volume: int
    open_interest: int
    bid: Decimal
    ask: Decimal
    mark_price: Decimal

    @property
    def bid_ask_spread(self) -> Decimal:
        """买卖价差"""
        return self.ask - self.bid

    @property
    def spread_ratio(self) -> float:
        """价差比例"""
        if self.mark_price > 0:
            return float(self.bid_ask_spread / self.mark_price)
        return 0.0


@dataclass
class DistributionOrder:
    """卖出模式订单"""

    order_id: str
    call_option: CallOption
    size: Decimal
    premium_target: Decimal
    covered_position: Decimal  # 覆盖的现货仓位
    batch_number: int
    created_at: datetime
    status: str = "pending"
    filled_premium: Optional[Decimal] = None
    risk_metrics: Dict[str, float] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DistributionMetrics:
    """卖出模式指标"""

    total_calls_sold: int = 0
    total_premium_collected: Decimal = Decimal("0")
    exercised_calls: int = 0
    expired_worthless: int = 0
    avg_delta: float = 0.0
    avg_premium_rate: float = 0.0
    success_rate: float = 0.0
    total_spot_sold: Decimal = Decimal("0")
    avg_sale_price: Decimal = Decimal("0")
    current_coverage: float = 0.0  # 当前覆盖比例
    error_count: int = 0


class DistributionMode(BaseComponent):
    """
    卖出模式

    核心功能：
    1. Covered Call策略逻辑
    2. 现货持仓检测和Call选择
    3. 收益最大化的strike选择算法
    4. 行权后的资金释放处理
    """

    def __init__(self, config_manager: ConfigManager):
        super().__init__("DistributionMode")
        self.config_manager = config_manager

        # 核心组件依赖
        self.event_bus: Optional[EventBus] = None
        self.cache_manager: Optional[CacheManager] = None

        # 卖出配置
        self.config = {
            "min_spot_position": Decimal("0.1"),  # 最小现货持仓
            "delta_range": [0.1, 0.2],  # Delta目标范围
            "strike_range": [0.05, 0.15],  # Strike相对价格范围（上方）
            "min_premium_rate": 0.015,  # 最小权利金收益率
            "max_coverage_ratio": 0.8,  # 最大覆盖比例
            "batch_sizes": [0.25, 0.30, 0.25, 0.20],  # 分批比例
            "min_expiry_days": 14,  # 最小到期天数
            "max_expiry_days": 45,  # 最大到期天数
            "min_volume": 100,  # 最小成交量
            "min_open_interest": 500,  # 最小持仓量
            "max_bid_ask_spread": 0.05,  # 最大买卖价差
            "exercise_handling": "auto_release",  # 行权处理方式
            "profit_target": 0.8,  # 利润目标（80%权利金）
        }

        # 状态管理
        self.active_orders: Dict[str, DistributionOrder] = {}
        self.metrics = DistributionMetrics()
        self.last_analysis_time: Optional[datetime] = None

        # 加载配置
        self._load_config()

    def _load_config(self):
        """加载配置"""
        try:
            strategy_config = self.config_manager.get_section("distribution_mode")
            if strategy_config:
                self.config.update(strategy_config)
        except Exception:
            # 使用默认配置
            pass

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            if self.logger:
                await self.logger.info("Initializing DistributionMode")

            # 初始化指标
            self.metrics = DistributionMetrics()

            # 清理状态
            self.active_orders.clear()

            if self.logger:
                await self.logger.info("DistributionMode initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"DistributionMode initialization failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            if self.logger:
                await self.logger.info("Starting DistributionMode")

            # 启动成功
            if self.logger:
                await self.logger.info("DistributionMode started successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"DistributionMode start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            if self.logger:
                await self.logger.info("Stopping DistributionMode")

            # 清理资源
            self.active_orders.clear()

            if self.logger:
                await self.logger.info("DistributionMode stopped successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"DistributionMode stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            issues = []

            # 检查配置
            if not self.config:
                issues.append("Configuration not loaded")

            # 检查依赖组件
            if not self.cache_manager:
                issues.append("CacheManager not available")

            # 检查错误率
            if self.metrics.error_count > 10:
                issues.append(f"High error count: {self.metrics.error_count}")

            # 确定健康状态
            if not issues:
                status = HealthStatus.HEALTHY
                message = "DistributionMode is healthy"
            elif len(issues) <= 2:
                status = HealthStatus.DEGRADED
                message = f"DistributionMode has minor issues: {', '.join(issues)}"
            else:
                status = HealthStatus.UNHEALTHY
                message = f"DistributionMode has major issues: {', '.join(issues)}"

            return HealthCheckResult(
                status=status,
                message=message,
                details={
                    "active_orders": len(self.active_orders),
                    "total_calls_sold": self.metrics.total_calls_sold,
                    "success_rate": self.metrics.success_rate,
                    "error_count": self.metrics.error_count,
                },
                timestamp=datetime.now(timezone.utc),
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
                timestamp=datetime.now(timezone.utc),
            )

    async def should_distribute(
        self, current_price: Decimal, market_analysis: Dict[str, Any]
    ) -> bool:
        """判断是否应该执行卖出策略"""
        try:
            # 检查现货持仓
            spot_positions = await self._get_spot_positions()
            if (
                not spot_positions
                or sum(pos.get("size", 0) for pos in spot_positions)
                < self.config["min_spot_position"]
            ):
                return False

            # 检查价格上涨条件
            price_momentum = market_analysis.get("price_momentum", 0)
            if price_momentum < 0.03:  # 至少3%上涨
                return False

            # 检查IV环境
            iv_rank = market_analysis.get("iv_rank", 0)
            if iv_rank < 0.4:  # IV不能太低
                return False

            # 检查技术指标
            rsi = market_analysis.get("rsi", 50)
            if rsi < 60:  # RSI显示超买
                return False

            # 检查覆盖比例
            current_coverage = await self._calculate_current_coverage()
            if current_coverage >= self.config["max_coverage_ratio"]:
                return False

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Distribution condition check failed: {e}")
            self.metrics.error_count += 1
            return False

    async def generate_distribution_orders(
        self, current_price: Decimal, market_analysis: Dict[str, Any]
    ) -> List[DistributionOrder]:
        """生成卖出模式订单"""
        try:
            orders = []

            # 获取现货持仓
            spot_positions = await self._get_spot_positions()
            if not spot_positions:
                return orders

            # 获取可用的Call期权
            call_options = await self._get_available_call_options(current_price)
            if not call_options:
                return orders

            # 计算当前覆盖情况
            current_coverage = await self._calculate_current_coverage()
            available_coverage = self.config["max_coverage_ratio"] - current_coverage

            if available_coverage <= 0:
                return orders

            # 为每个批次生成订单
            total_spot = sum(pos.get("size", 0) for pos in spot_positions)

            for i, batch_size in enumerate(self.config["batch_sizes"]):
                if available_coverage <= 0:
                    break

                # 计算本批次覆盖量
                batch_coverage = min(batch_size, available_coverage)
                batch_spot_size = Decimal(str(total_spot)) * Decimal(
                    str(batch_coverage)
                )

                # 选择最优Call期权
                optimal_call = await self._select_optimal_call(
                    call_options, current_price, market_analysis
                )
                if not optimal_call:
                    continue

                # 创建订单
                order = await self._create_distribution_order(
                    optimal_call, batch_spot_size, i + 1
                )
                if order:
                    orders.append(order)
                    available_coverage -= batch_coverage

            return orders

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Distribution order generation failed: {e}")
            self.metrics.error_count += 1
            return []

    async def handle_exercise(
        self, order: DistributionOrder, exercise_price: Decimal
    ) -> Dict[str, Any]:
        """处理Call期权行权"""
        try:
            if self.logger:
                await self.logger.info(
                    f"Handling Call exercise for order {order.order_id}"
                )

            # 计算行权收益
            spot_sale_proceeds = order.covered_position * exercise_price
            premium_collected = order.filled_premium or order.premium_target
            total_proceeds = spot_sale_proceeds + premium_collected

            # 计算收益率
            original_cost = order.covered_position * (
                exercise_price - premium_collected / order.covered_position
            )
            profit_ratio = (
                (total_proceeds - original_cost) / original_cost
                if original_cost > 0
                else 0
            )

            # 更新指标
            self.metrics.exercised_calls += 1
            self.metrics.total_spot_sold += order.covered_position
            self.metrics.avg_sale_price = (
                self.metrics.avg_sale_price * (self.metrics.exercised_calls - 1)
                + exercise_price
            ) / self.metrics.exercised_calls

            # 释放资金
            released_funds = total_proceeds

            # 决定后续策略
            post_exercise_strategy = await self._determine_post_exercise_strategy(
                released_funds, exercise_price, profit_ratio, market_analysis={}
            )

            result = {
                "order_id": order.order_id,
                "exercise_price": exercise_price,
                "spot_sold": order.covered_position,
                "premium_collected": premium_collected,
                "total_proceeds": total_proceeds,
                "profit_ratio": profit_ratio,
                "released_funds": released_funds,
                "post_strategy": post_exercise_strategy,
            }

            if self.logger:
                await self.logger.info(f"Call exercise handled: {result}")

            return result

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Exercise handling failed: {e}")
            self.metrics.error_count += 1
            return {}

    async def _get_spot_positions(self) -> List[Any]:
        """获取现货持仓"""
        try:
            if not self.cache_manager:
                return []

            # 从缓存获取现货持仓数据
            positions_data = await self.cache_manager.get("spot_positions", "trading")
            if not positions_data:
                return []

            # 过滤BTC持仓
            btc_positions = [
                pos
                for pos in positions_data
                if pos.get("symbol", "").upper() == "BTC" and pos.get("size", 0) > 0
            ]

            return btc_positions

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get spot positions: {e}")
            self.metrics.error_count += 1
            return []

    async def _get_available_call_options(
        self, current_price: Decimal
    ) -> List[CallOption]:
        """获取可用的Call期权"""
        try:
            if not self.cache_manager:
                return []

            # 从缓存获取期权数据
            options_data = await self.cache_manager.get(
                "deribit_options", "market_data"
            )
            if not options_data:
                return []

            call_options = []
            min_strike = current_price * (
                1 + Decimal(str(self.config["strike_range"][0]))
            )
            max_strike = current_price * (
                1 + Decimal(str(self.config["strike_range"][1]))
            )

            for option_data in options_data:
                if (
                    option_data.get("option_type") == "call"
                    and min_strike
                    <= Decimal(str(option_data.get("strike", 0)))
                    <= max_strike
                    and self.config["delta_range"][0]
                    <= option_data.get("delta", 0)
                    <= self.config["delta_range"][1]
                    and option_data.get("volume", 0) >= self.config["min_volume"]
                    and option_data.get("open_interest", 0)
                    >= self.config["min_open_interest"]
                ):
                    call_option = CallOption(
                        symbol=option_data["symbol"],
                        strike=Decimal(str(option_data["strike"])),
                        expiry=datetime.fromisoformat(option_data["expiry"]),
                        premium=Decimal(str(option_data["mark_price"])),
                        delta=option_data["delta"],
                        gamma=option_data.get("gamma", 0),
                        theta=option_data.get("theta", 0),
                        vega=option_data.get("vega", 0),
                        iv=option_data.get("iv", 0),
                        volume=option_data["volume"],
                        open_interest=option_data["open_interest"],
                        bid=Decimal(str(option_data.get("bid", 0))),
                        ask=Decimal(str(option_data.get("ask", 0))),
                        mark_price=Decimal(str(option_data["mark_price"])),
                    )

                    # 检查买卖价差
                    if call_option.spread_ratio <= self.config["max_bid_ask_spread"]:
                        call_options.append(call_option)

            return call_options

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get call options: {e}")
            return []

    async def _select_optimal_call(
        self,
        call_options: List[CallOption],
        current_price: Decimal,
        market_analysis: Dict[str, Any],
    ) -> Optional[CallOption]:
        """选择最优Call期权 - 使用Monte Carlo增强定价"""
        try:
            if not call_options:
                return None

            # 导入Monte Carlo引擎
            from ...analysis.monte_carlo_engine import (
                MarketParameters,
                MonteCarloEngine,
                OptionContract,
                SimulationConfig,
            )

            # 初始化Monte Carlo引擎
            mc_engine = MonteCarloEngine()
            await mc_engine.initialize()

            # 市场参数
            market_params = MarketParameters(
                spot_price=float(current_price),
                risk_free_rate=0.05,  # 5%无风险利率
                volatility=market_analysis.get("implied_volatility", 0.5),
                dividend_yield=0.0,
            )

            # 模拟配置
            sim_config = SimulationConfig(
                num_simulations=5000,  # 适中的模拟次数以平衡精度和性能
                num_steps=50,
                antithetic_variates=True,
                parallel_execution=True,
                num_threads=2,
            )

            # 评分函数 - 集成Monte Carlo定价
            async def score_call_mc(call: CallOption) -> float:
                score = 0.0

                try:
                    # 创建期权合约
                    time_to_expiry = (
                        call.expiry - datetime.now(timezone.utc)
                    ).total_seconds() / (365.25 * 24 * 3600)
                    contract = OptionContract(
                        option_type="call",
                        strike=float(call.strike),
                        time_to_expiry=max(time_to_expiry, 0.001),  # 避免零时间
                        option_side="short",  # 我们在卖出Call
                        quantity=1.0,
                    )

                    # Monte Carlo定价
                    mc_result = await mc_engine.price_option(
                        contract, market_params, sim_config
                    )
                    theoretical_price = mc_result.option_price

                    # 市场价格与理论价格的比较
                    market_price = float(call.premium)
                    pricing_efficiency = (
                        market_price / theoretical_price if theoretical_price > 0 else 0
                    )

                    # 1. 定价效率评分（30%权重） - 市场价格高于理论价格更好（卖方优势）
                    if pricing_efficiency > 1.0:  # 市场价格高估
                        efficiency_score = min(2.0, pricing_efficiency)  # 最高2倍评分
                        score += 30 * (efficiency_score - 1.0)
                    elif pricing_efficiency > 0.8:  # 合理定价
                        score += 30 * 0.5

                    # 2. VaR风险评分（25%权重） - 低VaR风险更好
                    var_95 = abs(mc_result.var_95)
                    max_acceptable_var = float(current_price) * 0.1  # 10%最大VaR
                    if var_95 < max_acceptable_var:
                        var_score = 1.0 - (var_95 / max_acceptable_var)
                        score += 25 * var_score

                    # 3. 权利金收益率评分（20%权重）
                    premium_rate = market_price / float(call.strike)
                    if premium_rate >= self.config["min_premium_rate"]:
                        score += 20 * min(
                            2.0, premium_rate / self.config["min_premium_rate"]
                        )

                    # 4. Delta评分（15%权重）
                    target_delta = (
                        self.config["delta_range"][0] + self.config["delta_range"][1]
                    ) / 2
                    delta_score = 1 - abs(call.delta - target_delta) / target_delta
                    score += 15 * delta_score

                    # 5. 流动性评分（10%权重）
                    volume_score = min(call.volume / 1000, 1.0)
                    oi_score = min(call.open_interest / 5000, 1.0)
                    liquidity_score = (volume_score + oi_score) / 2
                    score += 10 * liquidity_score

                except Exception as e:
                    if self.logger:
                        await self.logger.warning(
                            f"Monte Carlo pricing failed for {call.symbol}: {e}"
                        )
                    # 降级到传统评分
                    premium_rate = float(call.premium / call.strike)
                    if premium_rate >= self.config["min_premium_rate"]:
                        score += 40 * (premium_rate / self.config["min_premium_rate"])

                    target_delta = (
                        self.config["delta_range"][0] + self.config["delta_range"][1]
                    ) / 2
                    delta_score = 1 - abs(call.delta - target_delta) / target_delta
                    score += 30 * delta_score

                    volume_score = min(call.volume / 1000, 1.0)
                    oi_score = min(call.open_interest / 5000, 1.0)
                    liquidity_score = (volume_score + oi_score) / 2
                    score += 20 * liquidity_score

                    spread_score = max(
                        0, 1 - call.spread_ratio / self.config["max_bid_ask_spread"]
                    )
                    score += 10 * spread_score

                return score

            # 为每个期权计算评分
            scored_calls = []
            for call in call_options:
                try:
                    score = await score_call_mc(call)
                    scored_calls.append((call, score))
                except Exception as e:
                    if self.logger:
                        await self.logger.warning(
                            f"Scoring failed for {call.symbol}: {e}"
                        )
                    continue

            if not scored_calls:
                return None

            # 选择评分最高的期权
            best_call, best_score = max(scored_calls, key=lambda x: x[1])

            # 验证最低要求
            premium_rate = float(best_call.premium / best_call.strike)
            if premium_rate < self.config["min_premium_rate"]:
                return None

            if self.logger:
                await self.logger.info(
                    f"Selected optimal call {best_call.symbol} with Monte Carlo score: {best_score:.2f}"
                )

            return best_call

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Monte Carlo call selection failed: {e}")

            # 降级到传统选择方法
            def score_call_traditional(call: CallOption) -> float:
                score = 0.0
                premium_rate = float(call.premium / call.strike)
                if premium_rate >= self.config["min_premium_rate"]:
                    score += 40 * (premium_rate / self.config["min_premium_rate"])

                target_delta = (
                    self.config["delta_range"][0] + self.config["delta_range"][1]
                ) / 2
                delta_score = 1 - abs(call.delta - target_delta) / target_delta
                score += 30 * delta_score

                volume_score = min(call.volume / 1000, 1.0)
                oi_score = min(call.open_interest / 5000, 1.0)
                liquidity_score = (volume_score + oi_score) / 2
                score += 20 * liquidity_score

                spread_score = max(
                    0, 1 - call.spread_ratio / self.config["max_bid_ask_spread"]
                )
                score += 10 * spread_score
                return score

            best_call = max(call_options, key=score_call_traditional)
            premium_rate = float(best_call.premium / best_call.strike)
            if premium_rate < self.config["min_premium_rate"]:
                return None

            return best_call

    async def _calculate_current_coverage(self) -> float:
        """计算当前覆盖比例"""
        try:
            # 获取现货总量
            spot_positions = await self._get_spot_positions()
            total_spot = sum(Decimal(str(pos.get("size", 0))) for pos in spot_positions)

            if total_spot == 0:
                return 0.0

            # 计算已覆盖的现货量
            covered_spot = sum(
                order.covered_position for order in self.active_orders.values()
            )

            return float(covered_spot / total_spot)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Coverage calculation failed: {e}")
            return 0.0

    async def _create_distribution_order(
        self, call: CallOption, spot_size: Decimal, batch_number: int
    ) -> Optional[DistributionOrder]:
        """创建卖出订单"""
        try:
            # 计算订单参数
            order_size = min(
                spot_size, call.strike * Decimal("0.1")
            )  # 限制单笔最大规模
            target_premium = call.premium * Decimal("0.95")  # 稍低于市价

            # 计算预期收益
            expected_pnl = target_premium * order_size / call.strike

            # 计算风险指标
            risk_metrics = {
                "max_opportunity_cost": float(
                    order_size * (call.strike - call.premium)
                ),  # 机会成本
                "break_even": float(call.strike + target_premium),
                "probability_exercise": self._estimate_exercise_probability(call),
                "gamma_risk": abs(call.gamma) * float(order_size),
                "vega_risk": abs(call.vega) * float(order_size),
            }

            # 生成订单ID
            order_id = (
                f"dist_{int(datetime.now(timezone.utc).timestamp())}_{batch_number}"
            )

            # 创建订单
            order = DistributionOrder(
                order_id=order_id,
                call_option=call,
                size=order_size,
                premium_target=target_premium,
                covered_position=order_size,
                batch_number=batch_number,
                created_at=datetime.now(timezone.utc),
                risk_metrics=risk_metrics,
                metadata={
                    "expected_pnl": float(expected_pnl),
                    "premium_rate": float(target_premium / call.strike),
                    "days_to_expiry": (call.expiry - datetime.now(timezone.utc)).days,
                },
            )

            return order

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order creation failed: {e}")
            return None

    def _estimate_exercise_probability(self, call: CallOption) -> float:
        """估算行权概率"""
        try:
            # 基于Delta的简单估算
            # Delta近似等于行权概率
            return max(0.0, min(1.0, call.delta))

        except Exception:
            return 0.5  # 默认50%

    async def _determine_post_exercise_strategy(
        self,
        released_funds: Decimal,
        exercise_price: Decimal,
        profit_ratio: float,
        market_analysis: Dict[str, Any],
    ) -> Dict[str, Any]:
        """确定行权后策略"""
        try:
            strategy = {
                "type": "FUNDS_RELEASED",
                "released_amount": float(released_funds),
                "actions": [],
                "recommendations": [],
            }

            # 基于盈利情况决定策略
            if profit_ratio > 0.15:  # 高盈利（>15%）
                strategy["actions"].append(
                    {
                        "action": "PARTIAL_REINVEST",
                        "amount": float(released_funds * Decimal("0.6")),
                        "reasoning": "高盈利情况下部分资金再投资",
                    }
                )
                strategy["actions"].append(
                    {
                        "action": "PROFIT_TAKING",
                        "amount": float(released_funds * Decimal("0.4")),
                        "reasoning": "锁定部分利润",
                    }
                )
            elif profit_ratio > 0.05:  # 中等盈利（5-15%）
                strategy["actions"].append(
                    {
                        "action": "FULL_REINVEST",
                        "amount": float(released_funds),
                        "reasoning": "中等盈利情况下全部资金再投资",
                    }
                )
            else:  # 低盈利或亏损
                strategy["actions"].append(
                    {
                        "action": "CONSERVATIVE_REINVEST",
                        "amount": float(released_funds * Decimal("0.3")),
                        "reasoning": "低盈利情况下保守再投资",
                    }
                )

            return strategy

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Post-exercise strategy determination failed: {e}"
                )
            return {"type": "HOLD_FUNDS", "actions": []}
