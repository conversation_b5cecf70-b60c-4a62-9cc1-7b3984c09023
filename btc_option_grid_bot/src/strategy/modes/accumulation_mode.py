"""
抄底模式 (AccumulationMode)

实现分批Sell Put策略逻辑，通过卖出OTM Put期权实现期权版分批抄底：
1. Strike价格选择和size计算
2. IV Rank条件判断和入场时机
3. 行权处理和现货转换逻辑
4. Delta管理和风险控制

核心策略：
- 下跌时分批卖出OTM Put期权（Delta 0.1-0.2）
- 若被行权：以预设低价获得BTC现货（实现抄底效果）
- 若不被行权：获得权利金收益（时间价值收益）
"""

from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional

from ...core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from ...core.config_manager import ConfigManager
from ...core.event_bus import BaseEvent, EventBus
from ...data.cache_manager import CacheManager


@dataclass
class PutOption:
    """Put期权信息"""

    symbol: str
    strike: Decimal
    expiry: datetime
    premium: Decimal
    delta: float
    gamma: float
    theta: float
    vega: float
    implied_vol: float
    bid_price: Decimal
    ask_price: Decimal
    volume: int
    open_interest: int


@dataclass
class AccumulationOrder:
    """抄底订单"""

    option: PutOption
    size: Decimal
    target_premium: Decimal
    max_delta: float
    reason: str
    batch_number: int
    expected_pnl: Decimal
    risk_metrics: Dict[str, float]


@dataclass
class AccumulationMetrics:
    """抄底模式指标"""

    total_puts_sold: int = 0
    total_premium_collected: Decimal = Decimal("0")
    exercised_puts: int = 0
    expired_worthless: int = 0
    avg_delta: float = 0.0
    avg_premium_rate: float = 0.0
    success_rate: float = 0.0
    total_spot_acquired: Decimal = Decimal("0")
    avg_acquisition_price: Decimal = Decimal("0")
    current_exposure: Decimal = Decimal("0")
    error_count: int = 0


class AccumulationMode(BaseComponent):
    """
    抄底模式

    核心功能：
    1. 分批Sell Put策略逻辑
    2. Strike价格选择和size计算
    3. IV Rank条件判断和入场时机
    4. 行权处理和现货转换逻辑
    """

    def __init__(self, config_manager: ConfigManager):
        super().__init__("AccumulationMode")
        self.config_manager = config_manager

        # 核心组件依赖
        self.event_bus: Optional[EventBus] = None
        self.cache_manager: Optional[CacheManager] = None

        # 抄底配置
        self.config = {
            "min_iv_rank": 0.7,  # 最小IV排名
            "delta_range": [0.1, 0.2],  # Delta目标范围
            "strike_range": [-0.20, -0.08],  # Strike相对价格范围
            "min_premium_rate": 0.02,  # 最小权利金收益率
            "max_position_size": Decimal("1000"),  # 最大仓位大小
            "batch_sizes": [0.20, 0.25, 0.30, 0.25],  # 分批比例
            "min_expiry_days": 14,  # 最小到期天数
            "max_expiry_days": 45,  # 最大到期天数
            "min_volume": 100,  # 最小成交量
            "min_open_interest": 500,  # 最小持仓量
            "max_bid_ask_spread": 0.05,  # 最大买卖价差
            "exercise_handling": "auto_convert",  # 行权处理方式
        }

        # 抄底指标
        self.accumulation_metrics = AccumulationMetrics()

        # 活跃订单
        self.active_puts: Dict[str, PutOption] = {}
        self.pending_orders: List[AccumulationOrder] = []

        # 批次管理
        self.current_batch = 0
        self.batch_history: List[Dict[str, Any]] = []

        # 价格监控
        self.price_levels: List[Decimal] = []
        self.last_trigger_price: Optional[Decimal] = None
        self.last_order_time: Optional[datetime] = None

    async def _initialize_impl(self) -> bool:
        """具体初始化实现"""
        try:
            # 加载抄底模式配置
            accumulation_config = self.config_manager.get_section(
                "strategies.accumulation_mode"
            )
            if accumulation_config:
                self.config.update(accumulation_config)

            # 初始化价格水平（如果cache_manager可用）
            if self.cache_manager:
                await self._initialize_price_levels()
            else:
                # 如果cache_manager不可用，使用默认价格水平
                self._initialize_default_price_levels()

            if self.logger:
                await self.logger.info(
                    f"AccumulationMode initialized with config: {self.config}"
                )

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to initialize AccumulationMode: {e}")
            return False

    async def _start_impl(self) -> bool:
        """具体启动实现"""
        try:
            if self.logger:
                await self.logger.info("AccumulationMode started successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to start AccumulationMode: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """具体停止实现"""
        try:
            # 清理待处理订单
            self.pending_orders.clear()

            if self.logger:
                await self.logger.info("AccumulationMode stopped successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to stop AccumulationMode: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """具体健康检查实现"""
        try:
            issues = []

            # 检查组件依赖
            if not self.cache_manager:
                issues.append("CacheManager not available")

            # 检查错误率
            if self.accumulation_metrics.error_count > 5:
                issues.append(
                    f"High error count: {self.accumulation_metrics.error_count}"
                )

            # 检查仓位风险
            if (
                self.accumulation_metrics.current_exposure
                > self.config["max_position_size"]
            ):
                issues.append("Position size exceeds limit")

            status = HealthStatus.HEALTHY if not issues else HealthStatus.DEGRADED

            return HealthCheckResult(
                status=status,
                message=f"AccumulationMode active with {len(self.active_puts)} puts",
                details={
                    "active_puts": len(self.active_puts),
                    "total_premium_collected": float(
                        self.accumulation_metrics.total_premium_collected
                    ),
                    "success_rate": self.accumulation_metrics.success_rate,
                    "current_exposure": float(
                        self.accumulation_metrics.current_exposure
                    ),
                    "issues": issues,
                },
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
                details={"error": str(e)},
            )

    def set_dependencies(self, event_bus: EventBus, cache_manager: CacheManager):
        """设置组件依赖"""
        self.event_bus = event_bus
        self.cache_manager = cache_manager

    async def _initialize_price_levels(self):
        """初始化价格水平"""
        try:
            # 获取当前BTC价格
            current_price = await self._get_current_btc_price()
            if not current_price:
                # 如果无法获取价格，使用默认价格水平
                self._initialize_default_price_levels()
                return

            # 根据配置生成价格水平
            strike_range = self.config["strike_range"]
            batch_count = len(self.config["batch_sizes"])

            self.price_levels = []
            for i in range(batch_count):
                # 计算每个批次的价格水平
                ratio = strike_range[0] + (strike_range[1] - strike_range[0]) * i / (
                    batch_count - 1
                )
                price_level = current_price * (1 + ratio)
                self.price_levels.append(price_level)

            if self.logger:
                await self.logger.info(f"Initialized price levels: {self.price_levels}")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to initialize price levels: {e}")
            # 出错时使用默认价格水平
            self._initialize_default_price_levels()

    def _initialize_default_price_levels(self):
        """初始化默认价格水平（当cache_manager不可用时）"""
        try:
            # 使用默认价格50000作为基准
            default_price = Decimal("50000")
            strike_range = self.config["strike_range"]
            batch_count = len(self.config["batch_sizes"])

            self.price_levels = []
            for i in range(batch_count):
                # 计算每个批次的价格水平
                ratio = strike_range[0] + (strike_range[1] - strike_range[0]) * i / (
                    batch_count - 1
                )
                price_level = default_price * (1 + ratio)
                self.price_levels.append(price_level)

        except Exception:
            # 如果出错，使用最基本的默认值
            self.price_levels = [
                Decimal("40000"),
                Decimal("42000"),
                Decimal("44000"),
                Decimal("46000"),
            ]

    async def _get_current_btc_price(self) -> Optional[Decimal]:
        """获取当前BTC价格"""
        try:
            if not self.cache_manager:
                return None

            market_data = await self.cache_manager.get("btc_market_data", "market_data")
            if market_data and "price" in market_data:
                return Decimal(str(market_data["price"]))

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get BTC price: {e}")
            return None

    async def should_accumulate(
        self, current_price: Decimal, market_analysis: Dict[str, Any]
    ) -> bool:
        """判断是否应该执行抄底"""
        try:
            # 检查IV排名
            iv_rank = market_analysis.get("iv_rank", 0.0)
            if iv_rank < self.config["min_iv_rank"]:
                return False

            # 检查价格下跌
            price_momentum = market_analysis.get("price_momentum", 0.0)
            if price_momentum >= 0:
                return False

            # 检查市场状态
            market_state = market_analysis.get("market_state", "sideways")
            if market_state not in ["trending_down", "high_volatility"]:
                return False

            # 检查冷却时间
            if self.last_order_time and datetime.now(
                timezone.utc
            ) - self.last_order_time < timedelta(hours=4):
                return False

            # 检查仓位限制
            if (
                self.accumulation_metrics.current_exposure
                >= self.config["max_position_size"]
            ):
                return False

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Accumulation check failed: {e}")
            return False

    async def generate_accumulation_orders(
        self, market_analysis: Dict[str, Any]
    ) -> List[AccumulationOrder]:
        """生成抄底订单"""
        try:
            orders = []

            # 获取当前价格
            current_price = await self._get_current_btc_price()
            if not current_price:
                return orders

            # 获取可用期权
            available_puts = await self._get_available_puts(current_price)
            if not available_puts:
                return orders

            # 选择最佳期权
            best_puts = await self._select_best_puts(available_puts, current_price)

            # 生成订单
            for i, put_option in enumerate(best_puts):
                if i >= len(self.config["batch_sizes"]):
                    break

                batch_size = self.config["batch_sizes"][i]
                order = await self._create_accumulation_order(
                    put_option, batch_size, i + 1
                )
                if order:
                    orders.append(order)

            return orders

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order generation failed: {e}")
            return []

    async def _get_available_puts(self, current_price: Decimal) -> List[PutOption]:
        """获取可用的Put期权"""
        try:
            if not self.cache_manager:
                return []

            # 获取期权链数据
            option_chain = await self.cache_manager.get(
                "btc_option_chain", "option_chain"
            )
            if not option_chain:
                return []

            available_puts = []
            min_expiry = datetime.now(timezone.utc) + timedelta(
                days=self.config["min_expiry_days"]
            )
            max_expiry = datetime.now(timezone.utc) + timedelta(
                days=self.config["max_expiry_days"]
            )

            for option_data in option_chain:
                try:
                    # 过滤Put期权
                    if option_data.get("option_type") != "put":
                        continue

                    # 检查到期时间
                    expiry = datetime.fromisoformat(option_data.get("expiry", ""))
                    if expiry < min_expiry or expiry > max_expiry:
                        continue

                    # 检查Strike范围 (Put期权应该在当前价格下方)
                    strike = Decimal(str(option_data.get("strike", 0)))
                    if strike >= current_price:  # Put期权Strike必须低于当前价格
                        continue

                    strike_ratio = (
                        strike - current_price
                    ) / current_price  # 这会是负数
                    if not (
                        self.config["strike_range"][0]
                        <= strike_ratio
                        <= self.config["strike_range"][1]
                    ):
                        continue

                    # 检查流动性
                    volume = option_data.get("volume", 0)
                    open_interest = option_data.get("open_interest", 0)
                    if (
                        volume < self.config["min_volume"]
                        or open_interest < self.config["min_open_interest"]
                    ):
                        continue

                    # 检查买卖价差
                    bid_price = Decimal(str(option_data.get("bid_price", 0)))
                    ask_price = Decimal(str(option_data.get("ask_price", 0)))
                    if ask_price > 0:
                        spread = (ask_price - bid_price) / ask_price
                        if spread > self.config["max_bid_ask_spread"]:
                            continue

                    # 创建Put期权对象
                    put_option = PutOption(
                        symbol=option_data.get("symbol", ""),
                        strike=strike,
                        expiry=expiry,
                        premium=bid_price,  # 卖出价格使用bid
                        delta=option_data.get("delta", 0.0),
                        gamma=option_data.get("gamma", 0.0),
                        theta=option_data.get("theta", 0.0),
                        vega=option_data.get("vega", 0.0),
                        implied_vol=option_data.get("implied_vol", 0.0),
                        bid_price=bid_price,
                        ask_price=ask_price,
                        volume=volume,
                        open_interest=open_interest,
                    )

                    available_puts.append(put_option)

                except Exception as e:
                    if self.logger:
                        await self.logger.warning(f"Failed to process option data: {e}")
                    continue

            return available_puts

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get available puts: {e}")
            return []

    async def _select_best_puts(
        self, available_puts: List[PutOption], current_price: Decimal
    ) -> List[PutOption]:
        """选择最佳Put期权"""
        try:
            if not available_puts:
                return []

            # 按Delta范围过滤
            delta_range = self.config["delta_range"]
            filtered_puts = [
                put
                for put in available_puts
                if delta_range[0] <= abs(put.delta) <= delta_range[1]
            ]

            if not filtered_puts:
                return []

            # 计算每个期权的评分
            scored_puts = []
            for put in filtered_puts:
                score = await self._calculate_put_score(put, current_price)
                scored_puts.append((put, score))

            # 按评分排序
            scored_puts.sort(key=lambda x: x[1], reverse=True)

            # 选择前几个最佳期权
            batch_count = len(self.config["batch_sizes"])
            best_puts = [put for put, score in scored_puts[:batch_count]]

            return best_puts

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Put selection failed: {e}")
            return []

    async def _calculate_put_score(
        self, put: PutOption, current_price: Decimal
    ) -> float:
        """计算Put期权评分 - 智能Strike选择算法"""
        try:
            score = 0.0

            # 获取IV排名数据
            iv_rank = await self._get_iv_rank(put.implied_vol)

            # 1. IV排名评分 (30%) - 高IV时期更适合卖出
            if iv_rank >= 0.5:  # IV排名>50%开始计分
                iv_score = min(1.0, (iv_rank - 0.5) * 2)  # 50%以上线性计分
                score += 0.3 * iv_score

            # 2. 风险回报比评分 (25%)
            risk_reward_ratio = await self._calculate_risk_reward_ratio(
                put, current_price
            )
            if risk_reward_ratio > 0:
                rr_score = min(1.0, risk_reward_ratio / 3.0)  # 3:1为满分
                score += 0.25 * rr_score

            # 3. Delta效率评分 (20%) - 基于Kelly Criterion优化
            delta_efficiency = await self._calculate_delta_efficiency(
                put, current_price
            )
            score += 0.2 * delta_efficiency

            # 4. 时间价值衰减优势评分 (15%)
            theta_advantage = await self._calculate_theta_advantage(put)
            score += 0.15 * theta_advantage

            # 5. 流动性与执行概率综合评分 (10%)
            execution_score = await self._calculate_execution_probability(
                put, current_price
            )
            score += 0.1 * execution_score

            return min(1.0, score)  # 确保评分不超过1.0

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Put scoring failed: {e}")
            return 0.0

    async def _get_iv_rank(self, current_iv: float) -> float:
        """计算隐含波动率排名 - IV Rank算法"""
        try:
            if not self.cache_manager:
                return 0.5  # 默认中等排名

            # 获取历史IV数据
            iv_history = await self.cache_manager.get("btc_iv_history", "volatility")
            if not iv_history or len(iv_history) < 30:
                return 0.5

            # 计算过去252天的IV百分位排名
            recent_ivs = [
                float(iv["value"]) for iv in iv_history[-252:] if "value" in iv
            ]
            if len(recent_ivs) < 30:
                return 0.5

            # 计算当前IV在历史分布中的排名
            rank = sum(1 for iv in recent_ivs if iv <= current_iv) / len(recent_ivs)
            return rank

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"IV rank calculation failed: {e}")
            return 0.5

    async def _calculate_risk_reward_ratio(
        self, put: PutOption, current_price: Decimal
    ) -> float:
        """计算风险回报比 - 基于技术分析的支撑位"""
        try:
            # 最大收益 = 权利金
            max_profit = put.premium

            # 估算最大风险 = Strike - Premium - 预期支撑位
            support_level = await self._estimate_support_level(current_price)
            max_loss = put.strike - put.premium - support_level

            if max_loss <= 0:
                return 0.0

            risk_reward_ratio = float(max_profit / max_loss)
            return risk_reward_ratio

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Risk-reward calculation failed: {e}")
            return 0.0

    async def _estimate_support_level(self, current_price: Decimal) -> Decimal:
        """估算技术支撑位 - 基于历史价格分析"""
        try:
            if not self.cache_manager:
                return current_price * Decimal("0.8")  # 默认支撑在-20%

            # 获取历史价格数据
            price_history = await self.cache_manager.get(
                "btc_price_history", "market_data"
            )
            if not price_history:
                return current_price * Decimal("0.8")

            # 支撑位计算：使用30天和90天低点
            recent_30d_lows = [
                Decimal(str(price.get("low", current_price)))
                for price in price_history[-30:]
                if "low" in price
            ]
            recent_90d_lows = [
                Decimal(str(price.get("low", current_price)))
                for price in price_history[-90:]
                if "low" in price
            ]

            support = current_price * Decimal("0.8")  # 默认值

            if recent_30d_lows:
                support_30d = min(recent_30d_lows)
                if recent_90d_lows:
                    support_90d = min(recent_90d_lows)
                    # 加权平均：30天权重70%，90天权重30%
                    support = support_30d * Decimal("0.7") + support_90d * Decimal(
                        "0.3"
                    )
                else:
                    support = support_30d

            # 确保支撑位不低于当前价格的70%
            return max(support, current_price * Decimal("0.7"))

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Support level estimation failed: {e}")
            return current_price * Decimal("0.8")

    async def _calculate_delta_efficiency(
        self, put: PutOption, current_price: Decimal
    ) -> float:
        """计算Delta效率 - Kelly Criterion优化"""
        try:
            # Delta效率 = |Delta| * 时间价值 / 内在价值比例
            abs_delta = abs(put.delta)

            # 计算时间价值比例
            intrinsic_value = max(0, put.strike - current_price)
            time_value = put.premium - intrinsic_value

            if put.premium == 0:
                return 0.0

            time_value_ratio = float(time_value / put.premium)

            # Delta效率：适中的Delta(0.1-0.2)且高时间价值的期权得分更高
            target_delta = 0.15  # 目标Delta
            delta_score = 1.0 - abs(abs_delta - target_delta) / 0.1
            delta_score = max(0.0, delta_score)

            # 综合效率评分
            efficiency = delta_score * time_value_ratio
            return min(1.0, efficiency)

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Delta efficiency calculation failed: {e}")
            return 0.0

    async def _calculate_theta_advantage(self, put: PutOption) -> float:
        """计算Theta优势评分 - 时间价值衰减分析"""
        try:
            # Theta优势 = 日Theta衰减 / 权利金 的比率
            if put.premium == 0 or put.theta == 0:
                return 0.0

            # 计算日收益率
            daily_theta_yield = abs(put.theta) / put.premium

            # 计算到期天数
            days_to_expiry = (put.expiry - datetime.now(timezone.utc)).days
            if days_to_expiry <= 0:
                return 0.0

            # Theta优势评分：日衰减越高得分越高，但要考虑时间风险
            theta_score = min(1.0, daily_theta_yield * 100)  # 1%日衰减为满分

            # 时间风险调整：太接近到期日降低评分
            time_risk_adj = 1.0 if days_to_expiry > 7 else days_to_expiry / 7.0

            return theta_score * time_risk_adj

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Theta advantage calculation failed: {e}")
            return 0.0

    async def _calculate_execution_probability(
        self, put: PutOption, current_price: Decimal
    ) -> float:
        """计算执行概率评分 - 流动性与执行风险综合分析"""
        try:
            # 流动性评分
            volume_score = min(1.0, put.volume / 1000)  # 1000成交量为满分
            oi_score = min(1.0, put.open_interest / 5000)  # 5000 OI为满分
            liquidity_score = (volume_score + oi_score) / 2

            # 买卖价差评分
            if put.ask_price > 0 and put.bid_price > 0:
                spread = (put.ask_price - put.bid_price) / put.ask_price
                spread_score = max(0.0, 1.0 - spread / 0.05)  # 5%价差为0分
            else:
                spread_score = 0.0

            # 概率性执行优势：不太可能被执行的OTM期权得分更高
            moneyness = float(put.strike / current_price)
            if moneyness < 0.9:  # 深度OTM
                execution_advantage = 1.0
            elif moneyness < 0.95:  # 中度OTM
                execution_advantage = 0.8
            else:  # 接近ATM
                execution_advantage = 0.3

            # 综合执行概率评分
            execution_score = (
                liquidity_score * 0.4 + spread_score * 0.3 + execution_advantage * 0.3
            )
            return execution_score

        except Exception as e:
            if self.logger:
                await self.logger.warning(
                    f"Execution probability calculation failed: {e}"
                )
            return 0.0

    async def _create_accumulation_order(
        self, put: PutOption, batch_size: float, batch_number: int
    ) -> Optional[AccumulationOrder]:
        """创建抄底订单"""
        try:
            # 计算订单大小
            max_size = self.config["max_position_size"] * Decimal(str(batch_size))
            order_size = min(max_size, put.strike * Decimal("0.1"))  # 限制单笔最大规模

            # 计算目标权利金
            target_premium = put.premium * Decimal("0.95")  # 稍低于市价

            # 计算预期收益
            expected_pnl = target_premium * order_size / put.strike

            # 计算风险指标
            risk_metrics = {
                "max_loss": float(order_size),  # 最大损失（如果被行权）
                "break_even": float(put.strike - target_premium),
                "probability_profit": self._estimate_profit_probability(put),
                "gamma_risk": abs(put.gamma) * float(order_size),
                "vega_risk": abs(put.vega) * float(order_size),
            }

            order = AccumulationOrder(
                option=put,
                size=order_size,
                target_premium=target_premium,
                max_delta=self.config["delta_range"][1],
                reason=f"Accumulation batch {batch_number}",
                batch_number=batch_number,
                expected_pnl=expected_pnl,
                risk_metrics=risk_metrics,
            )

            return order

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order creation failed: {e}")
            return None

    def _estimate_profit_probability(self, put: PutOption) -> float:
        """估算盈利概率"""
        try:
            # 简化的盈利概率估算
            # 基于Delta和时间价值
            time_decay_factor = (
                max(0.1, put.theta / put.premium) if put.premium > 0 else 0.1
            )
            delta_factor = 1.0 - abs(put.delta)  # Delta越小，盈利概率越高

            probability = delta_factor * 0.7 + time_decay_factor * 0.3
            return max(0.1, min(0.9, probability))

        except Exception:
            return 0.5  # 默认50%概率

    async def handle_exercise(
        self, put_symbol: str, exercise_price: Decimal, size: Decimal
    ):
        """处理期权行权"""
        try:
            # 更新指标
            self.accumulation_metrics.exercised_puts += 1
            self.accumulation_metrics.total_spot_acquired += size

            # 计算平均获得价格
            total_acquired = self.accumulation_metrics.total_spot_acquired
            if total_acquired > 0:
                self.accumulation_metrics.avg_acquisition_price = (
                    self.accumulation_metrics.avg_acquisition_price
                    * (total_acquired - size)
                    + exercise_price * size
                ) / total_acquired

            # 发布行权事件
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type="put_exercised",
                        data={
                            "symbol": put_symbol,
                            "exercise_price": float(exercise_price),
                            "size": float(size),
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                        },
                    )
                )

            if self.logger:
                await self.logger.info(
                    f"Put exercised: {put_symbol} at {exercise_price} for {size} BTC"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Exercise handling failed: {e}")

    async def update_metrics(self):
        """更新抄底指标"""
        try:
            # 计算成功率
            total_puts = self.accumulation_metrics.total_puts_sold
            if total_puts > 0:
                self.accumulation_metrics.success_rate = (
                    self.accumulation_metrics.expired_worthless / total_puts
                )

            # 计算平均Delta
            if self.active_puts:
                total_delta = sum(abs(put.delta) for put in self.active_puts.values())
                self.accumulation_metrics.avg_delta = total_delta / len(
                    self.active_puts
                )

            # 计算平均权利金收益率
            if self.accumulation_metrics.total_puts_sold > 0:
                self.accumulation_metrics.avg_premium_rate = (
                    float(self.accumulation_metrics.total_premium_collected)
                    / self.accumulation_metrics.total_puts_sold
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Metrics update failed: {e}")
