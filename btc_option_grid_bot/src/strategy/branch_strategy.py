"""
分支策略引擎

基于因果信号监听的方向性期权交易策略，集成微观结构信号进行精准出场。
主要功能：
1. 监听CausalEngine的因果信号作为入场触发
2. 集成MicrostructureSignals进行精准出场时机判断
3. 实现方向性期权交易逻辑（Buy OTM Call/Put）
4. 智能止盈机制基于微观结构信号
5. 趋势衰竭检测和自动切换到Iron Condor策略
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Any, Dict, List, Optional

from ..analysis.causal_engine import CausalEngine
from ..analysis.microstructure_signals import MicrostructureSignals, TrendStatus
from ..analysis.types import SignalType
from ..core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from ..core.event_bus import BaseEvent, EventBus
from ..data.cache_manager import CacheManager
from ..gateways.deribit_client import DeribitClient


class DirectionType(Enum):
    """方向类型枚举"""

    BULLISH = "bullish"
    BEARISH = "bearish"
    NEUTRAL = "neutral"


class StrategyState(Enum):
    """策略状态枚举"""

    IDLE = "idle"
    MONITORING = "monitoring"
    POSITION_OPEN = "position_open"
    PROFIT_TAKING = "profit_taking"
    SWITCHING_TO_VOLATILITY = "switching_to_volatility"


@dataclass
class BranchPosition:
    """分支策略持仓信息"""

    instrument_name: str
    direction: DirectionType
    entry_price: float
    quantity: float
    entry_time: datetime
    target_profit: float
    stop_loss: float
    current_pnl: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "instrument_name": self.instrument_name,
            "direction": self.direction.value,
            "entry_price": self.entry_price,
            "quantity": self.quantity,
            "entry_time": self.entry_time.isoformat(),
            "target_profit": self.target_profit,
            "stop_loss": self.stop_loss,
            "current_pnl": self.current_pnl,
        }


@dataclass
class SignalTrigger:
    """信号触发记录"""

    signal_type: SignalType
    direction: DirectionType
    confidence: float
    timestamp: datetime
    processed: bool = False


class BranchStrategy(BaseComponent):
    """
    分支策略引擎

    基于因果信号的方向性期权交易策略，集成微观结构信号进行精准出场。
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(component_name="BranchStrategy", config=config)

        # 配置参数
        self.max_position_size = self.config.get("max_position_size", 0.1)  # BTC
        self.profit_target_ratio = self.config.get(
            "profit_target_ratio", 2.0
        )  # 2倍盈利目标
        self.stop_loss_ratio = self.config.get("stop_loss_ratio", 0.5)  # 50%止损
        self.signal_confidence_threshold = self.config.get(
            "signal_confidence_threshold", 0.7
        )
        self.max_holding_hours = self.config.get("max_holding_hours", 24)

        # OTM期权选择参数
        self.otm_delta_range = self.config.get(
            "otm_delta_range", (0.15, 0.35)
        )  # Delta范围
        self.min_time_to_expiry_days = self.config.get("min_time_to_expiry_days", 7)
        self.max_time_to_expiry_days = self.config.get("max_time_to_expiry_days", 30)

        # 策略状态
        self._strategy_state = StrategyState.IDLE
        self._current_position: Optional[BranchPosition] = None
        self._signal_triggers: List[SignalTrigger] = []

        # 组件依赖
        self.event_bus: Optional[EventBus] = None
        self.causal_engine: Optional[CausalEngine] = None
        self.microstructure_signals: Optional[MicrostructureSignals] = None
        self.cache_manager: Optional[CacheManager] = None
        self.deribit_client: Optional[DeribitClient] = None
        self.position_manager: Optional[Any] = None

        # 监控任务
        self._monitoring_task: Optional[asyncio.Task] = None
        self._position_management_task: Optional[asyncio.Task] = None

        # 性能统计
        self.stats = {
            "total_signals": 0,
            "positions_opened": 0,
            "profitable_exits": 0,
            "stop_loss_exits": 0,
            "structure_breakdown_exits": 0,
            "volatility_strategy_switches": 0,
            "total_pnl": 0.0,
        }

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            # 验证依赖组件
            if not all(
                [
                    self.event_bus,
                    self.causal_engine,
                    self.microstructure_signals,
                    self.cache_manager,
                    self.deribit_client,
                    self.position_manager,
                ]
            ):
                if self.logger:
                    await self.logger.error("Missing required dependencies")
                return False

            # 订阅因果信号事件
            await self.event_bus.subscribe(
                event_types=["causal_signal"],
                callback=self._handle_causal_signal,
                subscriber_id=f"{self.component_name}_causal_listener",
            )

            if self.logger:
                await self.logger.info("BranchStrategy initialized successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to initialize BranchStrategy: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            # 启动监控任务
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            self._position_management_task = asyncio.create_task(
                self._position_management_loop()
            )

            self._strategy_state = StrategyState.MONITORING

            if self.logger:
                await self.logger.info("BranchStrategy started successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to start BranchStrategy: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            # 停止监控任务
            if self._monitoring_task and not self._monitoring_task.done():
                self._monitoring_task.cancel()
                try:
                    await self._monitoring_task
                except asyncio.CancelledError:
                    pass

            if (
                self._position_management_task
                and not self._position_management_task.done()
            ):
                self._position_management_task.cancel()
                try:
                    await self._position_management_task
                except asyncio.CancelledError:
                    pass

            self._strategy_state = StrategyState.IDLE

            if self.logger:
                await self.logger.info("BranchStrategy stopped successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to stop BranchStrategy: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            details = {
                "strategy_state": self._strategy_state.value,
                "current_position": self._current_position.to_dict()
                if self._current_position
                else None,
                "pending_signals": len(self._signal_triggers),
                "monitoring_task_running": self._monitoring_task
                and not self._monitoring_task.done(),
                "position_management_task_running": self._position_management_task
                and not self._position_management_task.done(),
                "stats": self.stats,
            }

            # 检查依赖组件健康状态
            if not all(
                [
                    self.causal_engine and await self.causal_engine.is_healthy(),
                    self.microstructure_signals
                    and await self.microstructure_signals.is_healthy(),
                ]
            ):
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message="Dependency components are unhealthy",
                    details=details,
                    timestamp=datetime.now(timezone.utc),
                )

            # 检查持仓风险
            if self._current_position:
                holding_time = (
                    datetime.now(timezone.utc) - self._current_position.entry_time
                ).total_seconds() / 3600
                if holding_time > self.max_holding_hours:
                    return HealthCheckResult(
                        status=HealthStatus.DEGRADED,
                        message="Position held too long",
                        details=details,
                        timestamp=datetime.now(timezone.utc),
                    )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message="BranchStrategy is healthy",
                details=details,
                timestamp=datetime.now(timezone.utc),
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
                details={"error": str(e)},
                timestamp=datetime.now(timezone.utc),
            )

    def set_dependencies(
        self,
        event_bus: EventBus,
        causal_engine: CausalEngine,
        microstructure_signals: MicrostructureSignals,
        cache_manager: CacheManager,
        deribit_client: DeribitClient,
        position_manager: Any,
    ):
        """设置依赖组件"""
        self.event_bus = event_bus
        self.causal_engine = causal_engine
        self.microstructure_signals = microstructure_signals
        self.cache_manager = cache_manager
        self.deribit_client = deribit_client
        self.position_manager = position_manager

    async def _handle_causal_signal(self, event: BaseEvent):
        """处理因果信号事件"""
        try:
            signal_data = event.metadata
            signal_type = SignalType(signal_data.get("signal_type"))
            confidence = signal_data.get("confidence", 0.0)

            # 检查信号置信度
            if confidence < self.signal_confidence_threshold:
                return

            # 确定交易方向
            direction = self._determine_direction_from_signal(signal_type, signal_data)
            if direction == DirectionType.NEUTRAL:
                return

            # 检查是否已有持仓
            if self._current_position:
                if self.logger:
                    await self.logger.info(
                        f"Ignoring signal {signal_type.value} - position already open"
                    )
                return

            # 记录信号触发
            trigger = SignalTrigger(
                signal_type=signal_type,
                direction=direction,
                confidence=confidence,
                timestamp=datetime.now(timezone.utc),
            )
            self._signal_triggers.append(trigger)
            self.stats["total_signals"] += 1

            if self.logger:
                await self.logger.info(
                    f"Causal signal received: {signal_type.value}, "
                    f"direction: {direction.value}, confidence: {confidence:.3f}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling causal signal: {e}")
            self.metrics.error_count += 1

    def _determine_direction_from_signal(
        self, signal_type: SignalType, signal_data: Dict[str, Any]
    ) -> DirectionType:
        """根据信号类型确定交易方向"""
        if signal_type == SignalType.STRUCTURE_DIVERGENCE:
            # 结构背离信号 - 根据背离方向确定
            divergence_direction = signal_data.get("direction", "neutral")
            if divergence_direction == "bullish":
                return DirectionType.BULLISH
            elif divergence_direction == "bearish":
                return DirectionType.BEARISH

        elif signal_type == SignalType.VOLATILITY_MISMATCH:
            # 波动率错配信号 - 根据错配类型确定
            mismatch_type = signal_data.get("mismatch_type", "neutral")
            if mismatch_type == "underpriced":
                return DirectionType.BULLISH  # 波动率被低估，看涨
            elif mismatch_type == "overpriced":
                return DirectionType.BEARISH  # 波动率被高估，看跌

        elif signal_type == SignalType.GAMMA_LIQUIDATION_OVERLAP:
            # Gamma清算重叠信号 - 根据清算方向确定反向交易
            liquidation_direction = signal_data.get("liquidation_direction", "neutral")
            if liquidation_direction == "long_liquidation":
                return DirectionType.BULLISH  # 多头清算后反弹
            elif liquidation_direction == "short_liquidation":
                return DirectionType.BEARISH  # 空头清算后回调

        return DirectionType.NEUTRAL

    async def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                await asyncio.sleep(5)  # 5秒检查一次

                # 处理待处理的信号
                await self._process_pending_signals()

                # 检查微观结构信号
                await self._check_microstructure_signals()

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Error in monitoring loop: {e}")
                self.metrics.error_count += 1
                await asyncio.sleep(10)

    async def _process_pending_signals(self):
        """处理待处理的信号"""
        if not self._signal_triggers or self._current_position:
            return

        # 获取最新的未处理信号
        unprocessed_signals = [s for s in self._signal_triggers if not s.processed]
        if not unprocessed_signals:
            return

        # 选择置信度最高的信号
        best_signal = max(unprocessed_signals, key=lambda s: s.confidence)

        # 尝试开仓
        success = await self._open_position(best_signal)
        if success:
            best_signal.processed = True
            self._strategy_state = StrategyState.POSITION_OPEN

    async def _open_position(self, signal: SignalTrigger) -> bool:
        """开仓操作"""
        try:
            # 选择合适的OTM期权
            instrument = await self._select_otm_option(signal.direction)
            if not instrument:
                if self.logger:
                    await self.logger.warning(
                        f"No suitable OTM option found for {signal.direction.value}"
                    )
                return False

            # 计算仓位大小
            position_size = await self._calculate_position_size(instrument)
            if position_size <= 0:
                if self.logger:
                    await self.logger.warning("Position size calculation failed")
                return False

            # 获取当前价格
            current_price = await self._get_option_price(instrument["instrument_name"])
            if not current_price:
                return False

            # 计算止盈止损
            target_profit = current_price * self.profit_target_ratio
            stop_loss = current_price * self.stop_loss_ratio

            # 执行开仓
            order_result = await self.deribit_client.create_market_order(
                instrument_name=instrument["instrument_name"],
                side="buy",
                amount=position_size,
            )

            if order_result and order_result.get("order", {}).get("state") == "filled":
                # 记录持仓信息
                order_info = order_result.get("order", {})
                self._current_position = BranchPosition(
                    instrument_name=instrument["instrument_name"],
                    direction=signal.direction,
                    entry_price=order_info.get("average_price", current_price),
                    quantity=position_size,
                    entry_time=datetime.now(timezone.utc),
                    target_profit=target_profit,
                    stop_loss=stop_loss,
                )

                self.stats["positions_opened"] += 1

                if self.logger:
                    await self.logger.info(
                        f"Position opened: {instrument['instrument_name']}, "
                        f"size: {position_size}, price: {self._current_position.entry_price}"
                    )

                return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error opening position: {e}")
            self.metrics.error_count += 1

        return False

    async def _select_otm_option(
        self, direction: DirectionType
    ) -> Optional[Dict[str, Any]]:
        """选择合适的OTM期权"""
        try:
            # 获取期权链数据
            option_chain = await self.deribit_client.get_option_chain("BTC")
            if not option_chain:
                return None

            # 获取当前BTC价格
            btc_price = await self._get_btc_price()
            if not btc_price:
                return None

            # 筛选条件
            now = datetime.now(timezone.utc)
            min_expiry = now + timedelta(days=self.min_time_to_expiry_days)
            max_expiry = now + timedelta(days=self.max_time_to_expiry_days)

            suitable_options = []

            for option in option_chain:
                # 检查到期时间
                expiry_date = datetime.fromisoformat(
                    option["expiration_timestamp"].replace("Z", "+00:00")
                )
                if not (min_expiry <= expiry_date <= max_expiry):
                    continue

                # 检查期权类型和方向
                option_type = option["option_type"]
                if direction == DirectionType.BULLISH and option_type != "call":
                    continue
                if direction == DirectionType.BEARISH and option_type != "put":
                    continue

                # 检查Delta范围（OTM条件）
                greeks = option.get("greeks", {})
                delta = abs(greeks.get("delta", 0))
                if not (self.otm_delta_range[0] <= delta <= self.otm_delta_range[1]):
                    continue

                # 检查流动性
                if option.get("bid_price", 0) <= 0 or option.get("ask_price", 0) <= 0:
                    continue

                suitable_options.append(option)

            if not suitable_options:
                return None

            # 选择Delta最接近中位数的期权
            target_delta = sum(self.otm_delta_range) / 2
            best_option = min(
                suitable_options,
                key=lambda opt: abs(
                    abs(opt.get("greeks", {}).get("delta", 0)) - target_delta
                ),
            )

            return best_option

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error selecting OTM option: {e}")
            return None

    async def _position_management_loop(self):
        """持仓管理循环"""
        while self.is_running:
            try:
                await asyncio.sleep(3)  # 3秒检查一次持仓

                if not self._current_position:
                    continue

                # 更新持仓PnL
                await self._update_position_pnl()

                # 检查出场条件
                exit_reason = await self._check_exit_conditions()
                if exit_reason:
                    await self._close_position(exit_reason)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Error in position management loop: {e}")
                self.metrics.error_count += 1
                await asyncio.sleep(10)

    async def _update_position_pnl(self):
        """更新持仓PnL"""
        if not self._current_position:
            return

        try:
            current_price = await self._get_option_price(
                self._current_position.instrument_name
            )
            if current_price:
                entry_price = self._current_position.entry_price
                quantity = self._current_position.quantity
                self._current_position.current_pnl = (
                    current_price - entry_price
                ) * quantity

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error updating position PnL: {e}")

    async def _check_exit_conditions(self) -> Optional[str]:
        """检查出场条件"""
        if not self._current_position:
            return None

        try:
            current_price = await self._get_option_price(
                self._current_position.instrument_name
            )
            if not current_price:
                return None

            # 1. 止盈检查
            if current_price >= self._current_position.target_profit:
                return "profit_target"

            # 2. 止损检查
            if current_price <= self._current_position.stop_loss:
                return "stop_loss"

            # 3. 微观结构信号检查
            if await self.microstructure_signals.should_exit_due_to_structure_breakdown():
                return "structure_breakdown"

            # 4. 时间止损检查
            holding_time = (
                datetime.now(timezone.utc) - self._current_position.entry_time
            ).total_seconds() / 3600
            if holding_time > self.max_holding_hours:
                return "time_stop"

            # 5. 趋势衰竭检查 - 切换到波动率策略
            if await self.microstructure_signals.should_switch_to_volatility_strategy():
                return "switch_to_volatility"

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error checking exit conditions: {e}")
            return None

    async def _close_position(self, reason: str):
        """平仓操作"""
        if not self._current_position:
            return

        try:
            # 执行平仓
            order_result = await self.deribit_client.create_market_order(
                instrument_name=self._current_position.instrument_name,
                side="sell",
                amount=self._current_position.quantity,
            )

            if order_result and order_result.get("order", {}).get("state") == "filled":
                order_info = order_result.get("order", {})
                exit_price = order_info.get("average_price", 0)
                final_pnl = (
                    exit_price - self._current_position.entry_price
                ) * self._current_position.quantity

                # 更新统计
                self.stats["total_pnl"] += final_pnl

                if reason == "profit_target":
                    self.stats["profitable_exits"] += 1
                elif reason == "stop_loss":
                    self.stats["stop_loss_exits"] += 1
                elif reason == "structure_breakdown":
                    self.stats["structure_breakdown_exits"] += 1
                elif reason == "switch_to_volatility":
                    self.stats["volatility_strategy_switches"] += 1
                    # 触发切换到波动率策略的事件
                    await self._trigger_volatility_strategy_switch()

                if self.logger:
                    await self.logger.info(
                        f"Position closed: {self._current_position.instrument_name}, "
                        f"reason: {reason}, PnL: {final_pnl:.4f}"
                    )

                # 清除持仓
                self._current_position = None
                self._strategy_state = StrategyState.MONITORING

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error closing position: {e}")
            self.metrics.error_count += 1

    async def _trigger_volatility_strategy_switch(self):
        """触发切换到波动率策略"""
        try:
            # 发布切换事件给策略协调器
            switch_event = BaseEvent(
                event_type="strategy_switch_request",
                source=self.component_name,
                data={
                    "from_strategy": "branch",
                    "to_strategy": "volatility",
                    "reason": "trend_exhaustion",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                },
            )

            if self.event_bus:
                await self.event_bus.publish(switch_event)

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error triggering volatility strategy switch: {e}"
                )

    async def _check_microstructure_signals(self):
        """检查微观结构信号"""
        try:
            current_signal = await self.microstructure_signals.get_current_signal()
            if not current_signal:
                return

            # 如果没有持仓且趋势状态健康，可以考虑新的入场机会
            if (
                not self._current_position
                and current_signal.trend_status == TrendStatus.HEALTHY
            ):
                # 这里可以添加基于微观结构信号的额外入场逻辑
                pass

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error checking microstructure signals: {e}")

    # 辅助方法

    async def _get_btc_price(self) -> Optional[float]:
        """获取BTC当前价格"""
        try:
            ticker = await self.deribit_client.get_ticker("BTC-PERPETUAL")
            return ticker.get("last_price") if ticker else None
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error getting BTC price: {e}")
            return None

    async def _get_option_price(self, instrument_name: str) -> Optional[float]:
        """获取期权当前价格"""
        try:
            ticker = await self.deribit_client.get_ticker(instrument_name)
            if ticker:
                bid = ticker.get("bid_price", 0)
                ask = ticker.get("ask_price", 0)
                if bid > 0 and ask > 0:
                    return (bid + ask) / 2
            return None
        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error getting option price for {instrument_name}: {e}"
                )
            return None

    async def _calculate_position_size(self, instrument: Dict[str, Any]) -> float:
        """计算仓位大小"""
        try:
            # 基于最大仓位限制和期权价格计算
            option_price = await self._get_option_price(instrument["instrument_name"])
            if not option_price or option_price <= 0:
                return 0.0

            # 计算最大可买数量
            max_contracts = self.max_position_size / option_price

            # 考虑风险调整
            risk_adjusted_size = max_contracts * 0.8  # 80%的风险调整

            # 最小单位调整
            min_contract_size = instrument.get("min_trade_amount", 0.1)
            adjusted_size = max(min_contract_size, risk_adjusted_size)

            return round(adjusted_size, 1)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error calculating position size: {e}")
            return 0.0

    # 公共接口方法

    async def get_current_position(self) -> Optional[BranchPosition]:
        """获取当前持仓"""
        return self._current_position

    async def get_strategy_stats(self) -> Dict[str, Any]:
        """获取策略统计"""
        stats = self.stats.copy()
        stats["strategy_state"] = self._strategy_state.value
        stats["current_position"] = (
            self._current_position.to_dict() if self._current_position else None
        )
        stats["pending_signals"] = len(
            [s for s in self._signal_triggers if not s.processed]
        )

        # 计算胜率
        total_exits = (
            stats["profitable_exits"]
            + stats["stop_loss_exits"]
            + stats["structure_breakdown_exits"]
        )
        stats["win_rate"] = (
            stats["profitable_exits"] / total_exits if total_exits > 0 else 0.0
        )

        return stats

    async def force_close_position(self, reason: str = "manual") -> bool:
        """强制平仓"""
        if not self._current_position:
            return False

        await self._close_position(reason)
        return True
