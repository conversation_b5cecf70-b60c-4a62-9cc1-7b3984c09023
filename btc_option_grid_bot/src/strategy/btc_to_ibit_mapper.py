from __future__ import annotations
"""
BTC→IBIT 信号映射模块（MVP）
- 输入：BTC 侧信号（价格、目标 delta、期限、方向）、IBIT 实时报价（由执行层提供）
- 输出：IBIT 可交易建议（目标期权合约或现货指令）
- 说明：此为逻辑骨架 + 基本校验，便于单元测试；后续可增强回归校准和误差带管理
"""

from dataclasses import dataclass
from decimal import Decimal
from typing import Optional
from datetime import date

from ..gateways.ibkr_client import OptionContract, Right


@dataclass
class BTCSideSignal:
    direction: str           # 'bullish' / 'bearish' / 'neutral'
    target_delta: float      # 0.6~0.8 for call 等
    tenor_days: int          # 30~90
    btc_price: Decimal       # 最新 BTCUSD


@dataclass
class IBITMappingParams:
    a: Decimal               # 回归常数项
    b: Decimal               # 回归斜率
    tracking_error_band_bps: int = 300  # 允许的误差带（bps）


@dataclass
class MappingOutput:
    action: str              # 'LONG_CALL' / 'SELL_PUT_CSP' / 'BUY_SPOT' / 'SKIP'
    option: Optional[OptionContract] = None
    spot_qty: Optional[int] = None


class BTCToIBITMapper:
    def __init__(self, params: IBITMappingParams):
        self.params = params

    def estimate_ibit_price(self, btc_price: Decimal) -> Decimal:
        return self.params.a + self.params.b * btc_price

    def within_band(self, ibit_est: Decimal, ibit_quote: Decimal) -> bool:
        if ibit_est <= 0:
            return False
        diff = abs(ibit_quote - ibit_est) / ibit_est
        return diff <= Decimal(self.params.tracking_error_band_bps) / Decimal(10000)

    def pick_option(self, underlying: str, expiry: date, strike: Decimal, right: Right) -> OptionContract:
        return OptionContract(underlying=underlying, expiry=expiry, strike=strike, right=right)

    def map_to_ibit(self, sig: BTCSideSignal, ibit_quote: Decimal, next_expiry: date) -> MappingOutput:
        ibit_est = self.estimate_ibit_price(sig.btc_price)
        if not self.within_band(ibit_est, ibit_quote):
            return MappingOutput(action="SKIP")

        # 简化：bullish → Long Call；bearish/neutral → CSP，实际可按策略参数切换
        if sig.direction == "bullish":
            # strike 取略高于现价（近似 delta 0.6~0.8 的范围，MVP 简化）
            strike = (ibit_quote * Decimal("1.05")).quantize(Decimal("0.01"))
            return MappingOutput(
                action="LONG_CALL",
                option=self.pick_option("IBIT", next_expiry, strike, Right.CALL),
            )
        else:
            strike = (ibit_quote * Decimal("0.90")).quantize(Decimal("0.01"))
            return MappingOutput(
                action="SELL_PUT_CSP",
                option=self.pick_option("IBIT", next_expiry, strike, Right.PUT),
            )

