"""
期权网格主策略

实现期权版分批抄底/卖出策略，通过三种交易模式的智能切换：
1. 抄底模式 (AccumulationMode) - 分批卖出OTM Put期权
2. 卖出模式 (DistributionMode) - 分批卖出OTM Call期权
3. 震荡模式 (SidewaysMode) - Short Strangle/Iron Condor策略

核心功能：
- 三种交易模式的切换逻辑
- 资金分配和仓位管理
- 风险检查和止损机制
- 策略性能评估和优化
"""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

import numpy as np

from ..analysis.causal_engine import CausalEngine
from ..core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from ..core.config_manager import ConfigManager
from ..core.event_bus import BaseEvent, EventBus
from ..data.cache_manager import CacheManager
from .modes.accumulation_mode import AccumulationMode
from .modes.distribution_mode import DistributionMode
from .modes.sideways_mode import SidewaysMode


class StrategyMode(Enum):
    """策略模式"""

    ACCUMULATION = "accumulation"  # 抄底模式
    DISTRIBUTION = "distribution"  # 卖出模式
    SIDEWAYS = "sideways"  # 震荡模式


class MarketState(Enum):
    """市场状态"""

    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"


@dataclass
class MarketAnalysis:
    """市场分析结果"""

    price_momentum: float  # 价格动量 (-1 到 1)
    iv_rank: float  # IV排名 (0 到 1)
    funding_rate: float  # 资金费率
    volume_profile: float  # 成交量分布
    technical_signals: Dict[str, float]  # 技术指标
    market_state: MarketState
    confidence: float  # 分析置信度
    timestamp: datetime


@dataclass
class StrategyMetrics:
    """策略指标"""

    total_trades: int = 0
    successful_trades: int = 0
    total_pnl: Decimal = Decimal("0")
    max_drawdown: Decimal = Decimal("0")
    sharpe_ratio: float = 0.0
    win_rate: float = 0.0
    avg_trade_duration: float = 0.0
    current_mode: Optional[StrategyMode] = None
    mode_switch_count: int = 0
    last_trade_time: Optional[datetime] = None
    error_count: int = 0


@dataclass
class PositionInfo:
    """仓位信息"""

    symbol: str
    position_type: str  # 'put', 'call', 'spot'
    size: Decimal
    entry_price: Decimal
    current_price: Decimal
    unrealized_pnl: Decimal
    delta: float
    gamma: float
    theta: float
    vega: float
    expiry: datetime
    strike: Optional[Decimal] = None


@dataclass
class OrderRequest:
    """订单请求"""

    symbol: str
    side: str  # 'buy', 'sell'
    order_type: str  # 'market', 'limit'
    size: Decimal
    price: Optional[Decimal] = None
    strike: Optional[Decimal] = None
    expiry: Optional[datetime] = None
    option_type: Optional[str] = None  # 'call', 'put'
    reason: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)


class OptionGridStrategy(BaseComponent):
    """
    期权网格主策略

    核心功能：
    1. 三种交易模式的切换逻辑
    2. 资金分配和仓位管理
    3. 风险检查和止损机制
    4. 策略性能评估和优化
    """

    def __init__(self, config_manager: ConfigManager):
        super().__init__("OptionGridStrategy")
        self.config_manager = config_manager

        # 核心组件依赖
        self.event_bus: Optional[EventBus] = None
        self.cache_manager: Optional[CacheManager] = None
        self.causal_engine: Optional[CausalEngine] = None

        # 策略状态
        self.current_mode: Optional[StrategyMode] = None
        self.target_mode: Optional[StrategyMode] = None
        self.mode_transition_time: Optional[datetime] = None

        # 策略配置
        self.config = {
            "capital_allocation": Decimal("0.70"),  # 主策略资金分配比例
            "batch_count": 5,  # 分批数量
            "batch_sizes": [0.10, 0.15, 0.20, 0.25, 0.30],  # 递进式仓位配置
            "delta_range": [0.1, 0.2],  # Delta目标范围
            "iv_threshold": 0.7,  # IV阈值
            "mode_switch_cooldown": 1800,  # 模式切换冷却时间(秒)
            "risk_check_interval": 30,  # 风险检查间隔(秒)
            "performance_review_interval": 3600,  # 性能评估间隔(秒)
        }

        # 策略指标
        self.strategy_metrics = StrategyMetrics()

        # 仓位管理
        self.positions: Dict[str, PositionInfo] = {}
        self.pending_orders: List[OrderRequest] = []

        # 市场分析缓存
        self.market_analysis_cache: Optional[MarketAnalysis] = None
        self.last_analysis_time: Optional[datetime] = None

        # 任务管理
        self._strategy_task: Optional[asyncio.Task] = None
        self._risk_monitor_task: Optional[asyncio.Task] = None
        self._performance_task: Optional[asyncio.Task] = None

        # 策略模式实例
        self.accumulation_mode = AccumulationMode(config_manager)
        self.distribution_mode = DistributionMode(config_manager)
        self.sideways_mode = SidewaysMode(config_manager)

    async def _initialize_impl(self) -> bool:
        """具体初始化实现"""
        try:
            # 加载策略配置
            strategy_config = self.config_manager.get_section("strategies.option_grid")
            if strategy_config:
                self.config.update(strategy_config)

            # 初始化策略模式实例
            await self._initialize_mode_instances()

            # 初始化模式为震荡模式
            self.current_mode = StrategyMode.SIDEWAYS
            self.strategy_metrics.current_mode = self.current_mode

            if self.logger:
                await self.logger.info(
                    f"OptionGridStrategy initialized with config: {self.config}"
                )

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to initialize OptionGridStrategy: {e}")
            return False

    async def _initialize_mode_instances(self) -> None:
        """初始化策略模式实例"""
        try:
            # 为模式实例注入依赖
            for mode in [
                self.accumulation_mode,
                self.distribution_mode,
                self.sideways_mode,
            ]:
                mode.cache_manager = self.cache_manager
                mode.logger = self.logger

                # 初始化模式实例
                if not await mode.initialize():
                    raise RuntimeError(f"Failed to initialize {mode.component_name}")

                # 启动模式实例
                if not await mode.start():
                    raise RuntimeError(f"Failed to start {mode.component_name}")

            if self.logger:
                await self.logger.info(
                    "All strategy mode instances initialized successfully"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to initialize mode instances: {e}")
            raise

    async def _start_impl(self) -> bool:
        """具体启动实现"""
        try:
            # 启动策略主循环
            self._strategy_task = asyncio.create_task(self._strategy_loop())

            # 启动风险监控
            self._risk_monitor_task = asyncio.create_task(self._risk_monitor_loop())

            # 启动性能评估
            self._performance_task = asyncio.create_task(self._performance_loop())

            if self.logger:
                await self.logger.info("OptionGridStrategy started successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to start OptionGridStrategy: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """具体停止实现"""
        try:
            # 停止所有任务
            tasks = [
                self._strategy_task,
                self._risk_monitor_task,
                self._performance_task,
            ]
            for task in tasks:
                if task and not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass

            if self.logger:
                await self.logger.info("OptionGridStrategy stopped successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to stop OptionGridStrategy: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """具体健康检查实现"""
        try:
            issues = []

            # 检查任务状态
            if not self._strategy_task or self._strategy_task.done():
                issues.append("Strategy task not running")

            if not self._risk_monitor_task or self._risk_monitor_task.done():
                issues.append("Risk monitor task not running")

            # 检查组件依赖
            if not self.cache_manager:
                issues.append("CacheManager not available")

            if not self.causal_engine:
                issues.append("CausalEngine not available")

            # 检查错误率
            if self.strategy_metrics.error_count > 10:
                issues.append(f"High error count: {self.strategy_metrics.error_count}")

            status = HealthStatus.HEALTHY if not issues else HealthStatus.DEGRADED

            return HealthCheckResult(
                status=status,
                message=f"Strategy running in {self.current_mode.value if self.current_mode else 'unknown'} mode",
                details={
                    "current_mode": self.current_mode.value
                    if self.current_mode
                    else None,
                    "total_trades": self.strategy_metrics.total_trades,
                    "win_rate": self.strategy_metrics.win_rate,
                    "error_count": self.strategy_metrics.error_count,
                    "issues": issues,
                },
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
                details={"error": str(e)},
            )

    def set_dependencies(
        self,
        event_bus: EventBus,
        cache_manager: CacheManager,
        causal_engine: CausalEngine,
    ):
        """设置组件依赖"""
        self.event_bus = event_bus
        self.cache_manager = cache_manager
        self.causal_engine = causal_engine

    async def _strategy_loop(self):
        """策略主循环"""
        while self.is_running:
            try:
                # 市场分析
                market_analysis = await self._analyze_market()

                # 模式选择
                target_mode = await self._select_mode(market_analysis)

                # 模式切换
                if target_mode != self.current_mode:
                    await self._switch_mode(target_mode, market_analysis)

                # 执行策略逻辑
                await self._execute_strategy_logic(market_analysis)

                # 等待下一次执行
                await asyncio.sleep(60)  # 1分钟执行一次

            except Exception as e:
                self.strategy_metrics.error_count += 1
                if self.logger:
                    await self.logger.error(f"Strategy loop error: {e}")
                await asyncio.sleep(30)  # 错误后等待30秒

    async def _analyze_market(self) -> MarketAnalysis:
        """分析市场状态"""
        try:
            # 检查缓存
            if (
                self.market_analysis_cache
                and self.last_analysis_time
                and datetime.now(timezone.utc) - self.last_analysis_time
                < timedelta(minutes=5)
            ):
                return self.market_analysis_cache

            # 获取市场数据
            market_data = await self._get_market_data()

            # 计算价格动量
            price_momentum = await self._calculate_price_momentum(market_data)

            # 计算IV排名
            iv_rank = await self._calculate_iv_rank(market_data)

            # 获取资金费率
            funding_rate = market_data.get("funding_rate", 0.0)

            # 分析成交量分布
            volume_profile = await self._analyze_volume_profile(market_data)

            # 技术指标分析
            technical_signals = await self._analyze_technical_signals(market_data)

            # 确定市场状态
            market_state = await self._determine_market_state(
                price_momentum, iv_rank, funding_rate, technical_signals
            )

            # 计算分析置信度
            confidence = await self._calculate_analysis_confidence(market_data)

            # 创建市场分析结果
            analysis = MarketAnalysis(
                price_momentum=price_momentum,
                iv_rank=iv_rank,
                funding_rate=funding_rate,
                volume_profile=volume_profile,
                technical_signals=technical_signals,
                market_state=market_state,
                confidence=confidence,
                timestamp=datetime.now(timezone.utc),
            )

            # 更新缓存
            self.market_analysis_cache = analysis
            self.last_analysis_time = analysis.timestamp

            return analysis

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Market analysis failed: {e}")

            # 返回默认分析结果
            return MarketAnalysis(
                price_momentum=0.0,
                iv_rank=0.1,  # 错误情况下使用低IV排名
                funding_rate=0.0,
                volume_profile=0.5,
                technical_signals={},
                market_state=MarketState.SIDEWAYS,
                confidence=0.1,
                timestamp=datetime.now(timezone.utc),
            )

    async def _get_market_data(self) -> Dict[str, Any]:
        """获取市场数据"""
        try:
            market_data = {}

            if self.cache_manager:
                # 获取BTC价格数据
                btc_data = await self.cache_manager.get(
                    "btc_market_data", "market_data"
                )
                if btc_data:
                    market_data.update(btc_data)

                # 获取期权数据
                option_data = await self.cache_manager.get(
                    "btc_option_chain", "option_chain"
                )
                if option_data:
                    market_data["option_chain"] = option_data

                # 获取Greeks数据
                greeks_data = await self.cache_manager.get("btc_greeks", "greeks")
                if greeks_data:
                    market_data["greeks"] = greeks_data

                # 获取历史数据
                price_history = await self.cache_manager.get(
                    "btc_price_history", "market_data"
                )
                if price_history:
                    market_data["price_history"] = price_history

                iv_history = await self.cache_manager.get(
                    "btc_iv_history", "market_data"
                )
                if iv_history:
                    market_data["iv_history"] = iv_history

                funding_rate = await self.cache_manager.get(
                    "btc_funding_rate", "market_data"
                )
                if funding_rate:
                    market_data["funding_rate"] = funding_rate.get("rate", 0.0)

                volume_profile = await self.cache_manager.get(
                    "btc_volume_profile", "market_data"
                )
                if volume_profile:
                    market_data["volume_profile"] = volume_profile.get("profile", 0.5)

            return market_data

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get market data: {e}")
            return {}

    async def _calculate_price_momentum(self, market_data: Dict[str, Any]) -> float:
        """计算价格动量"""
        try:
            # 获取价格历史
            price_history = market_data.get("price_history", [])
            if len(price_history) < 20:
                return 0.0

            # 计算短期和长期移动平均
            short_ma = np.mean(price_history[-5:])
            long_ma = np.mean(price_history[-20:])

            # 计算动量 (-1 到 1)
            if long_ma > 0:
                momentum = (short_ma - long_ma) / long_ma
                return max(-1.0, min(1.0, momentum * 10))  # 放大并限制范围

            return 0.0

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Price momentum calculation failed: {e}")
            return 0.0

    async def _calculate_iv_rank(self, market_data: Dict[str, Any]) -> float:
        """计算IV排名"""
        try:
            # 获取当前IV和历史IV
            current_iv = market_data.get("current_iv", 0.0)
            iv_history = market_data.get("iv_history", [])

            if not iv_history or len(iv_history) < 30:
                return 0.5  # 默认中等水平

            # 计算IV排名 (当前IV在历史中的百分位)
            # 计算当前IV在历史数据中的排名
            rank = sum(1 for iv in iv_history if iv <= current_iv) / len(iv_history)
            return max(0.0, min(1.0, float(rank)))

        except Exception as e:
            if self.logger:
                await self.logger.error(f"IV rank calculation failed: {e}")
            return 0.5

    async def _analyze_volume_profile(self, market_data: Dict[str, Any]) -> float:
        """分析成交量分布"""
        try:
            volume_data = market_data.get("volume_profile", {})
            if not volume_data:
                return 0.5

            # 分析成交量集中度
            total_volume = sum(volume_data.values())
            if total_volume == 0:
                return 0.5

            # 计算成交量分布的集中度
            max_volume = max(volume_data.values())
            concentration = max_volume / total_volume

            return max(0.0, min(1.0, concentration))

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Volume profile analysis failed: {e}")
            return 0.5

    async def _analyze_technical_signals(
        self, market_data: Dict[str, Any]
    ) -> Dict[str, float]:
        """分析技术指标"""
        try:
            signals = {}

            # RSI指标
            rsi = market_data.get("rsi", 50.0)
            signals["rsi"] = (rsi - 50) / 50.0  # 标准化到 -1 到 1

            # MACD指标
            macd = market_data.get("macd", 0.0)
            signals["macd"] = max(-1.0, min(1.0, macd))

            # 布林带位置
            bb_position = market_data.get("bollinger_position", 0.5)
            signals["bollinger"] = (bb_position - 0.5) * 2  # 转换到 -1 到 1

            return signals

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Technical signals analysis failed: {e}")
            return {}

    async def _determine_market_state(
        self,
        price_momentum: float,
        iv_rank: float,
        funding_rate: float,
        technical_signals: Dict[str, float],
    ) -> MarketState:
        """确定市场状态"""
        try:
            # 高波动率状态
            if iv_rank > 0.8:
                return MarketState.HIGH_VOLATILITY

            # 趋势状态判断
            trend_signals = [
                price_momentum,
                technical_signals.get("macd", 0.0),
                technical_signals.get("rsi", 0.0),
            ]

            avg_trend = np.mean(trend_signals)

            if avg_trend > 0.3:
                return MarketState.TRENDING_UP
            elif avg_trend < -0.3:
                return MarketState.TRENDING_DOWN
            else:
                return MarketState.SIDEWAYS

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Market state determination failed: {e}")
            return MarketState.SIDEWAYS

    async def _calculate_analysis_confidence(
        self, market_data: Dict[str, Any]
    ) -> float:
        """计算分析置信度"""
        try:
            confidence_factors = []

            # 数据完整性
            required_fields = ["price_history", "current_iv", "volume_profile"]
            data_completeness = sum(
                1 for field in required_fields if field in market_data
            ) / len(required_fields)
            if data_completeness > 0:  # 只有当有数据时才添加到confidence_factors
                confidence_factors.append(data_completeness)

            # 数据新鲜度
            last_update = market_data.get("last_update")
            if last_update:
                age_minutes = (
                    datetime.now(timezone.utc) - last_update
                ).total_seconds() / 60
                freshness = max(0.0, 1.0 - age_minutes / 60)  # 1小时内为满分
                confidence_factors.append(freshness)

            # 市场活跃度
            volume = market_data.get("volume", 0)
            if volume > 0:
                # 假设正常成交量为基准
                activity = min(1.0, volume / 1000000)  # 100万为满分
                confidence_factors.append(activity)

            if confidence_factors:
                result = np.mean(confidence_factors)
                return float(result) if not np.isnan(result) else 0.1
            else:
                return 0.1

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Confidence calculation failed: {e}")
            return 0.1  # 错误情况下返回低置信度

    async def _select_mode(self, market_analysis: MarketAnalysis) -> StrategyMode:
        """选择策略模式"""
        try:
            iv_rank = market_analysis.iv_rank
            market_state = market_analysis.market_state

            # 获取当前仓位信息
            has_spot_position = await self._has_spot_position()

            # 模式选择逻辑
            if market_state == MarketState.HIGH_VOLATILITY:
                # 高波动率时优先震荡模式
                return StrategyMode.SIDEWAYS

            elif market_state == MarketState.TRENDING_DOWN:
                # 下跌趋势 + 高IV，选择抄底模式
                if iv_rank > self.config["iv_threshold"]:
                    return StrategyMode.ACCUMULATION
                else:
                    return StrategyMode.SIDEWAYS

            elif market_state == MarketState.TRENDING_UP:
                # 上涨趋势 + 有现货仓位，选择卖出模式
                if has_spot_position:
                    return StrategyMode.DISTRIBUTION
                else:
                    return StrategyMode.SIDEWAYS

            else:
                # 震荡市场，选择震荡模式
                return StrategyMode.SIDEWAYS

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Mode selection failed: {e}")
            return StrategyMode.SIDEWAYS

    async def _has_spot_position(self) -> bool:
        """检查是否有现货仓位"""
        try:
            for position in self.positions.values():
                if position.position_type == "spot" and position.size > 0:
                    return True
            return False
        except Exception:
            return False

    async def _switch_mode(
        self, target_mode: StrategyMode, market_analysis: MarketAnalysis
    ):
        """切换策略模式"""
        try:
            # 检查冷却时间
            if self.mode_transition_time and datetime.now(
                timezone.utc
            ) - self.mode_transition_time < timedelta(
                seconds=self.config["mode_switch_cooldown"]
            ):
                return

            old_mode = self.current_mode

            # 执行模式切换前的清理
            await self._cleanup_mode(old_mode)

            # 切换模式
            self.current_mode = target_mode
            self.target_mode = target_mode
            self.mode_transition_time = datetime.now(timezone.utc)

            # 更新指标
            self.strategy_metrics.current_mode = target_mode
            self.strategy_metrics.mode_switch_count += 1

            # 发布模式切换事件
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type="strategy_mode_switch",
                        data={
                            "old_mode": old_mode.value if old_mode else None,
                            "new_mode": target_mode.value,
                            "market_analysis": {
                                "price_momentum": market_analysis.price_momentum,
                                "iv_rank": market_analysis.iv_rank,
                                "market_state": market_analysis.market_state.value,
                                "confidence": market_analysis.confidence,
                            },
                            "timestamp": self.mode_transition_time.isoformat(),
                        },
                    )
                )

            if self.logger:
                await self.logger.info(
                    f"Strategy mode switched: {old_mode.value if old_mode else 'None'} -> {target_mode.value}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Mode switch failed: {e}")

    async def _cleanup_mode(self, mode: Optional[StrategyMode]):
        """清理模式相关资源"""
        try:
            if not mode:
                return

            # 取消待处理订单
            self.pending_orders.clear()

            # 记录模式切换
            if self.logger:
                await self.logger.info(f"Cleaning up mode: {mode.value}")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Mode cleanup failed: {e}")

    async def _execute_strategy_logic(self, market_analysis: MarketAnalysis):
        """执行策略逻辑"""
        try:
            if not self.current_mode:
                return

            # 根据当前模式执行相应逻辑
            if self.current_mode == StrategyMode.ACCUMULATION:
                await self._execute_accumulation_mode(market_analysis)
            elif self.current_mode == StrategyMode.DISTRIBUTION:
                await self._execute_distribution_mode(market_analysis)
            elif self.current_mode == StrategyMode.SIDEWAYS:
                await self._execute_sideways_mode(market_analysis)

        except Exception as e:
            self.strategy_metrics.error_count += 1
            if self.logger:
                await self.logger.error(f"Strategy execution failed: {e}")

    async def _execute_accumulation_mode(self, market_analysis: MarketAnalysis):
        """执行抄底模式"""
        try:
            # 检查是否满足抄底条件（委托给专业模式类）
            current_price = Decimal(str(market_analysis.current_price))
            market_analysis_dict = {
                "price_momentum": market_analysis.price_momentum,
                "iv_rank": market_analysis.iv_rank,
                "support_level": market_analysis.support_level,
                "resistance_level": market_analysis.resistance_level,
                "rsi": getattr(market_analysis, "rsi", 30),  # 默认超卖状态
            }

            if not await self.accumulation_mode.should_accumulate(
                current_price, market_analysis_dict
            ):
                return

            # 生成抄底订单（委托给专业模式类）
            orders = await self.accumulation_mode.generate_accumulation_orders(
                current_price, market_analysis_dict
            )

            # 执行订单
            for order in orders:
                await self._execute_order(order)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Accumulation mode execution failed: {e}")

    async def _execute_distribution_mode(self, market_analysis: MarketAnalysis):
        """执行卖出模式"""
        try:
            # 检查是否满足卖出条件（委托给专业模式类）
            current_price = Decimal(str(market_analysis.current_price))
            market_analysis_dict = {
                "price_momentum": market_analysis.price_momentum,
                "iv_rank": market_analysis.iv_rank,
                "support_level": market_analysis.support_level,
                "resistance_level": market_analysis.resistance_level,
                "rsi": getattr(market_analysis, "rsi", 70),  # 默认超买状态
            }

            if not await self.distribution_mode.should_distribute(
                current_price, market_analysis_dict
            ):
                return

            # 生成卖出订单（委托给专业模式类）
            orders = await self.distribution_mode.generate_distribution_orders(
                current_price, market_analysis_dict
            )

            # 执行订单
            for order in orders:
                await self._execute_order(order)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Distribution mode execution failed: {e}")

    async def _execute_sideways_mode(self, market_analysis: MarketAnalysis):
        """执行震荡模式"""
        try:
            # 检查是否满足震荡策略条件（委托给专业模式类）
            current_price = Decimal(str(market_analysis.current_price))
            market_analysis_dict = {
                "price_momentum": market_analysis.price_momentum,
                "iv_rank": market_analysis.iv_rank,
                "realized_volatility": getattr(
                    market_analysis, "realized_volatility", 0.6
                ),
                "implied_volatility": getattr(
                    market_analysis, "implied_volatility", 0.8
                ),
                "rsi": getattr(market_analysis, "rsi", 50),  # 默认中性RSI
            }

            if not await self.sideways_mode.should_trade_sideways(
                current_price, market_analysis_dict
            ):
                return

            # 生成震荡策略订单（委托给专业模式类）
            orders = await self.sideways_mode.generate_sideways_strategies(
                current_price, market_analysis_dict
            )

            # 执行订单
            for order in orders:
                await self._execute_order(order)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Sideways mode execution failed: {e}")

    async def _execute_order(self, order: OrderRequest):
        """执行订单"""
        try:
            # 添加到待处理订单
            self.pending_orders.append(order)

            # 更新指标
            self.strategy_metrics.total_trades += 1
            self.strategy_metrics.last_trade_time = datetime.now(timezone.utc)

            # 发布订单事件
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type="strategy_order",
                        data={
                            "symbol": order.symbol,
                            "side": order.side,
                            "size": float(order.size),
                            "strike": float(order.strike) if order.strike else None,
                            "option_type": order.option_type,
                            "reason": order.reason,
                            "metadata": order.metadata,
                            "timestamp": datetime.now(timezone.utc).isoformat(),
                        },
                    )
                )

            if self.logger:
                await self.logger.info(
                    f"Order executed: {order.reason} - {order.side} {order.size} {order.symbol}"
                )

        except Exception as e:
            self.strategy_metrics.error_count += 1
            if self.logger:
                await self.logger.error(f"Order execution failed: {e}")

    async def _risk_monitor_loop(self):
        """风险监控循环"""
        while self.is_running:
            try:
                await self._check_position_risk()
                await self._check_greeks_risk()
                await self._check_expiry_risk()

                await asyncio.sleep(self.config["risk_check_interval"])

            except Exception as e:
                self.strategy_metrics.error_count += 1
                if self.logger:
                    await self.logger.error(f"Risk monitor error: {e}")
                await asyncio.sleep(60)

    async def _check_position_risk(self):
        """检查仓位风险"""
        try:
            total_exposure = Decimal("0")
            for position in self.positions.values():
                total_exposure += abs(position.size * position.current_price)

            # 检查总仓位限制
            max_exposure = self.config["capital_allocation"] * Decimal(
                "10"
            )  # 10倍杠杆限制
            if total_exposure > max_exposure:
                if self.logger:
                    await self.logger.warning(
                        f"Position exposure exceeds limit: {total_exposure} > {max_exposure}"
                    )

                # 发布风险警告
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type="risk_warning",
                            data={
                                "type": "position_exposure",
                                "current": float(total_exposure),
                                "limit": float(max_exposure),
                                "timestamp": datetime.now(timezone.utc).isoformat(),
                            },
                        )
                    )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Position risk check failed: {e}")

    async def _check_greeks_risk(self):
        """检查Greeks风险"""
        try:
            total_delta = 0.0
            total_gamma = 0.0
            total_theta = 0.0
            total_vega = 0.0

            for position in self.positions.values():
                if position.position_type in ["put", "call"]:
                    total_delta += position.delta * float(position.size)
                    total_gamma += position.gamma * float(position.size)
                    total_theta += position.theta * float(position.size)
                    total_vega += position.vega * float(position.size)

            # 检查Delta中性
            if abs(total_delta) > 0.5:  # Delta限制
                if self.logger:
                    await self.logger.warning(f"Delta exposure too high: {total_delta}")

            # 检查Gamma风险
            if abs(total_gamma) > 0.1:  # Gamma限制
                if self.logger:
                    await self.logger.warning(f"Gamma exposure too high: {total_gamma}")

            # 检查Vega风险
            if abs(total_vega) > 100:  # Vega限制
                if self.logger:
                    await self.logger.warning(f"Vega exposure too high: {total_vega}")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Greeks risk check failed: {e}")

    async def _check_expiry_risk(self):
        """检查到期风险"""
        try:
            near_expiry_positions = []
            current_time = datetime.now(timezone.utc)

            for symbol, position in self.positions.items():
                if position.position_type in ["put", "call"]:
                    days_to_expiry = (position.expiry - current_time).days
                    if days_to_expiry <= 7:  # 7天内到期
                        near_expiry_positions.append((symbol, position, days_to_expiry))

            if near_expiry_positions:
                if self.logger:
                    await self.logger.info(
                        f"Found {len(near_expiry_positions)} positions expiring within 7 days"
                    )

                # 发布到期提醒
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type="expiry_reminder",
                            data={
                                "positions": [
                                    {
                                        "symbol": symbol,
                                        "days_to_expiry": days,
                                        "size": float(pos.size),
                                        "unrealized_pnl": float(pos.unrealized_pnl),
                                    }
                                    for symbol, pos, days in near_expiry_positions
                                ],
                                "timestamp": current_time.isoformat(),
                            },
                        )
                    )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiry risk check failed: {e}")

    async def _performance_loop(self):
        """性能评估循环"""
        while self.is_running:
            try:
                await self._update_performance_metrics()
                await self._optimize_strategy_parameters()

                await asyncio.sleep(self.config["performance_review_interval"])

            except Exception as e:
                self.strategy_metrics.error_count += 1
                if self.logger:
                    await self.logger.error(f"Performance loop error: {e}")
                await asyncio.sleep(300)

    async def _update_performance_metrics(self):
        """更新性能指标"""
        try:
            # 计算总PnL
            total_pnl = Decimal("0")
            for position in self.positions.values():
                total_pnl += position.unrealized_pnl

            self.strategy_metrics.total_pnl = total_pnl

            # 计算胜率
            if self.strategy_metrics.total_trades > 0:
                self.strategy_metrics.win_rate = (
                    self.strategy_metrics.successful_trades
                    / self.strategy_metrics.total_trades
                )

            # 计算夏普比率（简化版）
            if self.strategy_metrics.total_trades > 10:
                returns = [float(total_pnl) / self.strategy_metrics.total_trades]
                if len(returns) > 1:
                    avg_return = np.mean(returns)
                    std_return = np.std(returns)
                    if std_return > 0:
                        self.strategy_metrics.sharpe_ratio = avg_return / std_return

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Performance metrics update failed: {e}")

    async def _optimize_strategy_parameters(self):
        """优化策略参数"""
        try:
            # 基于历史表现调整参数
            if self.strategy_metrics.win_rate < 0.4:  # 胜率过低
                # 提高IV阈值，更保守
                self.config["iv_threshold"] = min(
                    0.8, self.config["iv_threshold"] + 0.05
                )

            elif self.strategy_metrics.win_rate > 0.7:  # 胜率很高
                # 降低IV阈值，更积极
                self.config["iv_threshold"] = max(
                    0.6, self.config["iv_threshold"] - 0.02
                )

            # 根据错误率调整
            if self.strategy_metrics.error_count > 20:
                # 增加检查间隔，降低频率
                self.config["risk_check_interval"] = min(
                    120, self.config["risk_check_interval"] + 10
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Parameter optimization failed: {e}")

    def get_strategy_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        return {
            "current_mode": self.current_mode.value if self.current_mode else None,
            "target_mode": self.target_mode.value if self.target_mode else None,
            "mode_transition_time": self.mode_transition_time.isoformat()
            if self.mode_transition_time
            else None,
            "metrics": {
                "total_trades": self.strategy_metrics.total_trades,
                "successful_trades": self.strategy_metrics.successful_trades,
                "total_pnl": float(self.strategy_metrics.total_pnl),
                "win_rate": self.strategy_metrics.win_rate,
                "sharpe_ratio": self.strategy_metrics.sharpe_ratio,
                "mode_switch_count": self.strategy_metrics.mode_switch_count,
                "error_count": self.strategy_metrics.error_count,
            },
            "positions": {
                symbol: {
                    "type": pos.position_type,
                    "size": float(pos.size),
                    "unrealized_pnl": float(pos.unrealized_pnl),
                    "delta": pos.delta,
                    "expiry": pos.expiry.isoformat() if pos.expiry else None,
                }
                for symbol, pos in self.positions.items()
            },
            "pending_orders": len(self.pending_orders),
            "config": self.config,
        }
