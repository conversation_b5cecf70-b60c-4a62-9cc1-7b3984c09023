"""
分析模块类型定义

避免循环导入的类型定义模块
"""

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any, Dict


class SignalType(Enum):
    """信号类型枚举"""

    STRUCTURE_DIVERGENCE = "structure_divergence"
    VOLATILITY_MISMATCH = "volatility_mismatch"
    GAMMA_LIQUIDATION_OVERLAP = "gamma_liquidation_overlap"


class SignalStrength(Enum):
    """信号强度枚举"""

    WEAK = "weak"  # 0.0-0.3
    MODERATE = "moderate"  # 0.3-0.6
    STRONG = "strong"  # 0.6-0.8
    VERY_STRONG = "very_strong"  # 0.8-1.0


@dataclass
class CausalSignal:
    """因果分析信号"""

    signal_type: SignalType
    strength: float  # 0-1之间的信号强度
    confidence: float  # 0-1之间的置信度
    timestamp: datetime
    metadata: Dict[str, Any]  # 额外的信号元数据

    def get_strength_level(self) -> SignalStrength:
        """获取信号强度等级"""
        if self.strength < 0.3:
            return SignalStrength.WEAK
        elif self.strength < 0.6:
            return SignalStrength.MODERATE
        elif self.strength < 0.8:
            return SignalStrength.STRONG
        else:
            return SignalStrength.VERY_STRONG

    def is_actionable(self, min_confidence: float = 0.6) -> bool:
        """判断信号是否可操作"""
        return self.confidence >= min_confidence

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "signal_type": self.signal_type.value,
            "strength": self.strength,
            "confidence": self.confidence,
            "timestamp": self.timestamp.isoformat(),
            "strength_level": self.get_strength_level().value,
            "is_actionable": self.is_actionable(),
            "metadata": self.metadata,
        }
