"""
流动性分析模块

提供bid-ask spread监控、成交量分析、市场深度评估和流动性风险预警功能
"""

import asyncio
from collections import deque
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple

import numpy as np

from ..core.base_component import BaseComponent, HealthStatus
from ..core.event_bus import BaseEvent, DataUpdateEvent


@dataclass
class LiquidityMetrics:
    """流动性指标"""

    symbol: str
    timestamp: datetime

    # 价差指标
    bid_ask_spread: Decimal = Decimal("0")
    spread_percentage: float = 0.0

    # 成交量指标
    volume_24h: Decimal = Decimal("0")
    volume_1h: Decimal = Decimal("0")
    avg_trade_size: Decimal = Decimal("0")

    # 持仓量指标
    open_interest: Decimal = Decimal("0")
    oi_change_24h: Decimal = Decimal("0")

    # 市场深度指标
    bid_depth_5: Decimal = Decimal("0")  # 5档买单深度
    ask_depth_5: Decimal = Decimal("0")  # 5档卖单深度
    total_depth: Decimal = Decimal("0")

    # 流动性评分
    liquidity_score: float = 0.0  # 0-100分
    liquidity_grade: str = "Unknown"  # A, B, C, D, F

    # 风险指标
    price_impact_1k: float = 0.0  # 1000美元订单的价格冲击
    price_impact_10k: float = 0.0  # 10000美元订单的价格冲击


@dataclass
class LiquidityAlert:
    """流动性告警"""

    symbol: str
    alert_type: str  # spread_high, volume_low, depth_low, price_impact_high
    severity: str  # low, medium, high, critical
    message: str
    timestamp: datetime
    current_value: float
    threshold: float


class LiquidityAnalyzer(BaseComponent):
    """流动性分析器"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("LiquidityAnalyzer", config)
        self.config = config or {}

        # 流动性数据存储
        self.liquidity_metrics: Dict[str, LiquidityMetrics] = {}
        self.metrics_history: Dict[str, deque] = {}
        self.max_history_size = 1440  # 24小时历史数据

        # 告警配置
        self.alert_thresholds = {
            "spread_percentage": 5.0,  # 价差超过5%
            "volume_24h_min": 1000,  # 24小时最小成交量
            "depth_min": 5000,  # 最小市场深度
            "price_impact_1k_max": 0.5,  # 1k订单最大价格冲击0.5%
            "price_impact_10k_max": 2.0,  # 10k订单最大价格冲击2%
        }

        # 流动性评分权重
        self.score_weights = {
            "spread": 0.3,  # 价差权重
            "volume": 0.25,  # 成交量权重
            "depth": 0.25,  # 市场深度权重
            "stability": 0.2,  # 稳定性权重
        }

        # 活跃告警
        self.active_alerts: List[LiquidityAlert] = []

    async def _initialize_impl(self):
        """具体初始化实现"""
        # 订阅市场数据事件
        if hasattr(self, "event_bus") and self.event_bus:
            await self.event_bus.subscribe(
                "market_data_update", self._handle_market_data
            )
            await self.event_bus.subscribe(
                "orderbook_update", self._handle_orderbook_update
            )
            await self.event_bus.subscribe("trade_update", self._handle_trade_update)

        # 启动定期分析任务
        asyncio.create_task(self._periodic_analysis())

        if self.logger:
            await self.logger.info("LiquidityAnalyzer initialized successfully")

    async def _start_impl(self):
        """具体启动实现"""
        if self.logger:
            await self.logger.info("LiquidityAnalyzer started")

    async def _stop_impl(self):
        """具体停止实现"""
        if self.logger:
            await self.logger.info("LiquidityAnalyzer stopped")

    async def _health_check_impl(self) -> Tuple[HealthStatus, str]:
        """具体健康检查实现"""
        try:
            # 检查基本状态
            if not self.is_initialized:
                return HealthStatus.UNHEALTHY, "Component not initialized"

            # 检查数据更新状态
            if not self.liquidity_metrics:
                return HealthStatus.DEGRADED, "No liquidity data available"

            # 检查告警状态
            critical_alerts = [
                alert for alert in self.active_alerts if alert.severity == "critical"
            ]
            if critical_alerts:
                return (
                    HealthStatus.DEGRADED,
                    f"Critical liquidity alerts: {len(critical_alerts)}",
                )

            return HealthStatus.HEALTHY, "LiquidityAnalyzer operating normally"

        except Exception as e:
            return HealthStatus.UNHEALTHY, f"Health check failed: {e}"

    async def _handle_market_data(self, event: BaseEvent):
        """处理市场数据更新"""
        try:
            data = event.data
            symbol = data.get("symbol")

            if not symbol:
                return

            # 更新基础价格数据
            await self._update_price_metrics(symbol, data)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling market data: {e}")

    async def _handle_orderbook_update(self, event: BaseEvent):
        """处理订单簿更新"""
        try:
            data = event.data
            symbol = data.get("symbol")

            if not symbol:
                return

            # 更新市场深度指标
            await self._update_depth_metrics(symbol, data)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling orderbook update: {e}")

    async def _handle_trade_update(self, event: BaseEvent):
        """处理交易更新"""
        try:
            data = event.data
            symbol = data.get("symbol")

            if not symbol:
                return

            # 更新成交量指标
            await self._update_volume_metrics(symbol, data)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling trade update: {e}")

    async def _update_price_metrics(self, symbol: str, data: Dict[str, Any]):
        """更新价格相关指标"""
        try:
            bid = Decimal(str(data.get("bid", 0)))
            ask = Decimal(str(data.get("ask", 0)))

            if bid > 0 and ask > 0:
                # 计算价差
                spread = ask - bid
                mid_price = (bid + ask) / 2
                spread_percentage = (
                    float(spread / mid_price * 100) if mid_price > 0 else 0
                )

                # 获取或创建流动性指标
                if symbol not in self.liquidity_metrics:
                    self.liquidity_metrics[symbol] = LiquidityMetrics(
                        symbol=symbol, timestamp=datetime.now(timezone.utc)
                    )

                metrics = self.liquidity_metrics[symbol]
                metrics.bid_ask_spread = spread
                metrics.spread_percentage = spread_percentage
                metrics.timestamp = datetime.now(timezone.utc)

                # 检查价差告警
                await self._check_spread_alert(symbol, spread_percentage)

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error updating price metrics for {symbol}: {e}"
                )

    async def _update_depth_metrics(self, symbol: str, data: Dict[str, Any]):
        """更新市场深度指标"""
        try:
            bids = data.get("bids", [])
            asks = data.get("asks", [])

            # 计算5档深度
            bid_depth_5 = sum(Decimal(str(bid[1])) for bid in bids[:5])
            ask_depth_5 = sum(Decimal(str(ask[1])) for ask in asks[:5])
            total_depth = bid_depth_5 + ask_depth_5

            # 更新指标
            if symbol in self.liquidity_metrics:
                metrics = self.liquidity_metrics[symbol]
                metrics.bid_depth_5 = bid_depth_5
                metrics.ask_depth_5 = ask_depth_5
                metrics.total_depth = total_depth

                # 计算价格冲击
                metrics.price_impact_1k = await self._calculate_price_impact(
                    symbol, 1000, bids, asks
                )
                metrics.price_impact_10k = await self._calculate_price_impact(
                    symbol, 10000, bids, asks
                )

                # 检查深度告警
                await self._check_depth_alert(symbol, float(total_depth))
                await self._check_price_impact_alert(
                    symbol, metrics.price_impact_1k, metrics.price_impact_10k
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error updating depth metrics for {symbol}: {e}"
                )

    async def _update_volume_metrics(self, symbol: str, data: Dict[str, Any]):
        """更新成交量指标"""
        try:
            # 这里需要从缓存或数据库获取历史成交量数据
            # 简化实现，使用模拟数据

            if symbol in self.liquidity_metrics:
                metrics = self.liquidity_metrics[symbol]

                # 模拟成交量数据
                metrics.volume_24h = Decimal("5000")
                metrics.volume_1h = Decimal("200")
                metrics.avg_trade_size = Decimal("50")
                metrics.open_interest = Decimal("10000")
                metrics.oi_change_24h = Decimal("500")

                # 检查成交量告警
                await self._check_volume_alert(symbol, float(metrics.volume_24h))

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error updating volume metrics for {symbol}: {e}"
                )

    async def _calculate_price_impact(
        self, symbol: str, order_size_usd: float, bids: List, asks: List
    ) -> float:
        """计算价格冲击"""
        try:
            # 获取当前中间价
            if not bids or not asks:
                return 0.0

            best_bid = Decimal(str(bids[0][0]))
            best_ask = Decimal(str(asks[0][0]))
            mid_price = (best_bid + best_ask) / 2

            # 计算需要的数量
            order_quantity = Decimal(str(order_size_usd)) / mid_price

            # 模拟市价单执行，计算平均成交价
            remaining_qty = order_quantity
            total_cost = Decimal("0")

            # 假设买单，遍历卖单
            for price_str, qty_str in asks:
                if remaining_qty <= 0:
                    break

                price = Decimal(str(price_str))
                qty = Decimal(str(qty_str))

                executed_qty = min(remaining_qty, qty)
                total_cost += executed_qty * price
                remaining_qty -= executed_qty

            if remaining_qty > 0:
                # 流动性不足
                return 10.0  # 返回高价格冲击

            avg_price = total_cost / order_quantity
            price_impact = float(abs(avg_price - mid_price) / mid_price * 100)

            return price_impact

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error calculating price impact: {e}")
            return 0.0

    async def _calculate_liquidity_score(self, symbol: str) -> Tuple[float, str]:
        """计算流动性评分"""
        try:
            if symbol not in self.liquidity_metrics:
                return 0.0, "F"

            metrics = self.liquidity_metrics[symbol]

            # 价差评分 (0-100)
            spread_score = max(0, 100 - metrics.spread_percentage * 20)

            # 成交量评分 (0-100)
            volume_score = min(100, float(metrics.volume_24h) / 10000 * 100)

            # 市场深度评分 (0-100)
            depth_score = min(100, float(metrics.total_depth) / 50000 * 100)

            # 稳定性评分 (基于历史数据波动性)
            stability_score = await self._calculate_stability_score(symbol)

            # 加权总分
            total_score = (
                spread_score * self.score_weights["spread"]
                + volume_score * self.score_weights["volume"]
                + depth_score * self.score_weights["depth"]
                + stability_score * self.score_weights["stability"]
            )

            # 评级
            if total_score >= 90:
                grade = "A"
            elif total_score >= 80:
                grade = "B"
            elif total_score >= 70:
                grade = "C"
            elif total_score >= 60:
                grade = "D"
            else:
                grade = "F"

            return total_score, grade

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error calculating liquidity score: {e}")
            return 0.0, "F"

    async def _calculate_stability_score(self, symbol: str) -> float:
        """计算稳定性评分"""
        try:
            # 获取历史数据
            if symbol not in self.metrics_history:
                return 50.0  # 默认中等评分

            history = self.metrics_history[symbol]
            if len(history) < 10:
                return 50.0

            # 计算价差波动性
            spreads = [m.spread_percentage for m in history]
            spread_volatility = np.std(spreads) if spreads else 0

            # 稳定性评分 (波动性越低，评分越高)
            stability_score = max(0, 100 - spread_volatility * 10)

            return stability_score

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error calculating stability score: {e}")
            return 50.0

    async def _check_spread_alert(self, symbol: str, spread_percentage: float):
        """检查价差告警"""
        threshold = self.alert_thresholds["spread_percentage"]

        if spread_percentage > threshold:
            alert = LiquidityAlert(
                symbol=symbol,
                alert_type="spread_high",
                severity="high" if spread_percentage > threshold * 2 else "medium",
                message=f"Bid-ask spread {spread_percentage:.2f}% exceeds threshold {threshold}%",
                timestamp=datetime.now(timezone.utc),
                current_value=spread_percentage,
                threshold=threshold,
            )

            await self._add_alert(alert)

    async def _check_volume_alert(self, symbol: str, volume_24h: float):
        """检查成交量告警"""
        threshold = self.alert_thresholds["volume_24h_min"]

        if volume_24h < threshold:
            alert = LiquidityAlert(
                symbol=symbol,
                alert_type="volume_low",
                severity="medium",
                message=f"24h volume {volume_24h:.0f} below threshold {threshold}",
                timestamp=datetime.now(timezone.utc),
                current_value=volume_24h,
                threshold=threshold,
            )

            await self._add_alert(alert)

    async def _check_depth_alert(self, symbol: str, total_depth: float):
        """检查市场深度告警"""
        threshold = self.alert_thresholds["depth_min"]

        if total_depth < threshold:
            alert = LiquidityAlert(
                symbol=symbol,
                alert_type="depth_low",
                severity="medium",
                message=f"Market depth {total_depth:.0f} below threshold {threshold}",
                timestamp=datetime.now(timezone.utc),
                current_value=total_depth,
                threshold=threshold,
            )

            await self._add_alert(alert)

    async def _check_price_impact_alert(
        self, symbol: str, impact_1k: float, impact_10k: float
    ):
        """检查价格冲击告警"""
        threshold_1k = self.alert_thresholds["price_impact_1k_max"]
        threshold_10k = self.alert_thresholds["price_impact_10k_max"]

        if impact_1k > threshold_1k:
            alert = LiquidityAlert(
                symbol=symbol,
                alert_type="price_impact_high",
                severity="medium",
                message=f"1K order price impact {impact_1k:.2f}% exceeds threshold {threshold_1k}%",
                timestamp=datetime.now(timezone.utc),
                current_value=impact_1k,
                threshold=threshold_1k,
            )
            await self._add_alert(alert)

        if impact_10k > threshold_10k:
            alert = LiquidityAlert(
                symbol=symbol,
                alert_type="price_impact_high",
                severity="high",
                message=f"10K order price impact {impact_10k:.2f}% exceeds threshold {threshold_10k}%",
                timestamp=datetime.now(timezone.utc),
                current_value=impact_10k,
                threshold=threshold_10k,
            )
            await self._add_alert(alert)

    async def _add_alert(self, alert: LiquidityAlert):
        """添加告警"""
        # 检查是否已存在相同告警
        existing = any(
            a.symbol == alert.symbol
            and a.alert_type == alert.alert_type
            and (datetime.now(timezone.utc) - a.timestamp).seconds < 300  # 5分钟内
            for a in self.active_alerts
        )

        if not existing:
            self.active_alerts.append(alert)

            # 发布告警事件
            if self.event_bus:
                await self.event_bus.publish(
                    DataUpdateEvent(
                        event_type="liquidity_alert",
                        data={
                            "symbol": alert.symbol,
                            "alert_type": alert.alert_type,
                            "severity": alert.severity,
                            "message": alert.message,
                            "current_value": alert.current_value,
                            "threshold": alert.threshold,
                        },
                    )
                )

            if self.logger:
                await self.logger.warning(f"Liquidity alert: {alert.message}")

    async def _periodic_analysis(self):
        """定期分析任务"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟执行一次

                # 更新所有符号的流动性评分
                for symbol in self.liquidity_metrics:
                    score, grade = await self._calculate_liquidity_score(symbol)

                    metrics = self.liquidity_metrics[symbol]
                    metrics.liquidity_score = score
                    metrics.liquidity_grade = grade

                    # 保存历史数据
                    if symbol not in self.metrics_history:
                        self.metrics_history[symbol] = deque(
                            maxlen=self.max_history_size
                        )

                    self.metrics_history[symbol].append(metrics)

                # 清理过期告警
                current_time = datetime.now(timezone.utc)
                self.active_alerts = [
                    alert
                    for alert in self.active_alerts
                    if (current_time - alert.timestamp).seconds
                    < 3600  # 保留1小时内的告警
                ]

            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Periodic analysis error: {e}")

    # 公共接口方法

    async def get_liquidity_metrics(self, symbol: str) -> Optional[LiquidityMetrics]:
        """获取流动性指标"""
        return self.liquidity_metrics.get(symbol)

    async def get_all_liquidity_metrics(self) -> Dict[str, LiquidityMetrics]:
        """获取所有流动性指标"""
        return self.liquidity_metrics.copy()

    async def get_liquidity_alerts(
        self, symbol: Optional[str] = None
    ) -> List[LiquidityAlert]:
        """获取流动性告警"""
        if symbol:
            return [alert for alert in self.active_alerts if alert.symbol == symbol]
        return self.active_alerts.copy()

    async def get_liquidity_history(
        self, symbol: str, hours: int = 24
    ) -> List[LiquidityMetrics]:
        """获取流动性历史数据"""
        if symbol not in self.metrics_history:
            return []

        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        history = self.metrics_history[symbol]

        return [metrics for metrics in history if metrics.timestamp >= cutoff_time]

    async def is_symbol_liquid(self, symbol: str) -> bool:
        """检查符号是否具有足够流动性"""
        metrics = await self.get_liquidity_metrics(symbol)

        if not metrics:
            return False

        # 检查关键流动性指标
        spread_ok = (
            metrics.spread_percentage <= self.alert_thresholds["spread_percentage"]
        )
        volume_ok = float(metrics.volume_24h) >= self.alert_thresholds["volume_24h_min"]
        depth_ok = float(metrics.total_depth) >= self.alert_thresholds["depth_min"]

        return spread_ok and volume_ok and depth_ok

    async def health_check(self) -> HealthStatus:
        """健康检查"""
        try:
            # 检查组件状态
            if not self.liquidity_metrics:
                return HealthStatus.DEGRADED, "No liquidity data available"

            # 检查告警数量
            critical_alerts = [
                a for a in self.active_alerts if a.severity == "critical"
            ]
            if critical_alerts:
                return (
                    HealthStatus.UNHEALTHY,
                    f"{len(critical_alerts)} critical liquidity alerts",
                )

            high_alerts = [a for a in self.active_alerts if a.severity == "high"]
            if len(high_alerts) > 5:
                return HealthStatus.DEGRADED, f"{len(high_alerts)} high severity alerts"

            return HealthStatus.HEALTHY, "Liquidity analyzer operating normally"

        except Exception as e:
            return HealthStatus.UNHEALTHY, f"Health check failed: {e}"
