"""
结构分歧分析器

分析Binance Taker Flow与Deribit OI的相关性，识别主流资金与期权市场的方向差异。
实现资金流向分歧度量和评分算法，提供分歧信号的置信度计算。

核心功能：
1. 主流资金vs期权防御分析
2. Taker Buy/Sell Ratio与Call/Put OI变化率对比
3. 结构分歧信号生成和评分
4. 历史验证和成功率统计
"""

import asyncio
from collections import deque
from dataclasses import dataclass, field
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

import numpy as np

from ..core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from ..core.config_manager import ConfigManager
from ..core.event_bus import EventBus
from ..data.cache_manager import CacheManager
from .types import CausalSignal, SignalType


@dataclass
class TakerFlowData:
    """Taker流向数据"""

    buy_volume: float
    sell_volume: float
    buy_ratio: float  # buy_volume / (buy_volume + sell_volume)
    sell_ratio: float  # sell_volume / (buy_volume + sell_volume)
    net_flow: float  # buy_volume - sell_volume
    timestamp: datetime


@dataclass
class OptionOIData:
    """期权持仓量数据"""

    call_oi: float
    put_oi: float
    call_oi_change: float  # 变化率
    put_oi_change: float  # 变化率
    call_put_ratio: float  # call_oi / put_oi
    net_oi_change: float  # call_oi_change - put_oi_change
    timestamp: datetime


@dataclass
class StructureDivergenceMetrics:
    """结构分歧指标"""

    correlation: float  # Taker Flow与OI变化的相关性
    divergence_score: float  # 分歧评分 (0-1)
    confidence: float  # 置信度 (0-1)
    direction_conflict: bool  # 方向冲突标志
    strength_ratio: float  # 强度比率
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)


class StructureDivergenceAnalyzer(BaseComponent):
    """
    结构分歧分析器

    分析Binance现货/永续市场的主流资金流向与Deribit期权市场的防御性行为差异，
    识别市场结构性机会并生成交易信号。
    """

    def __init__(self, config_manager: ConfigManager):
        super().__init__("StructureDivergenceAnalyzer")
        self.config_manager = config_manager

        # 核心组件依赖
        self.event_bus: Optional[EventBus] = None
        self.cache_manager: Optional[CacheManager] = None

        # 数据缓冲区
        self.taker_flow_buffer: deque = deque(maxlen=100)  # 最近100个数据点
        self.option_oi_buffer: deque = deque(maxlen=100)
        self.divergence_history: deque = deque(maxlen=500)  # 分歧历史

        # 配置参数
        self.config = {
            "lookback_window": 20,  # 回看窗口大小
            "correlation_threshold": -0.3,  # 负相关阈值
            "divergence_threshold": 0.6,  # 分歧信号阈值
            "min_volume_threshold": 1000000,  # 最小成交量阈值（USD）
            "min_oi_change_threshold": 0.05,  # 最小OI变化阈值（5%）
            "confidence_decay_factor": 0.95,  # 置信度衰减因子
            "signal_cooldown_minutes": 30,  # 信号冷却时间（分钟）
        }

        # 统计指标
        self.total_signals = 0
        self.successful_signals = 0
        self.last_signal_time: Optional[datetime] = None

        # 运行状态
        self._analysis_task: Optional[asyncio.Task] = None
        self.analysis_interval = 30  # 30秒分析间隔

    async def _initialize_impl(self) -> bool:
        """具体初始化实现"""
        try:
            # 加载配置
            analyzer_config = self.config_manager.get_section("structure_analyzer")

            # 更新配置参数
            self.config.update(analyzer_config)
            self.analysis_interval = self.config.get("analysis_interval", 30)

            if self.logger:
                await self.logger.info(
                    "StructureDivergenceAnalyzer initialized successfully"
                )

            return True

        except Exception as e:
            # 即使没有logger也要记录错误
            error_msg = f"Failed to initialize StructureDivergenceAnalyzer: {e}"
            if self.logger:
                await self.logger.error(error_msg)
            else:
                print(error_msg)  # 测试环境下的fallback
            return False

    async def _start_impl(self) -> bool:
        """具体启动实现"""
        try:
            # 启动分析任务
            self._analysis_task = asyncio.create_task(self._analysis_loop())

            if self.logger:
                await self.logger.info(
                    "StructureDivergenceAnalyzer started successfully"
                )

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Failed to start StructureDivergenceAnalyzer: {e}"
                )
            return False

    async def _stop_impl(self) -> bool:
        """具体停止实现"""
        try:
            # 停止分析任务
            if self._analysis_task and not self._analysis_task.done():
                self._analysis_task.cancel()
                try:
                    await self._analysis_task
                except asyncio.CancelledError:
                    pass

            if self.logger:
                await self.logger.info(
                    "StructureDivergenceAnalyzer stopped successfully"
                )

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Failed to stop StructureDivergenceAnalyzer: {e}"
                )
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """具体健康检查实现"""
        try:
            # 检查分析任务状态
            analysis_healthy = (
                self._analysis_task is not None and not self._analysis_task.done()
            )

            # 检查数据缓冲区状态
            data_healthy = (
                len(self.taker_flow_buffer) > 0 and len(self.option_oi_buffer) > 0
            )

            if analysis_healthy and data_healthy:
                status = HealthStatus.HEALTHY
                message = "StructureDivergenceAnalyzer is running normally"
            elif analysis_healthy:
                status = HealthStatus.DEGRADED
                message = "StructureDivergenceAnalyzer is running but data may be stale"
            else:
                status = HealthStatus.UNHEALTHY
                message = "StructureDivergenceAnalyzer is not running"

            details = {
                "analysis_task_running": analysis_healthy,
                "taker_flow_buffer_size": len(self.taker_flow_buffer),
                "option_oi_buffer_size": len(self.option_oi_buffer),
                "total_signals": self.total_signals,
                "success_rate": self.successful_signals / self.total_signals
                if self.total_signals > 0
                else 0.0,
                "last_signal_time": self.last_signal_time.isoformat()
                if self.last_signal_time
                else None,
            }

            return HealthCheckResult(status=status, message=message, details=details)

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {str(e)}"
            )

    async def _analysis_loop(self):
        """主分析循环"""
        while True:
            try:
                await self._run_analysis()
                await asyncio.sleep(self.analysis_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Error in analysis loop: {e}")
                await asyncio.sleep(self.analysis_interval)

    async def _run_analysis(self):
        """运行结构分歧分析"""
        try:
            # 获取最新数据
            taker_data = await self._get_taker_flow_data()
            oi_data = await self._get_option_oi_data()

            if not taker_data or not oi_data:
                return

            # 更新数据缓冲区
            self.taker_flow_buffer.append(taker_data)
            self.option_oi_buffer.append(oi_data)

            # 检查是否有足够的数据进行分析
            if len(self.taker_flow_buffer) < self.config["lookback_window"]:
                return

            # 计算结构分歧指标
            divergence_metrics = await self._calculate_divergence_metrics()

            if divergence_metrics:
                # 生成信号
                signal = await self._generate_signal(divergence_metrics)

                if signal:
                    # 更新统计
                    self.total_signals += 1
                    self.last_signal_time = signal.timestamp

                    # 记录分歧历史
                    self.divergence_history.append(divergence_metrics)

                    if self.logger:
                        await self.logger.info(
                            f"Structure divergence signal generated: "
                            f"strength={signal.strength:.3f} confidence={signal.confidence:.3f}"
                        )

                    return signal

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error in structure divergence analysis: {e}")
            return None

    async def _get_taker_flow_data(self) -> Optional[TakerFlowData]:
        """获取Taker流向数据"""
        try:
            if not self.cache_manager:
                return None

            # 从缓存获取Binance交易数据
            binance_data = await self.cache_manager.get(
                "binance:aggTrade", "market_data"
            )
            if not binance_data:
                return None

            # 解析Taker流向数据
            buy_volume = float(binance_data.get("buy_volume", 0))
            sell_volume = float(binance_data.get("sell_volume", 0))
            total_volume = buy_volume + sell_volume

            if total_volume == 0:
                return None

            return TakerFlowData(
                buy_volume=buy_volume,
                sell_volume=sell_volume,
                buy_ratio=buy_volume / total_volume,
                sell_ratio=sell_volume / total_volume,
                net_flow=buy_volume - sell_volume,
                timestamp=datetime.now(timezone.utc),
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error getting taker flow data: {e}")
            return None

    async def _get_option_oi_data(self) -> Optional[OptionOIData]:
        """获取期权持仓量数据"""
        try:
            if not self.cache_manager:
                return None

            # 从缓存获取Deribit期权数据
            option_data = await self.cache_manager.get(
                "deribit:option_summary", "option_chain"
            )
            if not option_data:
                return None

            # 解析期权OI数据
            call_oi = float(option_data.get("call_oi", 0))
            put_oi = float(option_data.get("put_oi", 0))
            call_oi_change = float(option_data.get("call_oi_change_24h", 0))
            put_oi_change = float(option_data.get("put_oi_change_24h", 0))

            if call_oi == 0 and put_oi == 0:
                return None

            call_put_ratio = call_oi / put_oi if put_oi > 0 else float("inf")

            return OptionOIData(
                call_oi=call_oi,
                put_oi=put_oi,
                call_oi_change=call_oi_change,
                put_oi_change=put_oi_change,
                call_put_ratio=call_put_ratio,
                net_oi_change=call_oi_change - put_oi_change,
                timestamp=datetime.now(timezone.utc),
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error getting option OI data: {e}")
            return None

    async def _calculate_divergence_metrics(
        self,
    ) -> Optional[StructureDivergenceMetrics]:
        """计算结构分歧指标"""
        try:
            lookback = self.config["lookback_window"]

            # 获取最近的数据
            recent_taker_data = list(self.taker_flow_buffer)[-lookback:]
            recent_oi_data = list(self.option_oi_buffer)[-lookback:]

            if len(recent_taker_data) < lookback or len(recent_oi_data) < lookback:
                return None

            # 提取时间序列数据
            taker_net_flows = [data.net_flow for data in recent_taker_data]
            oi_net_changes = [data.net_oi_change for data in recent_oi_data]

            # 计算相关性
            correlation = self._calculate_correlation(taker_net_flows, oi_net_changes)

            # 计算分歧评分
            divergence_score = self._calculate_divergence_score(
                recent_taker_data[-1], recent_oi_data[-1], correlation
            )

            # 计算置信度
            confidence = self._calculate_confidence(
                recent_taker_data, recent_oi_data, correlation, divergence_score
            )

            # 检测方向冲突
            direction_conflict = self._detect_direction_conflict(
                recent_taker_data[-1], recent_oi_data[-1]
            )

            # 计算强度比率
            strength_ratio = self._calculate_strength_ratio(
                recent_taker_data[-1], recent_oi_data[-1]
            )

            # 构建元数据
            metadata = {
                "taker_buy_ratio": recent_taker_data[-1].buy_ratio,
                "taker_net_flow": recent_taker_data[-1].net_flow,
                "call_put_ratio": recent_oi_data[-1].call_put_ratio,
                "net_oi_change": recent_oi_data[-1].net_oi_change,
                "correlation": correlation,
                "lookback_window": lookback,
            }

            return StructureDivergenceMetrics(
                correlation=correlation,
                divergence_score=divergence_score,
                confidence=confidence,
                direction_conflict=direction_conflict,
                strength_ratio=strength_ratio,
                timestamp=datetime.now(timezone.utc),
                metadata=metadata,
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error calculating divergence metrics: {e}")
            return None

    def _calculate_correlation(
        self, series1: List[float], series2: List[float]
    ) -> float:
        """计算两个时间序列的相关性"""
        try:
            if len(series1) != len(series2) or len(series1) < 2:
                return 0.0

            # 使用numpy计算皮尔逊相关系数
            correlation = np.corrcoef(series1, series2)[0, 1]

            # 处理NaN值
            if np.isnan(correlation):
                return 0.0

            return float(correlation)

        except Exception:
            return 0.0

    def _calculate_divergence_score(
        self, taker_data: TakerFlowData, oi_data: OptionOIData, correlation: float
    ) -> float:
        """计算分歧评分"""
        try:
            # 基础分歧评分：负相关性越强，分歧越大
            base_score = max(0, -correlation)  # 将负相关转为正分数

            # 方向分歧加权
            direction_weight = 0.0

            # 主流资金做多但期权防御增加
            if taker_data.buy_ratio > 0.6 and oi_data.put_oi_change > 0.1:
                direction_weight += 0.3

            # 主流资金做空但期权看涨增加
            if taker_data.sell_ratio > 0.6 and oi_data.call_oi_change > 0.1:
                direction_weight += 0.3

            # 强度分歧加权
            strength_weight = 0.0
            taker_strength = abs(taker_data.net_flow)
            oi_strength = abs(oi_data.net_oi_change)

            # 标准化强度（简化处理）
            normalized_taker = min(1.0, taker_strength / 1000000)  # 假设100万为满强度
            normalized_oi = min(1.0, oi_strength / 0.5)  # 假设50%变化为满强度

            if normalized_taker > 0.5 and normalized_oi > 0.5:
                strength_weight = 0.2

            # 综合评分
            final_score = base_score + direction_weight + strength_weight

            return min(1.0, final_score)

        except Exception:
            return 0.0

    def _calculate_confidence(
        self,
        taker_data_list: List[TakerFlowData],
        oi_data_list: List[OptionOIData],
        correlation: float,
        divergence_score: float,
    ) -> float:
        """计算置信度"""
        try:
            confidence = 0.0

            # 相关性稳定性
            if abs(correlation) > 0.3:
                confidence += 0.3

            # 数据量充足性
            if len(taker_data_list) >= self.config["lookback_window"]:
                confidence += 0.2

            # 成交量阈值
            latest_taker = taker_data_list[-1]
            total_volume = latest_taker.buy_volume + latest_taker.sell_volume
            if total_volume >= self.config["min_volume_threshold"]:
                confidence += 0.2

            # OI变化显著性
            latest_oi = oi_data_list[-1]
            if abs(latest_oi.net_oi_change) >= self.config["min_oi_change_threshold"]:
                confidence += 0.2

            # 分歧评分强度
            if divergence_score > 0.6:
                confidence += 0.1

            return min(1.0, confidence)

        except Exception:
            return 0.0

    def _detect_direction_conflict(
        self, taker_data: TakerFlowData, oi_data: OptionOIData
    ) -> bool:
        """检测方向冲突"""
        try:
            # 主流资金强势做多，但Put防御增加
            bullish_conflict = (
                taker_data.buy_ratio > 0.65 and oi_data.put_oi_change > 0.1
            )

            # 主流资金强势做空，但Call防御增加
            bearish_conflict = (
                taker_data.sell_ratio > 0.65 and oi_data.call_oi_change > 0.1
            )

            return bullish_conflict or bearish_conflict

        except Exception:
            return False

    def _calculate_strength_ratio(
        self, taker_data: TakerFlowData, oi_data: OptionOIData
    ) -> float:
        """计算强度比率"""
        try:
            # 标准化Taker流向强度
            taker_strength = (
                abs(taker_data.net_flow)
                / (taker_data.buy_volume + taker_data.sell_volume)
                if (taker_data.buy_volume + taker_data.sell_volume) > 0
                else 0
            )

            # 标准化OI变化强度
            oi_strength = abs(oi_data.net_oi_change)

            # 计算比率
            if oi_strength > 0:
                return taker_strength / oi_strength
            else:
                return float("inf") if taker_strength > 0 else 1.0

        except Exception:
            return 1.0

    async def _generate_signal(
        self, metrics: StructureDivergenceMetrics
    ) -> Optional[CausalSignal]:
        """生成结构分歧信号"""
        try:
            # 检查信号冷却时间
            if self._is_in_cooldown():
                return None

            # 检查分歧阈值
            if metrics.divergence_score < self.config["divergence_threshold"]:
                return None

            # 检查置信度阈值
            if metrics.confidence < 0.6:  # 最低置信度要求
                return None

            # 生成信号
            signal = CausalSignal(
                signal_type=SignalType.STRUCTURE_DIVERGENCE,
                strength=metrics.divergence_score,
                confidence=metrics.confidence,
                timestamp=metrics.timestamp,
                metadata={
                    "correlation": metrics.correlation,
                    "direction_conflict": metrics.direction_conflict,
                    "strength_ratio": metrics.strength_ratio,
                    **metrics.metadata,
                },
            )

            return signal

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error generating signal: {e}")
            return None

    def _is_in_cooldown(self) -> bool:
        """检查是否在信号冷却期内"""
        if not self.last_signal_time:
            return False

        cooldown_minutes = self.config["signal_cooldown_minutes"]
        time_since_last = datetime.now(timezone.utc) - self.last_signal_time

        return time_since_last.total_seconds() < (cooldown_minutes * 60)

    # 公共接口方法

    async def get_current_metrics(self) -> Optional[Dict[str, Any]]:
        """获取当前分歧指标"""
        if len(self.taker_flow_buffer) == 0 or len(self.option_oi_buffer) == 0:
            return None

        metrics = await self._calculate_divergence_metrics()
        if not metrics:
            return None

        return {
            "correlation": metrics.correlation,
            "divergence_score": metrics.divergence_score,
            "confidence": metrics.confidence,
            "direction_conflict": metrics.direction_conflict,
            "strength_ratio": metrics.strength_ratio,
            "timestamp": metrics.timestamp.isoformat(),
            "metadata": metrics.metadata,
        }

    async def get_statistics(self) -> Dict[str, Any]:
        """获取分析器统计信息"""
        return {
            "total_signals": self.total_signals,
            "successful_signals": self.successful_signals,
            "success_rate": self.successful_signals / self.total_signals
            if self.total_signals > 0
            else 0.0,
            "last_signal_time": self.last_signal_time.isoformat()
            if self.last_signal_time
            else None,
            "buffer_sizes": {
                "taker_flow": len(self.taker_flow_buffer),
                "option_oi": len(self.option_oi_buffer),
                "divergence_history": len(self.divergence_history),
            },
            "config": self.config,
        }

    async def force_analysis(self) -> Optional[CausalSignal]:
        """强制执行一次分析"""
        return await self._run_analysis()

    async def analyze_divergence(
        self, market_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        分析结构分歧信号（供CausalEngine调用的标准接口）

        Args:
            market_data: 市场数据，包含Binance和Deribit的数据

        Returns:
            分析结果字典，包含strength、confidence和metadata
        """
        try:
            # 从市场数据中提取Taker Flow数据
            taker_data = await self._extract_taker_flow_from_market_data(market_data)
            if not taker_data:
                return None

            # 从市场数据中提取期权OI数据
            oi_data = await self._extract_oi_data_from_market_data(market_data)
            if not oi_data:
                return None

            # 更新数据缓冲区
            self.taker_flow_buffer.append(taker_data)
            self.option_oi_buffer.append(oi_data)

            # 检查是否有足够的数据进行分析
            if len(self.taker_flow_buffer) < self.config["lookback_window"]:
                return None

            # 计算结构分歧指标
            divergence_metrics = await self._calculate_divergence_metrics()

            if not divergence_metrics:
                return None

            # 检查是否达到信号阈值
            if (
                divergence_metrics.divergence_score
                < self.config["divergence_threshold"]
            ):
                return None

            # 检查置信度阈值
            if divergence_metrics.confidence < 0.6:
                return None

            # 返回标准化的分析结果
            return {
                "strength": divergence_metrics.divergence_score,
                "confidence": divergence_metrics.confidence,
                "metadata": {
                    "correlation": divergence_metrics.correlation,
                    "direction_conflict": divergence_metrics.direction_conflict,
                    "strength_ratio": divergence_metrics.strength_ratio,
                    "taker_buy_ratio": taker_data.buy_ratio,
                    "taker_net_flow": taker_data.net_flow,
                    "call_put_ratio": oi_data.call_put_ratio,
                    "net_oi_change": oi_data.net_oi_change,
                    "analysis_type": "structure_divergence",
                    "lookback_window": self.config["lookback_window"],
                },
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error in analyze_divergence: {e}")
            return None

    async def _extract_taker_flow_from_market_data(
        self, market_data: Dict[str, Any]
    ) -> Optional[TakerFlowData]:
        """从市场数据中提取Taker Flow数据"""
        try:
            # 优先从市场数据获取
            binance_data = market_data.get("binance", {})
            if binance_data:
                buy_volume = float(binance_data.get("buy_volume", 0))
                sell_volume = float(binance_data.get("sell_volume", 0))
                total_volume = buy_volume + sell_volume

                if total_volume > 0:
                    return TakerFlowData(
                        buy_volume=buy_volume,
                        sell_volume=sell_volume,
                        buy_ratio=buy_volume / total_volume,
                        sell_ratio=sell_volume / total_volume,
                        net_flow=buy_volume - sell_volume,
                        timestamp=datetime.now(timezone.utc),
                    )

            # 回退到缓存数据
            return await self._get_taker_flow_data()

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error extracting taker flow from market data: {e}"
                )
            return None

    async def _extract_oi_data_from_market_data(
        self, market_data: Dict[str, Any]
    ) -> Optional[OptionOIData]:
        """从市场数据中提取期权OI数据"""
        try:
            # 优先从市场数据获取
            deribit_data = market_data.get("deribit", {})
            option_chain = market_data.get("option_chain", {})

            if deribit_data or option_chain:
                # 从期权链数据计算聚合OI
                call_oi = 0.0
                put_oi = 0.0
                call_oi_change = 0.0
                put_oi_change = 0.0

                if option_chain and isinstance(option_chain, dict):
                    for option_id, option_data in option_chain.items():
                        if isinstance(option_data, dict):
                            oi = float(option_data.get("open_interest", 0))
                            oi_change = float(option_data.get("oi_change_24h", 0))

                            if "C" in option_id or option_data.get("type") == "call":
                                call_oi += oi
                                call_oi_change += oi_change
                            elif "P" in option_id or option_data.get("type") == "put":
                                put_oi += oi
                                put_oi_change += oi_change

                # 如果期权链数据不足，使用直接数据
                if call_oi == 0 and put_oi == 0:
                    call_oi = float(deribit_data.get("call_oi", 0))
                    put_oi = float(deribit_data.get("put_oi", 0))
                    call_oi_change = float(deribit_data.get("call_oi_change_24h", 0))
                    put_oi_change = float(deribit_data.get("put_oi_change_24h", 0))

                if call_oi > 0 or put_oi > 0:
                    call_put_ratio = call_oi / put_oi if put_oi > 0 else float("inf")

                    return OptionOIData(
                        call_oi=call_oi,
                        put_oi=put_oi,
                        call_oi_change=call_oi_change,
                        put_oi_change=put_oi_change,
                        call_put_ratio=call_put_ratio,
                        net_oi_change=call_oi_change - put_oi_change,
                        timestamp=datetime.now(timezone.utc),
                    )

            # 回退到缓存数据
            return await self._get_option_oi_data()

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error extracting OI data from market data: {e}"
                )
            return None
