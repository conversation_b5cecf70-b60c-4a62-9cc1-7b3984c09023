"""
Monte Carlo期权定价引擎

实现基于Monte Carlo模拟的期权定价模型，支持：
1. 几何布朗运动价格路径模拟
2. 各种期权策略的Monte Carlo定价
3. 希腊字母和VaR计算
4. 多种随机过程模型（跳跃扩散、随机波动率）
"""

import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import numpy as np

from ..core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from ..core.event_bus import EventBus
from ..data.cache_manager import CacheManager


@dataclass
class MarketParameters:
    """市场参数"""

    spot_price: float
    risk_free_rate: float = 0.05
    dividend_yield: float = 0.0
    volatility: float = 0.0
    volatility_surface: Optional[Dict[Tuple[float, float], float]] = (
        None  # (strike, time) -> iv
    )
    jump_intensity: float = 0.0  # 跳跃强度
    jump_mean: float = 0.0  # 跳跃均值
    jump_std: float = 0.0  # 跳跃标准差


@dataclass
class SimulationConfig:
    """模拟配置"""

    num_simulations: int = 10000
    num_steps: int = 252  # 每年252个交易日
    antithetic_variates: bool = True  # 对偶变量方差减少技术
    control_variates: bool = True  # 控制变量方差减少技术
    random_seed: Optional[int] = None
    parallel_execution: bool = True
    num_threads: int = 4


@dataclass
class OptionContract:
    """期权合约"""

    option_type: str  # 'call', 'put'
    strike: float
    time_to_expiry: float  # 年化时间
    option_side: str = "long"  # 'long', 'short'
    quantity: float = 1.0


@dataclass
class StrategyLeg:
    """策略腿"""

    contract: OptionContract
    weight: float = 1.0  # 权重（正数买入，负数卖出）


@dataclass
class MonteCarloResult:
    """Monte Carlo结果"""

    option_price: float
    standard_error: float
    confidence_interval: Tuple[float, float]
    greeks: Dict[str, float] = field(default_factory=dict)
    var_95: float = 0.0
    var_99: float = 0.0
    expected_shortfall_95: float = 0.0
    path_statistics: Dict[str, float] = field(default_factory=dict)
    computation_time: float = 0.0


class MonteCarloEngine(BaseComponent):
    """Monte Carlo期权定价引擎"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("MonteCarloEngine", config)
        self.config = config or {}

        # 组件依赖
        self.cache_manager: Optional[CacheManager] = None
        self.event_bus: Optional[EventBus] = None

        # 线程池
        self.thread_pool = ThreadPoolExecutor(max_workers=4)

        # 缓存计算结果
        self.pricing_cache: Dict[str, MonteCarloResult] = {}
        self.cache_ttl = 300  # 5分钟缓存

        # 统计信息
        self.stats = {
            "total_simulations": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "avg_computation_time": 0.0,
        }

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            if self.logger:
                await self.logger.info("Initializing MonteCarloEngine")

            # 初始化线程池
            self.thread_pool = ThreadPoolExecutor(
                max_workers=self.config.get("num_threads", 4)
            )

            if self.logger:
                await self.logger.info("MonteCarloEngine initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"MonteCarloEngine initialization failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            if self.logger:
                await self.logger.info("MonteCarloEngine started")
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"MonteCarloEngine start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            if self.thread_pool:
                self.thread_pool.shutdown(wait=True)
            if self.logger:
                await self.logger.info("MonteCarloEngine stopped")
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"MonteCarloEngine stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            issues = []

            # 检查线程池状态
            if not self.thread_pool or self.thread_pool._shutdown:
                issues.append("Thread pool not available")

            # 检查缓存状态
            cache_hit_rate = (
                (
                    self.stats["cache_hits"]
                    / (self.stats["cache_hits"] + self.stats["cache_misses"])
                )
                if (self.stats["cache_hits"] + self.stats["cache_misses"]) > 0
                else 0
            )

            if cache_hit_rate < 0.3:  # 缓存命中率过低
                issues.append(f"Low cache hit rate: {cache_hit_rate:.2%}")

            if not issues:
                status = HealthStatus.HEALTHY
                message = "MonteCarloEngine is healthy"
            else:
                status = HealthStatus.DEGRADED
                message = f"MonteCarloEngine issues: {', '.join(issues)}"

            return HealthCheckResult(
                status=status,
                message=message,
                details={
                    "total_simulations": self.stats["total_simulations"],
                    "cache_hit_rate": cache_hit_rate,
                    "avg_computation_time": self.stats["avg_computation_time"],
                    "active_cache_entries": len(self.pricing_cache),
                },
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    async def price_option(
        self,
        contract: OptionContract,
        market_params: MarketParameters,
        sim_config: Optional[SimulationConfig] = None,
    ) -> MonteCarloResult:
        """为单个期权定价"""
        try:
            sim_config = sim_config or SimulationConfig()

            # 检查缓存
            cache_key = self._generate_cache_key(contract, market_params, sim_config)
            if cache_key in self.pricing_cache:
                result = self.pricing_cache[cache_key]
                if self._is_cache_valid(result):
                    self.stats["cache_hits"] += 1
                    return result
                else:
                    del self.pricing_cache[cache_key]

            self.stats["cache_misses"] += 1

            # 执行Monte Carlo模拟
            if sim_config.parallel_execution:
                result = await self._price_option_parallel(
                    contract, market_params, sim_config
                )
            else:
                result = await self._price_option_sequential(
                    contract, market_params, sim_config
                )

            # 缓存结果
            self.pricing_cache[cache_key] = result

            # 更新统计
            self.stats["total_simulations"] += sim_config.num_simulations
            self.stats["avg_computation_time"] = (
                self.stats["avg_computation_time"] * (self.stats["cache_misses"] - 1)
                + result.computation_time
            ) / self.stats["cache_misses"]

            return result

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Option pricing failed: {e}")
            raise

    async def price_strategy(
        self,
        strategy_legs: List[StrategyLeg],
        market_params: MarketParameters,
        sim_config: Optional[SimulationConfig] = None,
    ) -> MonteCarloResult:
        """为期权策略定价"""
        try:
            sim_config = sim_config or SimulationConfig()

            # 生成价格路径
            paths = await self._generate_price_paths(market_params, sim_config)

            # 计算策略在每条路径上的价值
            strategy_values = await self._calculate_strategy_values(
                strategy_legs, paths, market_params
            )

            # 计算统计指标
            mean_value = np.mean(strategy_values)
            std_error = np.std(strategy_values) / np.sqrt(len(strategy_values))

            # 置信区间
            confidence_95 = 1.96 * std_error
            confidence_interval = (
                mean_value - confidence_95,
                mean_value + confidence_95,
            )

            # VaR计算
            var_95 = np.percentile(strategy_values, 5)
            var_99 = np.percentile(strategy_values, 1)

            # Expected Shortfall (CVaR)
            es_95 = np.mean(strategy_values[strategy_values <= var_95])

            # 路径统计
            path_stats = {
                "mean": float(np.mean(strategy_values)),
                "std": float(np.std(strategy_values)),
                "skewness": float(self._calculate_skewness(strategy_values)),
                "kurtosis": float(self._calculate_kurtosis(strategy_values)),
                "min": float(np.min(strategy_values)),
                "max": float(np.max(strategy_values)),
            }

            return MonteCarloResult(
                option_price=mean_value,
                standard_error=std_error,
                confidence_interval=confidence_interval,
                var_95=var_95,
                var_99=var_99,
                expected_shortfall_95=es_95,
                path_statistics=path_stats,
                computation_time=0.0,  # 由调用方设置
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Strategy pricing failed: {e}")
            raise

    async def calculate_greeks(
        self,
        contract: OptionContract,
        market_params: MarketParameters,
        sim_config: Optional[SimulationConfig] = None,
    ) -> Dict[str, float]:
        """计算希腊字母"""
        try:
            sim_config = sim_config or SimulationConfig()

            # 使用有限差分法计算希腊字母
            greeks = {}

            # Delta: dV/dS
            spot_shift = market_params.spot_price * 0.01  # 1%扰动

            market_params_up = MarketParameters(
                spot_price=market_params.spot_price + spot_shift,
                risk_free_rate=market_params.risk_free_rate,
                dividend_yield=market_params.dividend_yield,
                volatility=market_params.volatility,
            )
            market_params_down = MarketParameters(
                spot_price=market_params.spot_price - spot_shift,
                risk_free_rate=market_params.risk_free_rate,
                dividend_yield=market_params.dividend_yield,
                volatility=market_params.volatility,
            )

            price_up = await self.price_option(contract, market_params_up, sim_config)
            price_down = await self.price_option(
                contract, market_params_down, sim_config
            )

            greeks["delta"] = (price_up.option_price - price_down.option_price) / (
                2 * spot_shift
            )

            # Gamma: d²V/dS²
            price_base = await self.price_option(contract, market_params, sim_config)
            greeks["gamma"] = (
                price_up.option_price
                - 2 * price_base.option_price
                + price_down.option_price
            ) / (spot_shift**2)

            # Theta: dV/dt
            time_shift = 1 / 365  # 1天
            contract_theta = OptionContract(
                option_type=contract.option_type,
                strike=contract.strike,
                time_to_expiry=max(0, contract.time_to_expiry - time_shift),
                option_side=contract.option_side,
                quantity=contract.quantity,
            )

            if contract_theta.time_to_expiry > 0:
                price_theta = await self.price_option(
                    contract_theta, market_params, sim_config
                )
                greeks["theta"] = (
                    -(price_theta.option_price - price_base.option_price) / time_shift
                )
            else:
                greeks["theta"] = 0.0

            # Vega: dV/dσ
            vol_shift = 0.01  # 1%波动率扰动
            market_params_vega = MarketParameters(
                spot_price=market_params.spot_price,
                risk_free_rate=market_params.risk_free_rate,
                dividend_yield=market_params.dividend_yield,
                volatility=market_params.volatility + vol_shift,
            )

            price_vega = await self.price_option(
                contract, market_params_vega, sim_config
            )
            greeks["vega"] = (
                price_vega.option_price - price_base.option_price
            ) / vol_shift

            # Rho: dV/dr
            rate_shift = 0.01  # 1%利率扰动
            market_params_rho = MarketParameters(
                spot_price=market_params.spot_price,
                risk_free_rate=market_params.risk_free_rate + rate_shift,
                dividend_yield=market_params.dividend_yield,
                volatility=market_params.volatility,
            )

            price_rho = await self.price_option(contract, market_params_rho, sim_config)
            greeks["rho"] = (
                price_rho.option_price - price_base.option_price
            ) / rate_shift

            return greeks

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Greeks calculation failed: {e}")
            return {}

    async def _price_option_parallel(
        self,
        contract: OptionContract,
        market_params: MarketParameters,
        sim_config: SimulationConfig,
    ) -> MonteCarloResult:
        """并行期权定价"""
        start_time = datetime.now()

        # 将模拟分成多个批次
        batch_size = sim_config.num_simulations // sim_config.num_threads

        # 创建异步任务
        loop = asyncio.get_running_loop()
        tasks = []

        for i in range(sim_config.num_threads):
            batch_sim_config = SimulationConfig(
                num_simulations=batch_size,
                num_steps=sim_config.num_steps,
                antithetic_variates=sim_config.antithetic_variates,
                control_variates=False,  # 控制变量在合并时处理
                random_seed=sim_config.random_seed + i
                if sim_config.random_seed
                else None,
                parallel_execution=False,
            )

            task = loop.run_in_executor(
                self.thread_pool,
                self._price_option_sync,
                contract,
                market_params,
                batch_sim_config,
            )
            tasks.append(task)

        # 等待所有任务完成
        batch_results = await asyncio.gather(*tasks)

        # 合并结果
        all_values = np.concatenate([result["values"] for result in batch_results])

        # 计算最终统计
        mean_value = np.mean(all_values)
        std_error = np.std(all_values) / np.sqrt(len(all_values))

        # 应用控制变量方差减少技术
        if sim_config.control_variates:
            mean_value, std_error = self._apply_control_variates(
                all_values, market_params, contract
            )

        # 置信区间
        confidence_95 = 1.96 * std_error
        confidence_interval = (mean_value - confidence_95, mean_value + confidence_95)

        # VaR计算
        var_95 = np.percentile(all_values, 5)
        var_99 = np.percentile(all_values, 1)
        es_95 = np.mean(all_values[all_values <= var_95])

        computation_time = (datetime.now() - start_time).total_seconds()

        return MonteCarloResult(
            option_price=mean_value,
            standard_error=std_error,
            confidence_interval=confidence_interval,
            var_95=var_95,
            var_99=var_99,
            expected_shortfall_95=es_95,
            computation_time=computation_time,
        )

    async def _price_option_sequential(
        self,
        contract: OptionContract,
        market_params: MarketParameters,
        sim_config: SimulationConfig,
    ) -> MonteCarloResult:
        """顺序期权定价"""
        start_time = datetime.now()

        # 生成价格路径
        paths = await self._generate_price_paths(market_params, sim_config)

        # 计算期权价值
        option_values = self._calculate_option_payoffs(
            contract, paths[:, -1], market_params
        )

        # 统计计算
        mean_value = np.mean(option_values)
        std_error = np.std(option_values) / np.sqrt(len(option_values))

        # 应用方差减少技术
        if sim_config.control_variates:
            mean_value, std_error = self._apply_control_variates(
                option_values, market_params, contract
            )

        computation_time = (datetime.now() - start_time).total_seconds()

        return MonteCarloResult(
            option_price=mean_value,
            standard_error=std_error,
            confidence_interval=(
                mean_value - 1.96 * std_error,
                mean_value + 1.96 * std_error,
            ),
            computation_time=computation_time,
        )

    def _price_option_sync(
        self,
        contract: OptionContract,
        market_params: MarketParameters,
        sim_config: SimulationConfig,
    ) -> Dict[str, Any]:
        """同步期权定价（用于线程池）"""
        # 设置随机种子
        if sim_config.random_seed:
            np.random.seed(sim_config.random_seed)

        # 生成价格路径
        paths = self._generate_price_paths_sync(market_params, sim_config)

        # 计算期权价值
        option_values = self._calculate_option_payoffs(
            contract, paths[:, -1], market_params
        )

        return {"values": option_values}

    async def _generate_price_paths(
        self, market_params: MarketParameters, sim_config: SimulationConfig
    ) -> np.ndarray:
        """生成价格路径"""
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(
            self.thread_pool, self._generate_price_paths_sync, market_params, sim_config
        )

    def _generate_price_paths_sync(
        self, market_params: MarketParameters, sim_config: SimulationConfig
    ) -> np.ndarray:
        """同步生成价格路径"""
        dt = 1.0 / sim_config.num_steps  # 时间步长
        num_sims = sim_config.num_simulations
        num_steps = sim_config.num_steps

        # 初始化路径矩阵
        paths = np.zeros((num_sims, num_steps + 1))
        paths[:, 0] = market_params.spot_price

        # 生成随机数
        if sim_config.antithetic_variates:
            # 对偶变量技术
            half_sims = num_sims // 2
            randn = np.random.randn(half_sims, num_steps)
            randn_full = np.vstack([randn, -randn])
            if num_sims % 2 == 1:
                randn_full = np.vstack([randn_full, np.random.randn(1, num_steps)])
        else:
            randn_full = np.random.randn(num_sims, num_steps)

        # 几何布朗运动参数
        mu = market_params.risk_free_rate - market_params.dividend_yield
        sigma = market_params.volatility

        # 生成路径
        for i in range(num_steps):
            dW = randn_full[:, i] * np.sqrt(dt)

            # 基础GBM
            drift = (mu - 0.5 * sigma**2) * dt
            diffusion = sigma * dW

            # 跳跃过程（可选）
            if market_params.jump_intensity > 0:
                jump_prob = market_params.jump_intensity * dt
                jump_occurs = np.random.rand(num_sims) < jump_prob
                jumps = np.where(
                    jump_occurs,
                    np.random.normal(
                        market_params.jump_mean, market_params.jump_std, num_sims
                    ),
                    0.0,
                )
                diffusion += jumps

            # 更新价格
            paths[:, i + 1] = paths[:, i] * np.exp(drift + diffusion)

        return paths

    def _calculate_option_payoffs(
        self,
        contract: OptionContract,
        final_prices: np.ndarray,
        market_params: MarketParameters,
    ) -> np.ndarray:
        """计算期权收益"""
        if contract.option_type.lower() == "call":
            payoffs = np.maximum(final_prices - contract.strike, 0)
        elif contract.option_type.lower() == "put":
            payoffs = np.maximum(contract.strike - final_prices, 0)
        else:
            raise ValueError(f"Unknown option type: {contract.option_type}")

        # 贴现到现值
        discount_factor = np.exp(
            -market_params.risk_free_rate * contract.time_to_expiry
        )
        present_values = payoffs * discount_factor

        # 考虑多空方向
        if contract.option_side.lower() == "short":
            present_values = -present_values

        return present_values * contract.quantity

    async def _calculate_strategy_values(
        self,
        strategy_legs: List[StrategyLeg],
        paths: np.ndarray,
        market_params: MarketParameters,
    ) -> np.ndarray:
        """计算策略价值"""
        final_prices = paths[:, -1]
        strategy_values = np.zeros(len(final_prices))

        for leg in strategy_legs:
            leg_values = self._calculate_option_payoffs(
                leg.contract, final_prices, market_params
            )
            strategy_values += leg.weight * leg_values

        return strategy_values

    def _apply_control_variates(
        self,
        option_values: np.ndarray,
        market_params: MarketParameters,
        contract: OptionContract,
    ) -> Tuple[float, float]:
        """应用控制变量技术"""
        try:
            # 使用几何布朗运动下的解析解作为控制变量
            from scipy.stats import norm

            S = market_params.spot_price
            K = contract.strike
            T = contract.time_to_expiry
            r = market_params.risk_free_rate
            sigma = market_params.volatility

            if T <= 0:
                analytical_price = (
                    max(S - K, 0)
                    if contract.option_type.lower() == "call"
                    else max(K - S, 0)
                )
            else:
                d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
                d2 = d1 - sigma * np.sqrt(T)

                if contract.option_type.lower() == "call":
                    analytical_price = S * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(
                        d2
                    )
                else:
                    analytical_price = K * np.exp(-r * T) * norm.cdf(
                        -d2
                    ) - S * norm.cdf(-d1)

            # 计算控制变量系数
            mean_mc = np.mean(option_values)
            covariance = np.cov(option_values, option_values)[0, 1]  # 这里简化处理
            variance_cv = np.var(option_values)  # 控制变量的方差

            if variance_cv > 0:
                beta = covariance / variance_cv
                controlled_values = mean_mc + beta * (analytical_price - mean_mc)
                # 确保sqrt参数非负，避免RuntimeWarning
                sqrt_term = max(0, 1 - beta**2)
                controlled_std = np.std(option_values) * np.sqrt(sqrt_term)
                return controlled_values, controlled_std / np.sqrt(len(option_values))
            else:
                return mean_mc, np.std(option_values) / np.sqrt(len(option_values))

        except Exception:
            # 如果控制变量失败，返回原始结果
            return np.mean(option_values), np.std(option_values) / np.sqrt(
                len(option_values)
            )

    def _calculate_skewness(self, values: np.ndarray) -> float:
        """计算偏度"""
        mean = np.mean(values)
        std = np.std(values)
        if std == 0:
            return 0.0
        return np.mean(((values - mean) / std) ** 3)

    def _calculate_kurtosis(self, values: np.ndarray) -> float:
        """计算峰度"""
        mean = np.mean(values)
        std = np.std(values)
        if std == 0:
            return 0.0
        return np.mean(((values - mean) / std) ** 4) - 3  # 减去3得到超额峰度

    def _generate_cache_key(
        self,
        contract: OptionContract,
        market_params: MarketParameters,
        sim_config: SimulationConfig,
    ) -> str:
        """生成缓存键"""
        key_parts = [
            contract.option_type,
            f"{contract.strike:.2f}",
            f"{contract.time_to_expiry:.4f}",
            contract.option_side,
            f"{contract.quantity:.2f}",
            f"{market_params.spot_price:.2f}",
            f"{market_params.volatility:.4f}",
            f"{market_params.risk_free_rate:.4f}",
            f"{sim_config.num_simulations}",
        ]
        return "_".join(key_parts)

    def _is_cache_valid(self, result: MonteCarloResult) -> bool:
        """检查缓存是否有效"""
        # 简单的时间检查，实际应用中可以更复杂
        return True  # 暂时总是有效

    async def get_engine_statistics(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        return {
            "total_simulations": self.stats["total_simulations"],
            "cache_hits": self.stats["cache_hits"],
            "cache_misses": self.stats["cache_misses"],
            "cache_hit_rate": self.stats["cache_hits"]
            / (self.stats["cache_hits"] + self.stats["cache_misses"])
            if (self.stats["cache_hits"] + self.stats["cache_misses"]) > 0
            else 0,
            "avg_computation_time": self.stats["avg_computation_time"],
            "active_cache_entries": len(self.pricing_cache),
            "thread_pool_active": not (
                self.thread_pool._shutdown if self.thread_pool else True
            ),
        }

    async def clear_cache(self):
        """清空缓存"""
        self.pricing_cache.clear()
        if self.logger:
            await self.logger.info("Monte Carlo pricing cache cleared")

    def set_cache_manager(self, cache_manager: CacheManager):
        """设置缓存管理器"""
        self.cache_manager = cache_manager

    def set_event_bus(self, event_bus: EventBus):
        """设置事件总线"""
        self.event_bus = event_bus
