"""
波动率错配分析器

实现Binance永续与Deribit期权的隐含波动率对比分析：
1. 期限结构扭曲识别算法
2. 波动率套利机会评估
3. 隐含波动率偏差检测
4. 波动率曲面分析

Author: BTC Options Grid Trading System
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Any, Dict, List, Optional

import numpy as np

from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from src.core.config_manager import ConfigManager


class VolatilityMismatchType(Enum):
    """波动率错配类型"""

    TERM_STRUCTURE_INVERSION = "term_structure_inversion"  # 期限结构倒挂
    SKEW_ANOMALY = "skew_anomaly"  # 偏度异常
    SURFACE_DISTORTION = "surface_distortion"  # 曲面扭曲
    ARBITRAGE_OPPORTUNITY = "arbitrage_opportunity"  # 套利机会


@dataclass
class VolatilityData:
    """波动率数据"""

    timestamp: datetime
    strike: float
    expiry: datetime
    implied_vol: float
    mark_price: float
    bid_vol: Optional[float] = None
    ask_vol: Optional[float] = None
    volume: float = 0.0
    open_interest: float = 0.0


@dataclass
class PerpetualVolatilityData:
    """永续合约波动率数据"""

    timestamp: datetime
    realized_vol_1h: float
    realized_vol_4h: float
    realized_vol_24h: float
    funding_rate: float
    mark_price: float
    volume_24h: float


@dataclass
class VolatilityMismatchSignal:
    """波动率错配信号"""

    mismatch_type: VolatilityMismatchType
    strength: float  # 0-1
    confidence: float  # 0-1
    timestamp: datetime
    expiry: datetime
    strike: Optional[float] = None
    implied_vol: Optional[float] = None
    realized_vol: Optional[float] = None
    vol_spread: Optional[float] = None
    arbitrage_profit: Optional[float] = None
    metadata: Dict[str, Any] = None


@dataclass
class VolatilityMismatchMetrics:
    """波动率错配指标"""

    avg_vol_spread: float
    max_vol_spread: float
    term_structure_slope: float
    skew_coefficient: float
    arbitrage_opportunities: int
    total_arbitrage_profit: float
    signal_count_24h: int
    accuracy_rate: float
    error_count: int = 0


class VolatilityMismatchAnalyzer(BaseComponent):
    """波动率错配分析器"""

    def __init__(self, config_manager: ConfigManager):
        super().__init__("volatility_analyzer")
        self.config_manager = config_manager
        self.config = {}

        # 分析参数
        self.analysis_interval = 60  # 60秒分析间隔
        self.lookback_window = 20  # 20分钟回看窗口
        self.vol_threshold = 0.05  # 5%波动率阈值
        self.min_arbitrage_profit = 0.001  # 0.1%最小套利利润
        self.signal_cooldown = timedelta(minutes=15)  # 15分钟信号冷却

        # 数据存储
        self.option_vol_history: List[VolatilityData] = []
        self.perp_vol_history: List[PerpetualVolatilityData] = []
        self.signal_history: List[VolatilityMismatchSignal] = []
        self.last_signal_time: Dict[VolatilityMismatchType, datetime] = {}

        # 运行状态
        self._analysis_task: Optional[asyncio.Task] = None

        # 统计指标 - 存储在custom_metrics中
        self.volatility_metrics = VolatilityMismatchMetrics(
            avg_vol_spread=0.0,
            max_vol_spread=0.0,
            term_structure_slope=0.0,
            skew_coefficient=0.0,
            arbitrage_opportunities=0,
            total_arbitrage_profit=0.0,
            signal_count_24h=0,
            accuracy_rate=0.0,
        )

    async def _initialize_impl(self) -> bool:
        """具体初始化实现"""
        try:
            # 加载配置
            analyzer_config = self.config_manager.get_section("volatility_analyzer")

            # 更新配置参数
            self.analysis_interval = analyzer_config.get("analysis_interval", 60)
            self.lookback_window = analyzer_config.get("lookback_window", 20)
            self.vol_threshold = analyzer_config.get("vol_threshold", 0.05)
            self.min_arbitrage_profit = analyzer_config.get(
                "min_arbitrage_profit", 0.001
            )

            # 信号冷却时间
            cooldown_minutes = analyzer_config.get("signal_cooldown_minutes", 15)
            self.signal_cooldown = timedelta(minutes=cooldown_minutes)

            # 存储配置
            self.config = analyzer_config

            if self.logger:
                await self.logger.info(
                    "VolatilityMismatchAnalyzer initialized successfully"
                )

            return True

        except Exception as e:
            # 即使没有logger也要记录错误
            error_msg = f"Failed to initialize VolatilityMismatchAnalyzer: {e}"
            if self.logger:
                await self.logger.error(error_msg)
            else:
                print(error_msg)  # 测试环境下的fallback
            return False

    async def _start_impl(self) -> bool:
        """具体启动实现"""
        try:
            # 启动分析任务
            self._analysis_task = asyncio.create_task(self._analysis_loop())

            # 等待一小段时间确保任务启动
            await asyncio.sleep(0.01)

            # 检查任务是否还在运行
            if self._analysis_task.done():
                # 如果任务已经完成，检查是否有异常
                try:
                    self._analysis_task.result()
                except Exception as e:
                    if self.logger:
                        await self.logger.error(
                            f"Analysis task failed immediately: {e}"
                        )
                    return False

            if self.logger:
                await self.logger.info(
                    "VolatilityMismatchAnalyzer started successfully"
                )

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Failed to start VolatilityMismatchAnalyzer: {e}"
                )
            return False

    async def _stop_impl(self) -> bool:
        """具体停止实现"""
        try:
            # 停止分析任务
            if self._analysis_task and not self._analysis_task.done():
                self._analysis_task.cancel()
                try:
                    await self._analysis_task
                except asyncio.CancelledError:
                    pass

            if self.logger:
                await self.logger.info(
                    "VolatilityMismatchAnalyzer stopped successfully"
                )

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Failed to stop VolatilityMismatchAnalyzer: {e}"
                )
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """具体健康检查实现"""
        try:
            # 检查基本状态
            if not self.is_running:
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message="VolatilityMismatchAnalyzer is not running",
                    details={},
                )

            # 检查分析任务状态
            if self._analysis_task and self._analysis_task.done():
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message="Analysis task has stopped unexpectedly",
                    details={"task_done": True},
                )

            # 检查数据新鲜度
            current_time = datetime.now(timezone.utc)
            data_age_minutes = 0

            if self.option_vol_history:
                latest_data = max(self.option_vol_history, key=lambda x: x.timestamp)
                data_age_minutes = (
                    current_time - latest_data.timestamp
                ).total_seconds() / 60

            if data_age_minutes > 10:  # 数据超过10分钟认为不健康
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"Data is {data_age_minutes:.1f} minutes old",
                    details={"data_age_minutes": data_age_minutes},
                )

            # 检查信号生成
            recent_signals = len(
                [
                    s
                    for s in self.signal_history
                    if (current_time - s.timestamp).total_seconds() < 3600
                ]
            )  # 1小时内

            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message="VolatilityMismatchAnalyzer is healthy",
                details={
                    "data_age_minutes": data_age_minutes,
                    "recent_signals": recent_signals,
                    "total_signals": len(self.signal_history),
                    "option_data_points": len(self.option_vol_history),
                    "perp_data_points": len(self.perp_vol_history),
                },
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
                details={"error": str(e)},
            )

    async def _analysis_loop(self):
        """主分析循环"""
        while True:
            try:
                await self._run_volatility_analysis()
                await asyncio.sleep(self.analysis_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Error in volatility analysis loop: {e}")
                await asyncio.sleep(self.analysis_interval)

    async def _run_volatility_analysis(self):
        """运行波动率分析"""
        try:
            # 获取最新数据
            option_data = await self._get_option_volatility_data()
            perp_data = await self._get_perpetual_volatility_data()

            if not option_data or not perp_data:
                return

            # 更新数据历史
            self._update_data_history(option_data, perp_data)

            # 执行各种分析
            signals = []

            # 1. 期限结构分析
            term_signals = await self._analyze_term_structure(option_data)
            signals.extend(term_signals)

            # 2. 波动率偏度分析
            skew_signals = await self._analyze_volatility_skew(option_data)
            signals.extend(skew_signals)

            # 3. 隐含vs实现波动率分析
            iv_rv_signals = await self._analyze_iv_vs_rv(option_data, perp_data)
            signals.extend(iv_rv_signals)

            # 4. 套利机会识别
            arbitrage_signals = await self._identify_arbitrage_opportunities(
                option_data, perp_data
            )
            signals.extend(arbitrage_signals)

            # 处理信号
            for signal in signals:
                await self._process_volatility_signal(signal)

            # 更新指标
            self._update_metrics()

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error in volatility analysis: {e}")

    async def _get_option_volatility_data(self) -> Optional[List[VolatilityData]]:
        """获取期权波动率数据"""
        try:
            if not self.cache_manager:
                return None

            # 从缓存获取期权数据
            option_data = await self.cache_manager.get("deribit:option_chain")
            if not option_data:
                return None

            vol_data = []
            current_time = datetime.now(timezone.utc)

            for option in option_data.get("options", []):
                if option.get("implied_volatility"):
                    vol_data.append(
                        VolatilityData(
                            timestamp=current_time,
                            strike=option["strike"],
                            expiry=datetime.fromisoformat(option["expiry"]),
                            implied_vol=option["implied_volatility"],
                            mark_price=option["mark_price"],
                            bid_vol=option.get("bid_iv"),
                            ask_vol=option.get("ask_iv"),
                            volume=option.get("volume", 0),
                            open_interest=option.get("open_interest", 0),
                        )
                    )

            return vol_data

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error getting option volatility data: {e}")
            return None

    async def _get_perpetual_volatility_data(self) -> Optional[PerpetualVolatilityData]:
        """获取永续合约波动率数据"""
        try:
            if not self.cache_manager:
                return None

            # 从缓存获取永续合约数据
            perp_data = await self.cache_manager.get("binance:perpetual")
            if not perp_data:
                return None

            current_time = datetime.now(timezone.utc)

            return PerpetualVolatilityData(
                timestamp=current_time,
                realized_vol_1h=perp_data.get("realized_vol_1h", 0),
                realized_vol_4h=perp_data.get("realized_vol_4h", 0),
                realized_vol_24h=perp_data.get("realized_vol_24h", 0),
                funding_rate=perp_data.get("funding_rate", 0),
                mark_price=perp_data.get("mark_price", 0),
                volume_24h=perp_data.get("volume_24h", 0),
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error getting perpetual volatility data: {e}")
            return None

    def _update_data_history(
        self, option_data: List[VolatilityData], perp_data: PerpetualVolatilityData
    ):
        """更新数据历史"""
        # 添加新数据
        self.option_vol_history.extend(option_data)
        self.perp_vol_history.append(perp_data)

        # 清理过期数据
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)

        self.option_vol_history = [
            data for data in self.option_vol_history if data.timestamp >= cutoff_time
        ]

        self.perp_vol_history = [
            data for data in self.perp_vol_history if data.timestamp >= cutoff_time
        ]

    async def _analyze_term_structure(
        self, option_data: List[VolatilityData]
    ) -> List[VolatilityMismatchSignal]:
        """分析期限结构"""
        signals = []

        try:
            # 按到期日分组
            expiry_groups = {}
            for data in option_data:
                expiry_key = data.expiry.date()
                if expiry_key not in expiry_groups:
                    expiry_groups[expiry_key] = []
                expiry_groups[expiry_key].append(data)

            # 计算各期限的ATM波动率
            term_structure = []
            current_time = datetime.now(timezone.utc)

            for expiry_date, options in expiry_groups.items():
                if len(options) < 3:  # 需要足够的期权数据
                    continue

                # 找到ATM期权
                spot_price = options[0].mark_price  # 假设mark_price接近现货价格
                atm_option = min(options, key=lambda x: abs(x.strike - spot_price))

                days_to_expiry = (
                    datetime.combine(expiry_date, datetime.min.time()).replace(
                        tzinfo=timezone.utc
                    )
                    - current_time
                ).days
                if days_to_expiry > 0:
                    term_structure.append((days_to_expiry, atm_option.implied_vol))

            # 检查期限结构倒挂
            if len(term_structure) >= 2:
                term_structure.sort(key=lambda x: x[0])  # 按到期日排序

                for i in range(len(term_structure) - 1):
                    short_term = term_structure[i]
                    long_term = term_structure[i + 1]

                    # 检查倒挂（短期波动率高于长期）
                    vol_diff = short_term[1] - long_term[1]
                    if vol_diff > self.vol_threshold:
                        strength = min(1.0, vol_diff / (2 * self.vol_threshold))
                        confidence = 0.8 if vol_diff > 2 * self.vol_threshold else 0.6

                        signal = VolatilityMismatchSignal(
                            mismatch_type=VolatilityMismatchType.TERM_STRUCTURE_INVERSION,
                            strength=strength,
                            confidence=confidence,
                            timestamp=current_time,
                            expiry=datetime.combine(
                                expiry_date, datetime.min.time()
                            ).replace(tzinfo=timezone.utc),
                            vol_spread=vol_diff,
                            metadata={
                                "short_term_days": short_term[0],
                                "long_term_days": long_term[0],
                                "short_term_vol": short_term[1],
                                "long_term_vol": long_term[1],
                            },
                        )
                        signals.append(signal)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error in term structure analysis: {e}")

        return signals

    async def _analyze_volatility_skew(
        self, option_data: List[VolatilityData]
    ) -> List[VolatilityMismatchSignal]:
        """分析波动率偏度"""
        signals = []

        try:
            # 按到期日分组
            expiry_groups = {}
            for data in option_data:
                expiry_key = data.expiry.date()
                if expiry_key not in expiry_groups:
                    expiry_groups[expiry_key] = []
                expiry_groups[expiry_key].append(data)

            current_time = datetime.now(timezone.utc)

            for expiry_date, options in expiry_groups.items():
                if len(options) < 5:  # 需要足够的期权数据
                    continue

                # 计算波动率偏度
                strikes = [opt.strike for opt in options]
                vols = [opt.implied_vol for opt in options]

                if len(strikes) >= 5:
                    # 使用线性回归计算偏度
                    spot_price = np.mean([opt.mark_price for opt in options])
                    moneyness = [(strike / spot_price - 1) for strike in strikes]

                    # 计算偏度系数
                    skew_coeff = (
                        np.corrcoef(moneyness, vols)[0, 1] if len(moneyness) > 1 else 0
                    )

                    # 检查异常偏度
                    if abs(skew_coeff) > 0.7:  # 强偏度阈值
                        strength = min(1.0, abs(skew_coeff))
                        confidence = 0.7 if abs(skew_coeff) > 0.8 else 0.5

                        signal = VolatilityMismatchSignal(
                            mismatch_type=VolatilityMismatchType.SKEW_ANOMALY,
                            strength=strength,
                            confidence=confidence,
                            timestamp=current_time,
                            expiry=datetime.combine(
                                expiry_date, datetime.min.time()
                            ).replace(tzinfo=timezone.utc),
                            metadata={
                                "skew_coefficient": skew_coeff,
                                "option_count": len(options),
                                "vol_range": max(vols) - min(vols),
                            },
                        )
                        signals.append(signal)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error in volatility skew analysis: {e}")

        return signals

    async def _analyze_iv_vs_rv(
        self, option_data: List[VolatilityData], perp_data: PerpetualVolatilityData
    ) -> List[VolatilityMismatchSignal]:
        """分析隐含波动率vs实现波动率"""
        signals = []

        try:
            if not option_data or not perp_data:
                return signals

            # 计算ATM隐含波动率
            spot_price = perp_data.mark_price
            atm_options = [
                opt for opt in option_data if abs(opt.strike / spot_price - 1) < 0.05
            ]

            if not atm_options:
                return signals

            avg_iv = np.mean([opt.implied_vol for opt in atm_options])
            realized_vol = perp_data.realized_vol_24h

            # 计算IV-RV差异
            vol_spread = avg_iv - realized_vol

            if abs(vol_spread) > self.vol_threshold:
                strength = min(1.0, abs(vol_spread) / (2 * self.vol_threshold))
                confidence = 0.8 if abs(vol_spread) > 2 * self.vol_threshold else 0.6

                # 判断套利方向
                arbitrage_direction = "buy_vol" if vol_spread < 0 else "sell_vol"

                signal = VolatilityMismatchSignal(
                    mismatch_type=VolatilityMismatchType.ARBITRAGE_OPPORTUNITY,
                    strength=strength,
                    confidence=confidence,
                    timestamp=datetime.now(timezone.utc),
                    expiry=min(opt.expiry for opt in atm_options),
                    implied_vol=avg_iv,
                    realized_vol=realized_vol,
                    vol_spread=vol_spread,
                    metadata={
                        "arbitrage_direction": arbitrage_direction,
                        "atm_option_count": len(atm_options),
                        "funding_rate": perp_data.funding_rate,
                    },
                )
                signals.append(signal)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error in IV vs RV analysis: {e}")

        return signals

    async def _identify_arbitrage_opportunities(
        self, option_data: List[VolatilityData], perp_data: PerpetualVolatilityData
    ) -> List[VolatilityMismatchSignal]:
        """识别套利机会"""
        signals = []

        try:
            if not option_data or not perp_data:
                return signals

            current_time = datetime.now(timezone.utc)
            spot_price = perp_data.mark_price

            # 寻找价格错配的期权
            for option in option_data:
                # 计算理论价格（简化Black-Scholes）
                days_to_expiry = (option.expiry - current_time).days
                if days_to_expiry <= 0:
                    continue

                time_to_expiry = days_to_expiry / 365.0
                moneyness = option.strike / spot_price

                # 简化的理论波动率估算
                theoretical_vol = self._estimate_theoretical_volatility(
                    moneyness, time_to_expiry, perp_data.realized_vol_24h
                )

                vol_diff = option.implied_vol - theoretical_vol
                potential_profit = abs(vol_diff) * spot_price * 0.1  # 简化利润估算

                if potential_profit > self.min_arbitrage_profit * spot_price:
                    strength = min(1.0, potential_profit / (spot_price * 0.01))
                    confidence = 0.7 if potential_profit > spot_price * 0.005 else 0.5

                    signal = VolatilityMismatchSignal(
                        mismatch_type=VolatilityMismatchType.ARBITRAGE_OPPORTUNITY,
                        strength=strength,
                        confidence=confidence,
                        timestamp=current_time,
                        expiry=option.expiry,
                        strike=option.strike,
                        implied_vol=option.implied_vol,
                        arbitrage_profit=potential_profit,
                        metadata={
                            "theoretical_vol": theoretical_vol,
                            "vol_difference": vol_diff,
                            "moneyness": moneyness,
                            "time_to_expiry": time_to_expiry,
                        },
                    )
                    signals.append(signal)

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error identifying arbitrage opportunities: {e}"
                )

        return signals

    def _estimate_theoretical_volatility(
        self, moneyness: float, time_to_expiry: float, realized_vol: float
    ) -> float:
        """估算理论波动率"""
        # 简化的波动率模型
        base_vol = realized_vol

        # 时间衰减调整
        time_adjustment = 1.0 + (0.5 - time_to_expiry) * 0.1

        # 货币性调整
        moneyness_adjustment = 1.0 + abs(moneyness - 1.0) * 0.2

        return base_vol * time_adjustment * moneyness_adjustment

    async def _process_volatility_signal(self, signal: VolatilityMismatchSignal):
        """处理波动率信号"""
        try:
            # 检查信号冷却
            if self._is_signal_in_cooldown(signal.mismatch_type):
                return

            # 添加到信号历史
            self.signal_history.append(signal)
            self.last_signal_time[signal.mismatch_type] = signal.timestamp

            # 发布信号事件
            if self.event_bus:
                await self.event_bus.publish(
                    "volatility_mismatch_signal",
                    {
                        "signal_type": signal.mismatch_type.value,
                        "strength": signal.strength,
                        "confidence": signal.confidence,
                        "timestamp": signal.timestamp.isoformat(),
                        "expiry": signal.expiry.isoformat(),
                        "strike": signal.strike,
                        "implied_vol": signal.implied_vol,
                        "realized_vol": signal.realized_vol,
                        "vol_spread": signal.vol_spread,
                        "arbitrage_profit": signal.arbitrage_profit,
                        "metadata": signal.metadata,
                    },
                )

            # 缓存信号数据
            if self.cache_manager:
                cache_key = f"volatility_signal:{signal.mismatch_type.value}:{int(signal.timestamp.timestamp())}"
                await self.cache_manager.set(
                    cache_key,
                    {
                        "signal_type": signal.mismatch_type.value,
                        "strength": signal.strength,
                        "confidence": signal.confidence,
                        "metadata": signal.metadata,
                    },
                    ttl=3600,
                )  # 1小时TTL

            if self.logger:
                await self.logger.info(
                    f"Volatility mismatch signal generated: {signal.mismatch_type.value} "
                    f"(strength: {signal.strength:.3f}, confidence: {signal.confidence:.3f})"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error processing volatility signal: {e}")

    def _is_signal_in_cooldown(self, signal_type: VolatilityMismatchType) -> bool:
        """检查信号是否在冷却期"""
        if signal_type not in self.last_signal_time:
            return False

        time_since_last = (
            datetime.now(timezone.utc) - self.last_signal_time[signal_type]
        )
        return time_since_last < self.signal_cooldown

    def _update_metrics(self):
        """更新指标"""
        try:
            if not self.signal_history:
                return

            # 计算24小时内的信号
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
            recent_signals = [
                s for s in self.signal_history if s.timestamp >= cutoff_time
            ]

            if recent_signals:
                # 计算平均波动率差异
                vol_spreads = [
                    s.vol_spread for s in recent_signals if s.vol_spread is not None
                ]
                if vol_spreads:
                    self.volatility_metrics.avg_vol_spread = np.mean(vol_spreads)
                    self.volatility_metrics.max_vol_spread = max(vol_spreads)

                # 计算套利机会
                arbitrage_signals = [
                    s
                    for s in recent_signals
                    if s.mismatch_type == VolatilityMismatchType.ARBITRAGE_OPPORTUNITY
                ]
                self.volatility_metrics.arbitrage_opportunities = len(arbitrage_signals)

                arbitrage_profits = [
                    s.arbitrage_profit
                    for s in arbitrage_signals
                    if s.arbitrage_profit is not None
                ]
                if arbitrage_profits:
                    self.volatility_metrics.total_arbitrage_profit = sum(
                        arbitrage_profits
                    )

                # 信号数量
                self.volatility_metrics.signal_count_24h = len(recent_signals)

                # 简化的准确率计算（需要实际交易结果来验证）
                self.volatility_metrics.accuracy_rate = 0.75  # 占位符

        except Exception as e:
            if self.logger:
                asyncio.create_task(self.logger.error(f"Error updating metrics: {e}"))

    async def get_current_metrics(self) -> VolatilityMismatchMetrics:
        """获取当前指标"""
        return self.volatility_metrics

    async def get_signal_history(
        self, hours: int = 24
    ) -> List[VolatilityMismatchSignal]:
        """获取信号历史"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        return [s for s in self.signal_history if s.timestamp >= cutoff_time]

    async def analyze_mismatch(
        self, market_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        分析波动率错配信号（供CausalEngine调用的标准接口）

        Args:
            market_data: 市场数据，包含期权和永续合约数据

        Returns:
            分析结果字典，包含strength、confidence和metadata
        """
        try:
            # 从市场数据中提取期权波动率数据
            option_data = await self._extract_option_volatility_from_market_data(
                market_data
            )
            if not option_data:
                return None

            # 从市场数据中提取永续合约波动率数据
            perp_data = await self._extract_perp_volatility_from_market_data(
                market_data
            )
            if not perp_data:
                return None

            # 更新数据历史
            self._update_data_history(option_data, perp_data)

            # 执行核心分析：寻找最强的波动率错配信号
            best_signal = None
            max_strength = 0.0

            # 1. 分析Funding Rate与IV Skew背离
            funding_iv_signal = await self._analyze_funding_iv_divergence(
                option_data, perp_data
            )
            if funding_iv_signal and funding_iv_signal.strength > max_strength:
                best_signal = funding_iv_signal
                max_strength = funding_iv_signal.strength

            # 2. 分析期限结构异常
            term_structure_signal = await self._analyze_term_structure_anomaly(
                option_data
            )
            if term_structure_signal and term_structure_signal.strength > max_strength:
                best_signal = term_structure_signal
                max_strength = term_structure_signal.strength

            # 3. 分析隐含vs实现波动率背离
            iv_rv_signal = await self._analyze_iv_rv_divergence(option_data, perp_data)
            if iv_rv_signal and iv_rv_signal.strength > max_strength:
                best_signal = iv_rv_signal
                max_strength = iv_rv_signal.strength

            # 检查是否有足够强的信号
            if not best_signal or best_signal.strength < self.vol_threshold:
                return None

            # 返回标准化的分析结果
            result = {
                "strength": best_signal.strength,
                "confidence": best_signal.confidence,
                "metadata": {
                    "mismatch_type": best_signal.mismatch_type.value,
                    "vol_spread": best_signal.vol_spread,
                    "implied_vol": best_signal.implied_vol,
                    "realized_vol": best_signal.realized_vol,
                    "arbitrage_profit": best_signal.arbitrage_profit,
                    "analysis_type": "volatility_mismatch",
                    "expiry": best_signal.expiry.isoformat()
                    if best_signal.expiry
                    else None,
                    "strike": best_signal.strike,
                },
            }

            # 合并metadata
            if best_signal.metadata:
                result["metadata"].update(best_signal.metadata)

            return result

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error in analyze_mismatch: {e}")
            return None

    async def _extract_option_volatility_from_market_data(
        self, market_data: Dict[str, Any]
    ) -> Optional[List[VolatilityData]]:
        """从市场数据中提取期权波动率数据"""
        try:
            # 优先从市场数据获取
            option_chain = market_data.get("option_chain", {})
            greeks_data = market_data.get("greeks", {})

            if option_chain:
                vol_data = []
                current_time = datetime.now(timezone.utc)

                for option_id, option_info in option_chain.items():
                    if isinstance(option_info, dict):
                        # 尝试从Greeks数据获取隐含波动率
                        greeks = greeks_data.get(option_id, {}) if greeks_data else {}
                        implied_vol = greeks.get(
                            "implied_volatility"
                        ) or option_info.get("implied_volatility")

                        if (
                            implied_vol
                            and option_info.get("strike")
                            and option_info.get("expiry")
                        ):
                            try:
                                vol_data.append(
                                    VolatilityData(
                                        timestamp=current_time,
                                        strike=float(option_info["strike"]),
                                        expiry=datetime.fromisoformat(
                                            option_info["expiry"]
                                        ),
                                        implied_vol=float(implied_vol),
                                        mark_price=float(
                                            option_info.get("mark_price", 0)
                                        ),
                                        bid_vol=float(greeks.get("bid_iv", 0))
                                        if greeks.get("bid_iv")
                                        else None,
                                        ask_vol=float(greeks.get("ask_iv", 0))
                                        if greeks.get("ask_iv")
                                        else None,
                                        volume=float(option_info.get("volume", 0)),
                                        open_interest=float(
                                            option_info.get("open_interest", 0)
                                        ),
                                    )
                                )
                            except (ValueError, TypeError):
                                continue  # 跳过格式错误的数据

                if vol_data:
                    return vol_data

            # 回退到缓存数据
            return await self._get_option_volatility_data()

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error extracting option volatility from market data: {e}"
                )
            return None

    async def _extract_perp_volatility_from_market_data(
        self, market_data: Dict[str, Any]
    ) -> Optional[PerpetualVolatilityData]:
        """从市场数据中提取永续合约波动率数据"""
        try:
            # 优先从市场数据获取
            binance_data = market_data.get("binance", {})
            deribit_data = market_data.get("deribit", {})

            current_time = datetime.now(timezone.utc)

            # 尝试从各种数据源构建永续数据
            funding_rate = 0.0
            mark_price = 0.0
            volume_24h = 0.0
            realized_vol_24h = 0.0

            if binance_data:
                funding_rate = float(binance_data.get("funding_rate", 0))
                mark_price = float(binance_data.get("mark_price", 0))
                volume_24h = float(binance_data.get("volume_24h", 0))

                # 尝试计算实现波动率（简化）
                price_changes = binance_data.get("price_changes", [])
                if price_changes and len(price_changes) > 1:
                    returns = []
                    for i in range(1, len(price_changes)):
                        if price_changes[i - 1] > 0:
                            ret = (price_changes[i] / price_changes[i - 1]) - 1
                            returns.append(ret)

                    if returns:
                        import math

                        realized_vol_24h = (
                            np.std(returns) * math.sqrt(365)
                            if len(returns) > 1
                            else 0.0
                        )

            # 如果Binance数据不足，尝试Deribit
            if mark_price == 0 and deribit_data:
                mark_price = float(deribit_data.get("mark_price", 0))
                volume_24h = float(deribit_data.get("volume_24h", 0))

            if mark_price > 0:
                return PerpetualVolatilityData(
                    timestamp=current_time,
                    realized_vol_1h=realized_vol_24h / 24,  # 简化估算
                    realized_vol_4h=realized_vol_24h / 6,  # 简化估算
                    realized_vol_24h=realized_vol_24h,
                    funding_rate=funding_rate,
                    mark_price=mark_price,
                    volume_24h=volume_24h,
                )

            # 回退到缓存数据
            return await self._get_perpetual_volatility_data()

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error extracting perp volatility from market data: {e}"
                )
            return None

    async def _analyze_funding_iv_divergence(
        self, option_data: List[VolatilityData], perp_data: PerpetualVolatilityData
    ) -> Optional[VolatilityMismatchSignal]:
        """分析Funding Rate与IV Skew的背离"""
        try:
            if not option_data or not perp_data:
                return None

            current_time = datetime.now(timezone.utc)

            # 计算IV偏度（简化：ATM call vs put IV差异）
            spot_price = perp_data.mark_price
            atm_calls = [
                opt
                for opt in option_data
                if abs(opt.strike / spot_price - 1) < 0.05
                and "call" in str(opt.strike).lower()
            ]
            atm_puts = [
                opt
                for opt in option_data
                if abs(opt.strike / spot_price - 1) < 0.05
                and "put" in str(opt.strike).lower()
            ]

            if not atm_calls or not atm_puts:
                return None

            avg_call_iv = np.mean([opt.implied_vol for opt in atm_calls])
            avg_put_iv = np.mean([opt.implied_vol for opt in atm_puts])
            iv_skew = avg_call_iv - avg_put_iv

            # 分析Funding Rate方向与IV Skew的背离
            funding_rate = perp_data.funding_rate

            # 背离检测：Funding Rate为正（看涨情绪）但Put IV高于Call IV（看跌防御）
            divergence_detected = False
            divergence_strength = 0.0

            if funding_rate > 0.0001 and iv_skew < -0.02:  # 正资金费率但Put更贵
                divergence_strength = min(
                    1.0, abs(funding_rate) * 1000 + abs(iv_skew) * 10
                )
                divergence_detected = True
            elif funding_rate < -0.0001 and iv_skew > 0.02:  # 负资金费率但Call更贵
                divergence_strength = min(
                    1.0, abs(funding_rate) * 1000 + abs(iv_skew) * 10
                )
                divergence_detected = True

            if divergence_detected and divergence_strength > self.vol_threshold:
                confidence = (
                    0.8 if divergence_strength > 2 * self.vol_threshold else 0.6
                )

                return VolatilityMismatchSignal(
                    mismatch_type=VolatilityMismatchType.SKEW_ANOMALY,
                    strength=divergence_strength,
                    confidence=confidence,
                    timestamp=current_time,
                    expiry=min(opt.expiry for opt in option_data),
                    vol_spread=iv_skew,
                    metadata={
                        "funding_rate": funding_rate,
                        "iv_skew": iv_skew,
                        "avg_call_iv": avg_call_iv,
                        "avg_put_iv": avg_put_iv,
                        "divergence_type": "funding_iv_divergence",
                    },
                )

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error analyzing funding IV divergence: {e}")
            return None

    async def _analyze_term_structure_anomaly(
        self, option_data: List[VolatilityData]
    ) -> Optional[VolatilityMismatchSignal]:
        """分析期限结构异常"""
        try:
            if len(option_data) < 4:
                return None

            current_time = datetime.now(timezone.utc)

            # 按到期日分组并计算ATM IV
            expiry_ivs = {}
            spot_price = option_data[0].mark_price

            for opt in option_data:
                if abs(opt.strike / spot_price - 1) < 0.1:  # ATM附近
                    expiry_key = opt.expiry.date()
                    if expiry_key not in expiry_ivs:
                        expiry_ivs[expiry_key] = []
                    expiry_ivs[expiry_key].append(opt.implied_vol)

            # 计算各期限的平均IV
            term_structure = []
            for expiry_date, ivs in expiry_ivs.items():
                if len(ivs) >= 2:
                    days_to_expiry = (
                        datetime.combine(expiry_date, datetime.min.time()).replace(
                            tzinfo=timezone.utc
                        )
                        - current_time
                    ).days
                    if days_to_expiry > 0:
                        avg_iv = np.mean(ivs)
                        term_structure.append((days_to_expiry, avg_iv))

            if len(term_structure) < 2:
                return None

            # 检查期限结构倒挂
            term_structure.sort(key=lambda x: x[0])
            max_inversion = 0.0
            inversion_detected = False

            for i in range(len(term_structure) - 1):
                short_term = term_structure[i]
                long_term = term_structure[i + 1]

                # 倒挂：短期IV > 长期IV
                inversion = short_term[1] - long_term[1]
                if inversion > max_inversion:
                    max_inversion = inversion

                if inversion > self.vol_threshold:
                    inversion_detected = True

            if inversion_detected:
                strength = min(1.0, max_inversion / (2 * self.vol_threshold))
                confidence = 0.8 if max_inversion > 2 * self.vol_threshold else 0.6

                return VolatilityMismatchSignal(
                    mismatch_type=VolatilityMismatchType.TERM_STRUCTURE_INVERSION,
                    strength=strength,
                    confidence=confidence,
                    timestamp=current_time,
                    expiry=min(opt.expiry for opt in option_data),
                    vol_spread=max_inversion,
                    metadata={
                        "max_inversion": max_inversion,
                        "term_structure": term_structure,
                        "inversion_type": "term_structure_anomaly",
                    },
                )

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error analyzing term structure anomaly: {e}")
            return None

    async def _analyze_iv_rv_divergence(
        self, option_data: List[VolatilityData], perp_data: PerpetualVolatilityData
    ) -> Optional[VolatilityMismatchSignal]:
        """分析隐含vs实现波动率背离"""
        try:
            if not option_data or not perp_data:
                return None

            current_time = datetime.now(timezone.utc)

            # 计算ATM隐含波动率
            spot_price = perp_data.mark_price
            atm_options = [
                opt for opt in option_data if abs(opt.strike / spot_price - 1) < 0.05
            ]

            if not atm_options:
                return None

            avg_iv = np.mean([opt.implied_vol for opt in atm_options])
            realized_vol = perp_data.realized_vol_24h

            # 计算IV-RV差异
            vol_spread = avg_iv - realized_vol

            if abs(vol_spread) > self.vol_threshold:
                strength = min(1.0, abs(vol_spread) / (2 * self.vol_threshold))
                confidence = 0.8 if abs(vol_spread) > 2 * self.vol_threshold else 0.6

                return VolatilityMismatchSignal(
                    mismatch_type=VolatilityMismatchType.ARBITRAGE_OPPORTUNITY,
                    strength=strength,
                    confidence=confidence,
                    timestamp=current_time,
                    expiry=min(opt.expiry for opt in atm_options),
                    implied_vol=avg_iv,
                    realized_vol=realized_vol,
                    vol_spread=vol_spread,
                    arbitrage_profit=abs(vol_spread) * spot_price * 0.1,
                    metadata={
                        "avg_implied_vol": avg_iv,
                        "realized_vol_24h": realized_vol,
                        "vol_difference": vol_spread,
                        "arbitrage_direction": "buy_vol"
                        if vol_spread < 0
                        else "sell_vol",
                        "divergence_type": "iv_rv_divergence",
                    },
                )

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error analyzing IV-RV divergence: {e}")
            return None
