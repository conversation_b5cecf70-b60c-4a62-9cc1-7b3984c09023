"""
Gamma清算分析器

实现大额期权到期的Gamma暴露计算和强制平仓影响分析：
1. 大额期权到期的Gamma暴露计算
2. 强制平仓区域识别和影响评估
3. Gamma挤压效应预测模型
4. 期权到期对现货价格影响预测

Author: BTC Options Grid Trading System
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Any, Dict, List, Optional

import numpy as np

from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from src.core.config_manager import ConfigManager


class GammaExposureType(Enum):
    """Gamma暴露类型"""

    POSITIVE_GAMMA = "positive_gamma"  # 正Gamma暴露
    NEGATIVE_GAMMA = "negative_gamma"  # 负Gamma暴露
    GAMMA_SQUEEZE = "gamma_squeeze"  # Gamma挤压
    LIQUIDATION_RISK = "liquidation_risk"  # 清算风险


@dataclass
class OptionPosition:
    """期权持仓数据"""

    strike: float
    expiry: datetime
    option_type: str  # 'call' or 'put'
    open_interest: float
    volume: float
    delta: float
    gamma: float
    theta: float
    vega: float
    mark_price: float
    implied_vol: float


@dataclass
class GammaExposureData:
    """Gamma暴露数据"""

    price_level: float
    total_gamma: float
    call_gamma: float
    put_gamma: float
    net_gamma: float
    open_interest: float
    notional_value: float
    timestamp: datetime


@dataclass
class GammaLiquidationSignal:
    """Gamma清算信号"""

    exposure_type: GammaExposureType
    strength: float  # 0-1
    confidence: float  # 0-1
    timestamp: datetime
    expiry: datetime
    critical_price: float
    gamma_exposure: float
    liquidation_pressure: float
    price_impact: float
    affected_notional: float
    metadata: Dict[str, Any] = None


@dataclass
class GammaLiquidationMetrics:
    """Gamma清算指标"""

    total_gamma_exposure: float
    max_gamma_level: float
    critical_price_levels: List[float]
    liquidation_risk_score: float
    gamma_squeeze_probability: float
    expected_price_impact: float
    signal_count_24h: int
    accuracy_rate: float
    error_count: int = 0


class GammaLiquidationAnalyzer(BaseComponent):
    """Gamma清算分析器"""

    def __init__(self, config_manager: ConfigManager):
        super().__init__("gamma_analyzer")
        self.config_manager = config_manager
        self.config = {}

        # 分析参数
        self.analysis_interval = 120  # 2分钟分析间隔
        self.gamma_threshold = 1000000  # 100万美元Gamma阈值
        self.liquidation_threshold = 0.05  # 5%清算阈值
        self.price_impact_threshold = 0.02  # 2%价格影响阈值
        self.signal_cooldown = timedelta(minutes=30)  # 30分钟信号冷却

        # 数据存储
        self.option_positions: List[OptionPosition] = []
        self.gamma_exposure_history: List[GammaExposureData] = []
        self.signal_history: List[GammaLiquidationSignal] = []
        self.last_signal_time: Dict[GammaExposureType, datetime] = {}

        # 运行状态
        self._analysis_task: Optional[asyncio.Task] = None

        # 统计指标 - 存储在custom_metrics中
        self.gamma_metrics = GammaLiquidationMetrics(
            total_gamma_exposure=0.0,
            max_gamma_level=0.0,
            critical_price_levels=[],
            liquidation_risk_score=0.0,
            gamma_squeeze_probability=0.0,
            expected_price_impact=0.0,
            signal_count_24h=0,
            accuracy_rate=0.0,
        )

    async def _initialize_impl(self) -> bool:
        """具体初始化实现"""
        try:
            # 加载配置
            analyzer_config = self.config_manager.get_section("gamma_analyzer")

            # 更新配置参数
            self.analysis_interval = analyzer_config.get("analysis_interval", 120)
            self.gamma_threshold = analyzer_config.get("gamma_threshold", 1000000)
            self.liquidation_threshold = analyzer_config.get(
                "liquidation_threshold", 0.05
            )
            self.price_impact_threshold = analyzer_config.get(
                "price_impact_threshold", 0.02
            )

            # 信号冷却时间
            cooldown_minutes = analyzer_config.get("signal_cooldown_minutes", 30)
            self.signal_cooldown = timedelta(minutes=cooldown_minutes)

            # 存储配置
            self.config = analyzer_config

            if self.logger:
                await self.logger.info(
                    "GammaLiquidationAnalyzer initialized successfully"
                )

            return True

        except Exception as e:
            # 即使没有logger也要记录错误
            error_msg = f"Failed to initialize GammaLiquidationAnalyzer: {e}"
            if self.logger:
                await self.logger.error(error_msg)
            else:
                print(error_msg)  # 测试环境下的fallback
            return False

    async def _start_impl(self) -> bool:
        """具体启动实现"""
        try:
            # 启动分析任务
            self._analysis_task = asyncio.create_task(self._analysis_loop())

            # 等待一小段时间确保任务启动
            await asyncio.sleep(0.01)

            # 检查任务是否还在运行
            if self._analysis_task.done():
                # 如果任务已经完成，检查是否有异常
                try:
                    self._analysis_task.result()
                except Exception as e:
                    if self.logger:
                        await self.logger.error(
                            f"Analysis task failed immediately: {e}"
                        )
                    return False

            if self.logger:
                await self.logger.info("GammaLiquidationAnalyzer started successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Failed to start GammaLiquidationAnalyzer: {e}"
                )
            return False

    async def _stop_impl(self) -> bool:
        """具体停止实现"""
        try:
            # 停止分析任务
            if self._analysis_task and not self._analysis_task.done():
                self._analysis_task.cancel()
                try:
                    await self._analysis_task
                except asyncio.CancelledError:
                    pass

            if self.logger:
                await self.logger.info("GammaLiquidationAnalyzer stopped successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to stop GammaLiquidationAnalyzer: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """具体健康检查实现"""
        try:
            # 检查基本状态
            if not self.is_running:
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message="GammaLiquidationAnalyzer is not running",
                    details={},
                )

            # 检查分析任务状态
            if self._analysis_task and self._analysis_task.done():
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message="Analysis task has stopped unexpectedly",
                    details={"task_done": True},
                )

            # 检查数据新鲜度
            current_time = datetime.now(timezone.utc)
            data_age_minutes = 0

            if self.option_positions:
                # 检查最新持仓数据的时间戳（假设我们有时间戳字段）
                data_age_minutes = 5  # 简化处理，假设数据是5分钟前的

            if data_age_minutes > 15:  # 数据超过15分钟认为不健康
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"Position data is {data_age_minutes:.1f} minutes old",
                    details={"data_age_minutes": data_age_minutes},
                )

            # 检查Gamma暴露计算
            gamma_exposure_count = len(self.gamma_exposure_history)

            # 检查信号生成
            recent_signals = len(
                [
                    s
                    for s in self.signal_history
                    if (current_time - s.timestamp).total_seconds() < 3600
                ]
            )  # 1小时内

            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message="GammaLiquidationAnalyzer is healthy",
                details={
                    "data_age_minutes": data_age_minutes,
                    "recent_signals": recent_signals,
                    "total_signals": len(self.signal_history),
                    "option_positions": len(self.option_positions),
                    "gamma_exposure_points": gamma_exposure_count,
                    "total_gamma_exposure": self.gamma_metrics.total_gamma_exposure,
                    "liquidation_risk_score": self.gamma_metrics.liquidation_risk_score,
                },
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
                details={"error": str(e)},
            )

    async def _analysis_loop(self):
        """主分析循环"""
        while True:
            try:
                await self._run_gamma_analysis()
                await asyncio.sleep(self.analysis_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Error in gamma analysis loop: {e}")
                await asyncio.sleep(self.analysis_interval)

    async def _run_gamma_analysis(self):
        """运行Gamma分析"""
        try:
            # 获取最新期权数据
            option_data = await self._get_option_positions()
            if not option_data:
                return

            # 更新持仓数据
            self.option_positions = option_data

            # 计算Gamma暴露
            gamma_exposure = self._calculate_gamma_exposure(option_data)

            # 更新暴露历史
            self._update_gamma_history(gamma_exposure)

            # 执行各种分析
            signals = []

            # 1. 大额Gamma暴露分析
            gamma_signals = await self._analyze_gamma_exposure(gamma_exposure)
            signals.extend(gamma_signals)

            # 2. 清算风险分析
            liquidation_signals = await self._analyze_liquidation_risk(
                option_data, gamma_exposure
            )
            signals.extend(liquidation_signals)

            # 3. Gamma挤压分析
            squeeze_signals = await self._analyze_gamma_squeeze(
                option_data, gamma_exposure
            )
            signals.extend(squeeze_signals)

            # 4. 价格影响预测
            impact_signals = await self._predict_price_impact(
                option_data, gamma_exposure
            )
            signals.extend(impact_signals)

            # 处理信号
            for signal in signals:
                await self._process_gamma_signal(signal)

            # 更新指标
            self._update_metrics(gamma_exposure)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error in gamma analysis: {e}")

    async def _get_option_positions(self) -> Optional[List[OptionPosition]]:
        """获取期权持仓数据"""
        try:
            if not self.cache_manager:
                return None

            # 从缓存获取期权数据
            option_data = await self.cache_manager.get("deribit:option_chain")
            greeks_data = await self.cache_manager.get("deribit:greeks")

            if not option_data or not greeks_data:
                return None

            positions = []

            for option in option_data.get("options", []):
                option_id = f"{option['strike']}_{option['expiry']}_{option['type']}"
                greeks = greeks_data.get(option_id, {})

                if option.get("open_interest", 0) > 0:
                    positions.append(
                        OptionPosition(
                            strike=option["strike"],
                            expiry=datetime.fromisoformat(option["expiry"]),
                            option_type=option["type"],
                            open_interest=option["open_interest"],
                            volume=option.get("volume", 0),
                            delta=greeks.get("delta", 0),
                            gamma=greeks.get("gamma", 0),
                            theta=greeks.get("theta", 0),
                            vega=greeks.get("vega", 0),
                            mark_price=option["mark_price"],
                            implied_vol=option.get("implied_volatility", 0),
                        )
                    )

            return positions

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error getting option positions: {e}")
            return None

    def _calculate_gamma_exposure(
        self, positions: List[OptionPosition]
    ) -> Dict[float, GammaExposureData]:
        """计算Gamma暴露"""
        gamma_exposure = {}
        current_time = datetime.now(timezone.utc)

        try:
            # 按价格水平分组计算Gamma
            price_levels = set()
            for pos in positions:
                # 创建价格网格（每100美元一个级别）
                base_price = int(pos.strike / 100) * 100
                for i in range(-5, 6):  # 上下5个价格级别
                    price_levels.add(base_price + i * 100)

            for price_level in price_levels:
                total_gamma = 0
                call_gamma = 0
                put_gamma = 0
                total_oi = 0
                total_notional = 0

                for pos in positions:
                    # 计算在该价格水平的Gamma贡献
                    gamma_contribution = pos.gamma * pos.open_interest
                    notional_contribution = pos.open_interest * price_level

                    total_gamma += gamma_contribution
                    total_oi += pos.open_interest
                    total_notional += notional_contribution

                    if pos.option_type == "call":
                        call_gamma += gamma_contribution
                    else:
                        put_gamma += gamma_contribution

                if total_gamma != 0:
                    gamma_exposure[price_level] = GammaExposureData(
                        price_level=price_level,
                        total_gamma=total_gamma,
                        call_gamma=call_gamma,
                        put_gamma=put_gamma,
                        net_gamma=call_gamma - put_gamma,
                        open_interest=total_oi,
                        notional_value=total_notional,
                        timestamp=current_time,
                    )

        except Exception as e:
            if self.logger:
                asyncio.create_task(
                    self.logger.error(f"Error calculating gamma exposure: {e}")
                )

        return gamma_exposure

    def _update_gamma_history(self, gamma_exposure: Dict[float, GammaExposureData]):
        """更新Gamma暴露历史"""
        # 添加新数据
        self.gamma_exposure_history.extend(gamma_exposure.values())

        # 清理过期数据（保留24小时）
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
        self.gamma_exposure_history = [
            data
            for data in self.gamma_exposure_history
            if data.timestamp >= cutoff_time
        ]

    async def _analyze_gamma_exposure(
        self, gamma_exposure: Dict[float, GammaExposureData]
    ) -> List[GammaLiquidationSignal]:
        """分析Gamma暴露"""
        signals = []

        try:
            current_time = datetime.now(timezone.utc)

            for price_level, exposure in gamma_exposure.items():
                # 检查大额Gamma暴露
                if abs(exposure.total_gamma) > self.gamma_threshold:
                    exposure_type = (
                        GammaExposureType.POSITIVE_GAMMA
                        if exposure.total_gamma > 0
                        else GammaExposureType.NEGATIVE_GAMMA
                    )

                    strength = min(
                        1.0, abs(exposure.total_gamma) / (2 * self.gamma_threshold)
                    )
                    confidence = (
                        0.8
                        if abs(exposure.total_gamma) > 2 * self.gamma_threshold
                        else 0.6
                    )

                    signal = GammaLiquidationSignal(
                        exposure_type=exposure_type,
                        strength=strength,
                        confidence=confidence,
                        timestamp=current_time,
                        expiry=current_time + timedelta(days=30),  # 假设30天到期
                        critical_price=price_level,
                        gamma_exposure=exposure.total_gamma,
                        liquidation_pressure=abs(exposure.total_gamma)
                        / 1000000,  # 标准化
                        price_impact=self._estimate_price_impact(
                            exposure.total_gamma, exposure.notional_value
                        ),
                        affected_notional=exposure.notional_value,
                        metadata={
                            "call_gamma": exposure.call_gamma,
                            "put_gamma": exposure.put_gamma,
                            "net_gamma": exposure.net_gamma,
                            "open_interest": exposure.open_interest,
                        },
                    )
                    signals.append(signal)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error analyzing gamma exposure: {e}")

        return signals

    async def _analyze_liquidation_risk(
        self,
        positions: List[OptionPosition],
        gamma_exposure: Dict[float, GammaExposureData],
    ) -> List[GammaLiquidationSignal]:
        """分析清算风险"""
        signals = []

        try:
            current_time = datetime.now(timezone.utc)

            # 识别即将到期的大额持仓
            near_expiry_positions = [
                pos
                for pos in positions
                if (pos.expiry - current_time).days <= 7 and pos.open_interest > 1000
            ]

            for pos in near_expiry_positions:
                # 计算清算风险
                days_to_expiry = (pos.expiry - current_time).days
                liquidation_risk = self._calculate_liquidation_risk(pos, days_to_expiry)

                if liquidation_risk > self.liquidation_threshold:
                    strength = min(
                        1.0, liquidation_risk / (2 * self.liquidation_threshold)
                    )
                    confidence = 0.9 if days_to_expiry <= 3 else 0.7

                    signal = GammaLiquidationSignal(
                        exposure_type=GammaExposureType.LIQUIDATION_RISK,
                        strength=strength,
                        confidence=confidence,
                        timestamp=current_time,
                        expiry=pos.expiry,
                        critical_price=pos.strike,
                        gamma_exposure=pos.gamma * pos.open_interest,
                        liquidation_pressure=liquidation_risk,
                        price_impact=self._estimate_liquidation_impact(pos),
                        affected_notional=pos.open_interest * pos.strike,
                        metadata={
                            "option_type": pos.option_type,
                            "days_to_expiry": days_to_expiry,
                            "delta": pos.delta,
                            "theta": pos.theta,
                        },
                    )
                    signals.append(signal)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error analyzing liquidation risk: {e}")

        return signals

    async def _analyze_gamma_squeeze(
        self,
        positions: List[OptionPosition],
        gamma_exposure: Dict[float, GammaExposureData],
    ) -> List[GammaLiquidationSignal]:
        """分析Gamma挤压"""
        signals = []

        try:
            current_time = datetime.now(timezone.utc)

            # 寻找Gamma集中的价格区域
            sorted_exposures = sorted(
                gamma_exposure.items(),
                key=lambda x: abs(x[1].total_gamma),
                reverse=True,
            )

            if len(sorted_exposures) >= 3:
                top_exposures = sorted_exposures[:3]

                # 检查是否存在Gamma挤压条件
                total_gamma = sum(abs(exp[1].total_gamma) for exp in top_exposures)
                price_range = max(exp[0] for exp in top_exposures) - min(
                    exp[0] for exp in top_exposures
                )

                # Gamma挤压条件：大量Gamma集中在小价格范围内
                if (
                    total_gamma > 2 * self.gamma_threshold and price_range < 1000
                ):  # 1000美元范围内
                    squeeze_probability = min(
                        1.0, total_gamma / (5 * self.gamma_threshold)
                    )

                    avg_price = np.mean([exp[0] for exp in top_exposures])

                    signal = GammaLiquidationSignal(
                        exposure_type=GammaExposureType.GAMMA_SQUEEZE,
                        strength=squeeze_probability,
                        confidence=0.8,
                        timestamp=current_time,
                        expiry=current_time + timedelta(days=7),  # 预期7天内发生
                        critical_price=avg_price,
                        gamma_exposure=total_gamma,
                        liquidation_pressure=squeeze_probability,
                        price_impact=self._estimate_squeeze_impact(
                            total_gamma, price_range
                        ),
                        affected_notional=sum(
                            exp[1].notional_value for exp in top_exposures
                        ),
                        metadata={
                            "price_range": price_range,
                            "gamma_concentration": total_gamma,
                            "critical_levels": [exp[0] for exp in top_exposures],
                        },
                    )
                    signals.append(signal)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error analyzing gamma squeeze: {e}")

        return signals

    async def _predict_price_impact(
        self,
        positions: List[OptionPosition],
        gamma_exposure: Dict[float, GammaExposureData],
    ) -> List[GammaLiquidationSignal]:
        """预测价格影响"""
        signals = []

        try:
            current_time = datetime.now(timezone.utc)

            # 计算总体价格影响
            total_impact = 0
            for exposure in gamma_exposure.values():
                impact = self._estimate_price_impact(
                    exposure.total_gamma, exposure.notional_value
                )
                total_impact += abs(impact)

            if total_impact > self.price_impact_threshold:
                strength = min(1.0, total_impact / (2 * self.price_impact_threshold))
                confidence = 0.7

                # 找到最大影响的价格水平
                max_impact_price = max(
                    gamma_exposure.keys(),
                    key=lambda p: abs(
                        self._estimate_price_impact(
                            gamma_exposure[p].total_gamma,
                            gamma_exposure[p].notional_value,
                        )
                    ),
                )

                signal = GammaLiquidationSignal(
                    exposure_type=GammaExposureType.GAMMA_SQUEEZE,
                    strength=strength,
                    confidence=confidence,
                    timestamp=current_time,
                    expiry=current_time + timedelta(days=1),
                    critical_price=max_impact_price,
                    gamma_exposure=gamma_exposure[max_impact_price].total_gamma,
                    liquidation_pressure=total_impact,
                    price_impact=total_impact,
                    affected_notional=sum(
                        exp.notional_value for exp in gamma_exposure.values()
                    ),
                    metadata={
                        "total_price_impact": total_impact,
                        "max_impact_price": max_impact_price,
                        "impact_threshold": self.price_impact_threshold,
                    },
                )
                signals.append(signal)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error predicting price impact: {e}")

        return signals

    def _calculate_liquidation_risk(
        self, position: OptionPosition, days_to_expiry: int
    ) -> float:
        """计算清算风险"""
        # 简化的清算风险模型
        base_risk = 0.1  # 基础风险

        # 时间衰减风险
        time_risk = max(0, (7 - days_to_expiry) / 7) * 0.3

        # Delta风险
        delta_risk = abs(position.delta) * 0.2

        # 持仓规模风险
        size_risk = min(0.4, position.open_interest / 10000 * 0.1)

        return base_risk + time_risk + delta_risk + size_risk

    def _estimate_price_impact(self, gamma: float, notional: float) -> float:
        """估算价格影响"""
        # 简化的价格影响模型
        gamma_impact = abs(gamma) / 1000000 * 0.01  # Gamma影响
        notional_impact = notional / 100000000 * 0.005  # 名义价值影响

        return gamma_impact + notional_impact

    def _estimate_liquidation_impact(self, position: OptionPosition) -> float:
        """估算清算影响"""
        # 基于持仓规模和Delta的影响估算
        base_impact = position.open_interest / 10000 * 0.001
        delta_impact = abs(position.delta) * base_impact

        return base_impact + delta_impact

    def _estimate_squeeze_impact(self, total_gamma: float, price_range: float) -> float:
        """估算挤压影响"""
        # Gamma挤压的价格影响
        concentration_factor = total_gamma / max(price_range, 100)
        return min(0.1, concentration_factor / 10000)

    async def _process_gamma_signal(self, signal: GammaLiquidationSignal):
        """处理Gamma信号"""
        try:
            # 检查信号冷却
            if self._is_signal_in_cooldown(signal.exposure_type):
                return

            # 添加到信号历史
            self.signal_history.append(signal)
            self.last_signal_time[signal.exposure_type] = signal.timestamp

            # 发布信号事件
            if self.event_bus:
                await self.event_bus.publish(
                    "gamma_liquidation_signal",
                    {
                        "signal_type": signal.exposure_type.value,
                        "strength": signal.strength,
                        "confidence": signal.confidence,
                        "timestamp": signal.timestamp.isoformat(),
                        "expiry": signal.expiry.isoformat(),
                        "critical_price": signal.critical_price,
                        "gamma_exposure": signal.gamma_exposure,
                        "liquidation_pressure": signal.liquidation_pressure,
                        "price_impact": signal.price_impact,
                        "affected_notional": signal.affected_notional,
                        "metadata": signal.metadata,
                    },
                )

            # 缓存信号数据
            if self.cache_manager:
                cache_key = f"gamma_signal:{signal.exposure_type.value}:{int(signal.timestamp.timestamp())}"
                await self.cache_manager.set(
                    cache_key,
                    {
                        "signal_type": signal.exposure_type.value,
                        "strength": signal.strength,
                        "confidence": signal.confidence,
                        "critical_price": signal.critical_price,
                        "metadata": signal.metadata,
                    },
                    ttl=3600,
                )  # 1小时TTL

            if self.logger:
                await self.logger.info(
                    f"Gamma liquidation signal generated: {signal.exposure_type.value} "
                    f"(strength: {signal.strength:.3f}, confidence: {signal.confidence:.3f})"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error processing gamma signal: {e}")

    def _is_signal_in_cooldown(self, signal_type: GammaExposureType) -> bool:
        """检查信号是否在冷却期"""
        if signal_type not in self.last_signal_time:
            return False

        time_since_last = (
            datetime.now(timezone.utc) - self.last_signal_time[signal_type]
        )
        return time_since_last < self.signal_cooldown

    def _update_metrics(self, gamma_exposure: Dict[float, GammaExposureData]):
        """更新指标"""
        try:
            # 计算总Gamma暴露
            total_gamma = sum(abs(exp.total_gamma) for exp in gamma_exposure.values())
            self.gamma_metrics.total_gamma_exposure = total_gamma

            # 最大Gamma水平
            if gamma_exposure:
                self.gamma_metrics.max_gamma_level = max(
                    abs(exp.total_gamma) for exp in gamma_exposure.values()
                )

            # 关键价格水平
            critical_levels = [
                price
                for price, exp in gamma_exposure.items()
                if abs(exp.total_gamma) > self.gamma_threshold
            ]
            self.gamma_metrics.critical_price_levels = sorted(critical_levels)

            # 清算风险评分
            self.gamma_metrics.liquidation_risk_score = min(
                1.0, total_gamma / (10 * self.gamma_threshold)
            )

            # Gamma挤压概率
            if len(critical_levels) >= 2:
                price_range = max(critical_levels) - min(critical_levels)
                self.gamma_metrics.gamma_squeeze_probability = min(
                    1.0, total_gamma / max(price_range, 1000) / 1000
                )

            # 预期价格影响
            total_impact = sum(
                self._estimate_price_impact(exp.total_gamma, exp.notional_value)
                for exp in gamma_exposure.values()
            )
            self.gamma_metrics.expected_price_impact = total_impact

            # 24小时信号数量
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
            recent_signals = [
                s for s in self.signal_history if s.timestamp >= cutoff_time
            ]
            self.gamma_metrics.signal_count_24h = len(recent_signals)

            # 简化的准确率计算
            self.gamma_metrics.accuracy_rate = 0.8  # 占位符

        except Exception as e:
            if self.logger:
                asyncio.create_task(self.logger.error(f"Error updating metrics: {e}"))

    async def get_current_metrics(self) -> GammaLiquidationMetrics:
        """获取当前指标"""
        return self.gamma_metrics

    async def get_signal_history(self, hours: int = 24) -> List[GammaLiquidationSignal]:
        """获取信号历史"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        return [s for s in self.signal_history if s.timestamp >= cutoff_time]

    async def get_gamma_exposure(self) -> Dict[float, GammaExposureData]:
        """获取当前Gamma暴露"""
        if self.option_positions:
            return self._calculate_gamma_exposure(self.option_positions)
        return {}

    async def analyze_liquidation_overlap(
        self, market_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        分析Gamma清算重叠信号（供CausalEngine调用的标准接口）

        Args:
            market_data: 市场数据，包含期权和Greeks数据

        Returns:
            分析结果字典，包含strength、confidence和metadata
        """
        try:
            # 从市场数据中提取期权持仓数据
            option_positions = await self._extract_option_positions_from_market_data(
                market_data
            )
            if not option_positions:
                return None

            # 更新持仓数据
            self.option_positions = option_positions

            # 计算Gamma暴露分布
            gamma_exposure = self._calculate_gamma_exposure(option_positions)
            if not gamma_exposure:
                return None

            # 执行核心分析：寻找最强的Gamma清算重叠信号
            best_signal = None
            max_strength = 0.0

            # 1. 分析GEX集中区域与清算价格重叠
            gex_liquidation_signal = await self._analyze_gex_liquidation_overlap(
                gamma_exposure, market_data
            )
            if (
                gex_liquidation_signal
                and gex_liquidation_signal.strength > max_strength
            ):
                best_signal = gex_liquidation_signal
                max_strength = gex_liquidation_signal.strength

            # 2. 分析即将到期期权的Gamma风险
            expiry_gamma_signal = await self._analyze_expiry_gamma_risk(
                option_positions, gamma_exposure
            )
            if expiry_gamma_signal and expiry_gamma_signal.strength > max_strength:
                best_signal = expiry_gamma_signal
                max_strength = expiry_gamma_signal.strength

            # 3. 分析大额持仓清算压力
            liquidation_pressure_signal = (
                await self._analyze_massive_liquidation_pressure(
                    option_positions, gamma_exposure, market_data
                )
            )
            if (
                liquidation_pressure_signal
                and liquidation_pressure_signal.strength > max_strength
            ):
                best_signal = liquidation_pressure_signal
                max_strength = liquidation_pressure_signal.strength

            # 检查是否有足够强的信号
            if not best_signal or best_signal.strength < (
                self.liquidation_threshold * 2
            ):
                return None

            # 返回标准化的分析结果
            result = {
                "strength": best_signal.strength,
                "confidence": best_signal.confidence,
                "metadata": {
                    "exposure_type": best_signal.exposure_type.value,
                    "critical_price": best_signal.critical_price,
                    "gamma_exposure": best_signal.gamma_exposure,
                    "liquidation_pressure": best_signal.liquidation_pressure,
                    "price_impact": best_signal.price_impact,
                    "affected_notional": best_signal.affected_notional,
                    "analysis_type": "gamma_liquidation_overlap",
                    "expiry": best_signal.expiry.isoformat()
                    if best_signal.expiry
                    else None,
                },
            }

            # 合并metadata
            if best_signal.metadata:
                result["metadata"].update(best_signal.metadata)

            return result

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error in analyze_liquidation_overlap: {e}")
            return None

    async def _extract_option_positions_from_market_data(
        self, market_data: Dict[str, Any]
    ) -> Optional[List[OptionPosition]]:
        """从市场数据中提取期权持仓数据"""
        try:
            # 优先从市场数据获取
            option_chain = market_data.get("option_chain", {})
            greeks_data = market_data.get("greeks", {})

            if option_chain:
                positions = []

                for option_id, option_info in option_chain.items():
                    if (
                        isinstance(option_info, dict)
                        and option_info.get("open_interest", 0) > 0
                    ):
                        # 尝试从Greeks数据获取希腊字母
                        greeks = greeks_data.get(option_id, {}) if greeks_data else {}

                        try:
                            # 解析期权类型
                            option_type = (
                                "call"
                                if "C" in option_id or option_info.get("type") == "call"
                                else "put"
                            )

                            position = OptionPosition(
                                strike=float(option_info["strike"]),
                                expiry=datetime.fromisoformat(option_info["expiry"]),
                                option_type=option_type,
                                open_interest=float(option_info["open_interest"]),
                                volume=float(option_info.get("volume", 0)),
                                delta=float(greeks.get("delta", 0)),
                                gamma=float(greeks.get("gamma", 0)),
                                theta=float(greeks.get("theta", 0)),
                                vega=float(greeks.get("vega", 0)),
                                mark_price=float(option_info.get("mark_price", 0)),
                                implied_vol=float(
                                    option_info.get("implied_volatility", 0)
                                ),
                            )
                            positions.append(position)
                        except (ValueError, TypeError, KeyError):
                            continue  # 跳过格式错误的数据

                if positions:
                    return positions

            # 回退到缓存数据
            return await self._get_option_positions()

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error extracting option positions from market data: {e}"
                )
            return None

    async def _analyze_gex_liquidation_overlap(
        self,
        gamma_exposure: Dict[float, GammaExposureData],
        market_data: Dict[str, Any],
    ) -> Optional[GammaLiquidationSignal]:
        """分析GEX分布与清算区域重叠"""
        try:
            current_time = datetime.now(timezone.utc)
            current_price = 0.0

            # 获取当前价格
            binance_data = market_data.get("binance", {})
            deribit_data = market_data.get("deribit", {})

            if binance_data and binance_data.get("mark_price"):
                current_price = float(binance_data["mark_price"])
            elif deribit_data and deribit_data.get("mark_price"):
                current_price = float(deribit_data["mark_price"])
            elif gamma_exposure:
                # 使用Gamma暴露数据估算当前价格
                current_price = np.mean(list(gamma_exposure.keys()))

            if current_price <= 0:
                return None

            # 找到GEX集中的价格区域
            sorted_exposures = sorted(
                gamma_exposure.items(),
                key=lambda x: abs(x[1].total_gamma),
                reverse=True,
            )

            if not sorted_exposures:
                return None

            # 分析前3个最大Gamma暴露区域
            top_exposures = sorted_exposures[:3]
            total_concentrated_gamma = sum(
                abs(exp[1].total_gamma) for exp in top_exposures
            )

            # 计算价格距离最近的高Gamma区域
            nearest_gamma_price = min(
                top_exposures, key=lambda x: abs(x[0] - current_price)
            )[0]
            price_distance_ratio = (
                abs(nearest_gamma_price - current_price) / current_price
            )

            # 计算清算重叠风险
            overlap_risk = 0.0

            # 风险因子1: GEX集中度
            if total_concentrated_gamma > self.gamma_threshold:
                overlap_risk += min(
                    0.4, total_concentrated_gamma / (5 * self.gamma_threshold)
                )

            # 风险因子2: 价格接近度（价格越接近高Gamma区域，风险越高）
            if price_distance_ratio < 0.05:  # 5%价格范围内
                overlap_risk += 0.3 * (1 - price_distance_ratio / 0.05)

            # 风险因子3: Gamma不平衡（正负Gamma失衡）
            for price_level, exposure in top_exposures:
                gamma_imbalance = abs(exposure.call_gamma - exposure.put_gamma) / max(
                    abs(exposure.total_gamma), 1
                )
                if gamma_imbalance > 0.7:  # 70%以上不平衡
                    overlap_risk += 0.2
                    break

            # 风险因子4: 临近到期（如果有大量即将到期的期权）
            near_expiry_gamma = 0.0
            for pos in self.option_positions:
                days_to_expiry = (pos.expiry - current_time).days
                if days_to_expiry <= 7:  # 7天内到期
                    near_expiry_gamma += abs(pos.gamma * pos.open_interest)

            if near_expiry_gamma > self.gamma_threshold * 0.5:
                overlap_risk += 0.1

            # 生成信号
            if overlap_risk > self.liquidation_threshold:
                strength = min(1.0, overlap_risk)
                confidence = (
                    0.8 if overlap_risk > self.liquidation_threshold * 2 else 0.6
                )

                # 估算价格影响
                price_impact = self._estimate_gex_overlap_impact(
                    total_concentrated_gamma, price_distance_ratio
                )

                return GammaLiquidationSignal(
                    exposure_type=GammaExposureType.GAMMA_SQUEEZE,
                    strength=strength,
                    confidence=confidence,
                    timestamp=current_time,
                    expiry=current_time + timedelta(days=7),
                    critical_price=nearest_gamma_price,
                    gamma_exposure=total_concentrated_gamma,
                    liquidation_pressure=overlap_risk,
                    price_impact=price_impact,
                    affected_notional=sum(
                        exp[1].notional_value for exp in top_exposures
                    ),
                    metadata={
                        "current_price": current_price,
                        "price_distance_ratio": price_distance_ratio,
                        "concentrated_gamma": total_concentrated_gamma,
                        "near_expiry_gamma": near_expiry_gamma,
                        "top_gamma_levels": [exp[0] for exp in top_exposures],
                        "overlap_type": "gex_liquidation_overlap",
                    },
                )

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error analyzing GEX liquidation overlap: {e}")
            return None

    async def _analyze_expiry_gamma_risk(
        self,
        positions: List[OptionPosition],
        gamma_exposure: Dict[float, GammaExposureData],
    ) -> Optional[GammaLiquidationSignal]:
        """分析即将到期期权的Gamma风险"""
        try:
            current_time = datetime.now(timezone.utc)

            # 找到即将到期的大额持仓
            expiry_risks = {}

            for pos in positions:
                days_to_expiry = (pos.expiry - current_time).days
                if 0 < days_to_expiry <= 14:  # 14天内到期
                    expiry_key = pos.expiry.date()
                    if expiry_key not in expiry_risks:
                        expiry_risks[expiry_key] = {
                            "total_gamma": 0.0,
                            "total_notional": 0.0,
                            "positions": [],
                        }

                    gamma_contribution = abs(pos.gamma * pos.open_interest)
                    notional_contribution = pos.open_interest * pos.strike

                    expiry_risks[expiry_key]["total_gamma"] += gamma_contribution
                    expiry_risks[expiry_key]["total_notional"] += notional_contribution
                    expiry_risks[expiry_key]["positions"].append(pos)

            if not expiry_risks:
                return None

            # 找到风险最大的到期日
            max_risk_date = max(
                expiry_risks.keys(), key=lambda d: expiry_risks[d]["total_gamma"]
            )
            max_risk_info = expiry_risks[max_risk_date]

            if max_risk_info["total_gamma"] < self.gamma_threshold:
                return None

            # 计算到期风险评分
            days_to_max_expiry = (
                datetime.combine(max_risk_date, datetime.min.time()).replace(
                    tzinfo=timezone.utc
                )
                - current_time
            ).days
            time_decay_factor = max(
                0.1, (14 - days_to_max_expiry) / 14
            )  # 越接近到期，风险越高

            gamma_risk_factor = max_risk_info["total_gamma"] / (
                2 * self.gamma_threshold
            )
            expiry_risk_score = min(1.0, gamma_risk_factor * time_decay_factor)

            if expiry_risk_score > self.liquidation_threshold:
                strength = expiry_risk_score
                confidence = 0.9 if days_to_max_expiry <= 3 else 0.7

                # 计算关键价格（持仓量加权平均执行价）
                weighted_strike = sum(
                    pos.strike * pos.open_interest for pos in max_risk_info["positions"]
                ) / sum(pos.open_interest for pos in max_risk_info["positions"])

                return GammaLiquidationSignal(
                    exposure_type=GammaExposureType.LIQUIDATION_RISK,
                    strength=strength,
                    confidence=confidence,
                    timestamp=current_time,
                    expiry=datetime.combine(max_risk_date, datetime.min.time()).replace(
                        tzinfo=timezone.utc
                    ),
                    critical_price=weighted_strike,
                    gamma_exposure=max_risk_info["total_gamma"],
                    liquidation_pressure=expiry_risk_score,
                    price_impact=self._estimate_expiry_impact(
                        max_risk_info["total_gamma"], days_to_max_expiry
                    ),
                    affected_notional=max_risk_info["total_notional"],
                    metadata={
                        "days_to_expiry": days_to_max_expiry,
                        "time_decay_factor": time_decay_factor,
                        "position_count": len(max_risk_info["positions"]),
                        "weighted_strike": weighted_strike,
                        "expiry_date": max_risk_date.isoformat(),
                        "overlap_type": "expiry_gamma_risk",
                    },
                )

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error analyzing expiry gamma risk: {e}")
            return None

    async def _analyze_massive_liquidation_pressure(
        self,
        positions: List[OptionPosition],
        gamma_exposure: Dict[float, GammaExposureData],
        market_data: Dict[str, Any],
    ) -> Optional[GammaLiquidationSignal]:
        """分析大额持仓清算压力"""
        try:
            current_time = datetime.now(timezone.utc)

            # 识别大额持仓
            large_positions = [
                pos for pos in positions if pos.open_interest > 5000
            ]  # 5000张以上

            if not large_positions:
                return None

            # 计算总体清算压力
            total_liquidation_pressure = 0.0
            pressure_details = {}

            for pos in large_positions:
                # 计算单个持仓的清算风险
                days_to_expiry = (pos.expiry - current_time).days
                position_risk = self._calculate_advanced_liquidation_risk(
                    pos, days_to_expiry, market_data
                )

                if position_risk > 0.1:  # 10%以上风险才考虑
                    total_liquidation_pressure += position_risk * pos.open_interest

                    strike_key = f"{pos.strike}_{pos.option_type}"
                    pressure_details[strike_key] = {
                        "risk": position_risk,
                        "notional": pos.open_interest * pos.strike,
                        "gamma": pos.gamma * pos.open_interest,
                    }

            if not pressure_details:
                return None

            # 标准化清算压力评分
            max_possible_pressure = (
                sum(pos.open_interest for pos in large_positions) * 0.5
            )  # 假设最大50%清算风险
            normalized_pressure = min(
                1.0, total_liquidation_pressure / max_possible_pressure
            )

            if normalized_pressure > self.liquidation_threshold:
                strength = normalized_pressure
                confidence = (
                    0.8 if normalized_pressure > self.liquidation_threshold * 2 else 0.6
                )

                # 找到压力最大的价格水平
                max_pressure_strike = max(
                    pressure_details.keys(), key=lambda k: pressure_details[k]["risk"]
                )
                critical_price = float(max_pressure_strike.split("_")[0])

                total_affected_notional = sum(
                    detail["notional"] for detail in pressure_details.values()
                )
                total_gamma_at_risk = sum(
                    detail["gamma"] for detail in pressure_details.values()
                )

                return GammaLiquidationSignal(
                    exposure_type=GammaExposureType.LIQUIDATION_RISK,
                    strength=strength,
                    confidence=confidence,
                    timestamp=current_time,
                    expiry=current_time + timedelta(days=30),  # 预期30天内影响
                    critical_price=critical_price,
                    gamma_exposure=total_gamma_at_risk,
                    liquidation_pressure=normalized_pressure,
                    price_impact=self._estimate_massive_liquidation_impact(
                        total_gamma_at_risk, total_affected_notional
                    ),
                    affected_notional=total_affected_notional,
                    metadata={
                        "large_position_count": len(large_positions),
                        "pressure_details": pressure_details,
                        "max_pressure_strike": max_pressure_strike,
                        "total_liquidation_pressure": total_liquidation_pressure,
                        "overlap_type": "massive_liquidation_pressure",
                    },
                )

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error analyzing massive liquidation pressure: {e}"
                )
            return None

    def _calculate_advanced_liquidation_risk(
        self, position: OptionPosition, days_to_expiry: int, market_data: Dict[str, Any]
    ) -> float:
        """计算高级清算风险模型"""
        try:
            base_risk = 0.05  # 基础风险5%

            # 时间衰减风险（越接近到期风险越高）
            if days_to_expiry <= 0:
                time_risk = 0.8
            elif days_to_expiry <= 7:
                time_risk = 0.5 * (8 - days_to_expiry) / 7
            elif days_to_expiry <= 30:
                time_risk = 0.2 * (31 - days_to_expiry) / 23
            else:
                time_risk = 0.1

            # Delta风险（高Delta意味着对标的价格更敏感）
            delta_risk = abs(position.delta) * 0.3

            # 持仓规模风险
            size_risk = min(0.4, position.open_interest / 20000 * 0.2)  # 2万张为满风险

            # 波动率风险（高IV意味着更大的价格波动可能）
            vol_risk = (
                min(0.3, position.implied_vol * 0.5)
                if position.implied_vol > 0
                else 0.1
            )

            # 市场压力因子（从市场数据中提取）
            market_stress = 0.1  # 默认值

            binance_data = market_data.get("binance", {})
            if binance_data:
                # 基于资金费率判断市场压力
                funding_rate = abs(float(binance_data.get("funding_rate", 0)))
                if funding_rate > 0.001:  # 0.1%以上
                    market_stress += min(0.2, funding_rate * 100)

                # 基于成交量判断市场活跃度
                volume_24h = float(binance_data.get("volume_24h", 0))
                if volume_24h > 1000000000:  # 10亿以上成交量
                    market_stress += 0.1

            total_risk = (
                base_risk
                + time_risk
                + delta_risk
                + size_risk
                + vol_risk
                + market_stress
            )
            return min(1.0, total_risk)

        except Exception:
            return 0.1  # 默认风险

    def _estimate_gex_overlap_impact(
        self, concentrated_gamma: float, price_distance_ratio: float
    ) -> float:
        """估算GEX重叠的价格影响"""
        base_impact = min(0.05, concentrated_gamma / (10 * self.gamma_threshold) * 0.02)
        distance_factor = max(0.5, 1 - price_distance_ratio * 10)  # 距离越近影响越大
        return base_impact * distance_factor

    def _estimate_expiry_impact(
        self, gamma_exposure: float, days_to_expiry: int
    ) -> float:
        """估算到期影响"""
        base_impact = min(0.03, gamma_exposure / (5 * self.gamma_threshold) * 0.01)
        time_factor = max(1.0, (15 - days_to_expiry) / 15 * 2)  # 越接近到期影响越大
        return base_impact * time_factor

    def _estimate_massive_liquidation_impact(
        self, total_gamma: float, total_notional: float
    ) -> float:
        """估算大规模清算影响"""
        gamma_impact = min(0.04, total_gamma / (10 * self.gamma_threshold) * 0.02)
        notional_impact = min(0.02, total_notional / 1000000000 * 0.01)  # 10亿名义价值
        return gamma_impact + notional_impact
