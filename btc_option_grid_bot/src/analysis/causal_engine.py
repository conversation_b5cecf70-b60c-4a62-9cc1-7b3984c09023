"""
跨交易所因果分析引擎

实现三大核心信号分析：
1. 结构分歧信号 - 主流资金vs期权防御分析
2. 波动率错配信号 - 融资成本vs隐含波动率套利
3. Gamma清算重叠信号 - 清算压力vs Gamma集中区域重叠

提供实时信号计算、动态阈值管理和机器学习优化
"""

import asyncio
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Any, Callable, Dict, List, Optional

from ..core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from ..core.celery_manager import CeleryTaskManager
from ..core.config_manager import ConfigManager
from ..core.event_bus import EventBus, SignalEvent
from ..data.cache_manager import CacheManager
from .types import CausalSignal, SignalType


# 延迟导入避免循环依赖
@dataclass
class ThresholdConfig:
    """动态阈值配置"""

    signal_threshold: float = 0.65  # 信号触发阈值
    confidence_threshold: float = 0.7  # 置信度阈值
    lookback_period: int = 14  # 历史数据回看天数
    adaptation_rate: float = 0.1  # 阈值自适应速率
    min_threshold: float = 0.5  # 最小阈值
    max_threshold: float = 0.9  # 最大阈值


@dataclass
class SignalMetrics:
    """信号统计指标"""

    total_signals: int = 0
    successful_signals: int = 0
    false_positives: int = 0
    avg_strength: float = 0.0
    avg_confidence: float = 0.0
    last_signal_time: Optional[datetime] = None

    @property
    def success_rate(self) -> float:
        """成功率"""
        return (
            self.successful_signals / self.total_signals
            if self.total_signals > 0
            else 0.0
        )


class CausalEngine(BaseComponent):
    """
    跨交易所因果分析引擎

    核心功能：
    1. 三大信号分析器的框架和调度机制
    2. 信号强度计算算法（0-1标准化评分）
    3. 动态阈值管理（基于历史数据自适应调整）
    4. 历史数据回测和信号验证框架
    5. 机器学习模型优化信号识别
    6. 信号置信度计算和元数据记录
    """

    def __init__(self, config_manager: ConfigManager):
        super().__init__("CausalEngine")
        self.config_manager = config_manager

        # 核心组件依赖
        self.event_bus: Optional[EventBus] = None
        self.cache_manager: Optional[CacheManager] = None
        self.celery_manager: Optional[CeleryTaskManager] = None

        # 信号分析器实例 - 延迟初始化
        self.structure_analyzer = None
        self.volatility_analyzer = None
        self.gamma_analyzer = None
        self._config_manager = config_manager

        # 配置和阈值管理
        self.threshold_config = ThresholdConfig()
        self.signal_metrics: Dict[SignalType, SignalMetrics] = {
            signal_type: SignalMetrics() for signal_type in SignalType
        }

        # 信号历史和缓存
        self.signal_history: Dict[SignalType, List[CausalSignal]] = {
            signal_type: [] for signal_type in SignalType
        }
        self.max_history_size = 1000

        # 机器学习模型（占位符，后续实现）
        self.ml_model = None
        self.feature_extractors: List[Callable] = []

        # 运行状态
        self._analysis_task: Optional[asyncio.Task] = None
        self._threshold_update_task: Optional[asyncio.Task] = None
        self.analysis_interval = 10  # 10秒分析间隔

    def _create_analyzers(self):
        """延迟初始化分析器以避免循环导入"""
        if self.structure_analyzer is None:
            from .gamma_analyzer import GammaLiquidationAnalyzer
            from .structure_analyzer import StructureDivergenceAnalyzer
            from .volatility_analyzer import VolatilityMismatchAnalyzer

            self.structure_analyzer = StructureDivergenceAnalyzer(self._config_manager)
            self.volatility_analyzer = VolatilityMismatchAnalyzer(self._config_manager)
            self.gamma_analyzer = GammaLiquidationAnalyzer(self._config_manager)

    async def _initialize_analyzers_async(self):
        """异步初始化分析器"""
        if self.structure_analyzer:
            await self.structure_analyzer.initialize()
        if self.volatility_analyzer:
            await self.volatility_analyzer.initialize()
        if self.gamma_analyzer:
            await self.gamma_analyzer.initialize()

    async def _initialize_impl(self) -> bool:
        """具体初始化实现"""
        try:
            # 加载配置
            causal_config = self.config_manager.get_section("causal_engine")

            # 更新配置参数
            self.analysis_interval = causal_config.get("analysis_interval", 10)
            self.max_history_size = causal_config.get("max_history_size", 1000)

            # 更新阈值配置
            threshold_config = causal_config.get("thresholds", {})
            if threshold_config:
                self.threshold_config = ThresholdConfig(**threshold_config)

            # 初始化分析器实例
            self._create_analyzers()
            await self._initialize_analyzers_async()

            # 初始化特征提取器
            self._initialize_feature_extractors()

            if self.logger:
                await self.logger.info("CausalEngine initialized successfully")

            return True

        except Exception as e:
            # 即使没有logger也要记录错误
            error_msg = f"Failed to initialize CausalEngine: {e}"
            if self.logger:
                await self.logger.error(error_msg)
            else:
                print(error_msg)  # 测试环境下的fallback
            return False

    async def _initialize_analyzers(self) -> None:
        """初始化分析器实例"""
        try:
            # 为分析器注入依赖
            for analyzer in [
                self.structure_analyzer,
                self.volatility_analyzer,
                self.gamma_analyzer,
            ]:
                analyzer.cache_manager = self.cache_manager
                analyzer.logger = self.logger

                # 初始化分析器
                if not await analyzer.initialize():
                    raise RuntimeError(
                        f"Failed to initialize {analyzer.component_name}"
                    )

                # 启动分析器
                if not await analyzer.start():
                    raise RuntimeError(f"Failed to start {analyzer.component_name}")

            if self.logger:
                await self.logger.info("All signal analyzers initialized successfully")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to initialize analyzers: {e}")
            raise

    async def _start_impl(self) -> bool:
        """具体启动实现"""
        try:
            # 启动分析任务
            self._analysis_task = asyncio.create_task(self._analysis_loop())

            # 启动阈值更新任务
            self._threshold_update_task = asyncio.create_task(
                self._threshold_update_loop()
            )

            if self.logger:
                await self.logger.info("CausalEngine started successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to start CausalEngine: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """具体停止实现"""
        try:
            # 停止分析任务
            if self._analysis_task and not self._analysis_task.done():
                self._analysis_task.cancel()
                try:
                    await self._analysis_task
                except asyncio.CancelledError:
                    pass

            # 停止阈值更新任务
            if self._threshold_update_task and not self._threshold_update_task.done():
                self._threshold_update_task.cancel()
                try:
                    await self._threshold_update_task
                except asyncio.CancelledError:
                    pass

            if self.logger:
                await self.logger.info("CausalEngine stopped successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to stop CausalEngine: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """具体健康检查实现"""
        try:
            # 检查分析任务状态
            analysis_healthy = (
                self._analysis_task is not None and not self._analysis_task.done()
            )

            # 检查阈值更新任务状态
            threshold_healthy = (
                self._threshold_update_task is not None
                and not self._threshold_update_task.done()
            )

            # 检查信号生成情况
            recent_signals = any(
                metrics.last_signal_time
                and (
                    datetime.now(timezone.utc) - metrics.last_signal_time
                ).total_seconds()
                < 300
                for metrics in self.signal_metrics.values()
            )

            if analysis_healthy and threshold_healthy:
                status = HealthStatus.HEALTHY
                message = "CausalEngine is running normally"
            elif analysis_healthy or threshold_healthy:
                status = HealthStatus.DEGRADED
                message = "CausalEngine is partially functional"
            else:
                status = HealthStatus.UNHEALTHY
                message = "CausalEngine tasks are not running"

            details = {
                "analysis_task_running": analysis_healthy,
                "threshold_task_running": threshold_healthy,
                "recent_signals": recent_signals,
                "signal_metrics": {
                    signal_type.value: {
                        "total_signals": metrics.total_signals,
                        "success_rate": metrics.success_rate,
                        "avg_strength": metrics.avg_strength,
                    }
                    for signal_type, metrics in self.signal_metrics.items()
                },
            }

            return HealthCheckResult(status=status, message=message, details=details)

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {str(e)}"
            )

    def _initialize_feature_extractors(self):
        """初始化特征提取器"""
        # 基础特征提取器
        self.feature_extractors = [
            self._extract_price_features,
            self._extract_volume_features,
            self._extract_volatility_features,
            self._extract_sentiment_features,
        ]

    async def _analysis_loop(self):
        """主分析循环"""
        while True:
            try:
                await self._run_analysis_cycle()
                await asyncio.sleep(self.analysis_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Error in analysis loop: {e}")
                await asyncio.sleep(self.analysis_interval)

    async def _threshold_update_loop(self):
        """阈值更新循环"""
        while True:
            try:
                await self._update_dynamic_thresholds()
                await asyncio.sleep(3600)  # 每小时更新一次阈值

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Error in threshold update loop: {e}")
                await asyncio.sleep(3600)

    async def _run_analysis_cycle(self):
        """运行一次完整的分析周期"""
        try:
            # 获取市场数据
            market_data = await self._get_market_data()
            if not market_data:
                return

            # 并行运行三个信号分析器
            tasks = []

            # 结构分歧分析（任务16将实现）
            if self.structure_analyzer:
                tasks.append(self._analyze_structure_divergence(market_data))

            # 波动率错配分析（任务17将实现）
            if self.volatility_analyzer:
                tasks.append(self._analyze_volatility_mismatch(market_data))

            # Gamma清算重叠分析（任务18将实现）
            if self.gamma_analyzer:
                tasks.append(self._analyze_gamma_liquidation(market_data))

            # 等待所有分析完成
            if tasks:
                signals = await asyncio.gather(*tasks, return_exceptions=True)

                # 处理分析结果
                for signal in signals:
                    if isinstance(signal, CausalSignal):
                        await self._process_signal(signal)
                    elif isinstance(signal, Exception):
                        if self.logger:
                            await self.logger.error(f"Signal analysis error: {signal}")

            # 运行机器学习优化（如果模型已训练）
            if self.ml_model:
                await self._run_ml_optimization(market_data)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error in analysis cycle: {e}")

    async def _get_market_data(self) -> Optional[Dict[str, Any]]:
        """获取分析所需的市场数据"""
        try:
            if not self.cache_manager:
                return None

            # 从缓存获取各类数据
            market_data = {}

            # Binance数据
            binance_data = await self.cache_manager.get(
                "binance:market_data", "market_data"
            )
            if binance_data:
                market_data["binance"] = binance_data

            # Deribit数据
            deribit_data = await self.cache_manager.get(
                "deribit:market_data", "market_data"
            )
            if deribit_data:
                market_data["deribit"] = deribit_data

            # Greeks数据
            greeks_data = await self.cache_manager.get("deribit:greeks", "greeks")
            if greeks_data:
                market_data["greeks"] = greeks_data

            # 期权链数据
            option_chain = await self.cache_manager.get(
                "deribit:option_chain", "option_chain"
            )
            if option_chain:
                market_data["option_chain"] = option_chain

            return market_data if market_data else None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error getting market data: {e}")
            return None

    async def _analyze_structure_divergence(
        self, market_data: Dict[str, Any]
    ) -> Optional[CausalSignal]:
        """分析结构分歧信号（委托给专业分析器）"""
        try:
            if not self.structure_analyzer:
                return None

            # 委托给专业分析器进行分析
            analysis_result = await self.structure_analyzer.analyze_divergence(
                market_data
            )

            if analysis_result:
                # 将分析结果转换为CausalSignal
                signal = CausalSignal(
                    signal_type=SignalType.STRUCTURE_DIVERGENCE,
                    strength=analysis_result.get("strength", 0.0),
                    confidence=analysis_result.get("confidence", 0.0),
                    timestamp=datetime.now(timezone.utc),
                    metadata=analysis_result.get("metadata", {}),
                )
                return signal

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Structure divergence analysis failed: {e}")
            return None

    async def _analyze_volatility_mismatch(
        self, market_data: Dict[str, Any]
    ) -> Optional[CausalSignal]:
        """分析波动率错配信号（委托给专业分析器）"""
        try:
            if not self.volatility_analyzer:
                return None

            # 委托给专业分析器进行分析
            analysis_result = await self.volatility_analyzer.analyze_mismatch(
                market_data
            )

            if analysis_result:
                # 将分析结果转换为CausalSignal
                signal = CausalSignal(
                    signal_type=SignalType.VOLATILITY_MISMATCH,
                    strength=analysis_result.get("strength", 0.0),
                    confidence=analysis_result.get("confidence", 0.0),
                    timestamp=datetime.now(timezone.utc),
                    metadata=analysis_result.get("metadata", {}),
                )
                return signal

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Volatility mismatch analysis failed: {e}")
            return None

    async def _analyze_gamma_liquidation(
        self, market_data: Dict[str, Any]
    ) -> Optional[CausalSignal]:
        """分析Gamma清算重叠信号（委托给专业分析器）"""
        try:
            if not self.gamma_analyzer:
                return None

            # 委托给专业分析器进行分析
            analysis_result = await self.gamma_analyzer.analyze_liquidation_overlap(
                market_data
            )

            if analysis_result:
                # 将分析结果转换为CausalSignal
                signal = CausalSignal(
                    signal_type=SignalType.GAMMA_LIQUIDATION_OVERLAP,
                    strength=analysis_result.get("strength", 0.0),
                    confidence=analysis_result.get("confidence", 0.0),
                    timestamp=datetime.now(timezone.utc),
                    metadata=analysis_result.get("metadata", {}),
                )
                return signal

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Gamma liquidation analysis failed: {e}")
            return None

    async def _process_signal(self, signal: CausalSignal):
        """处理生成的信号"""
        try:
            # 更新信号历史
            self._update_signal_history(signal)

            # 更新统计指标
            self._update_signal_metrics(signal)

            # 检查是否达到触发阈值
            if signal.is_actionable:
                # 发布信号事件
                await self._publish_signal_event(signal)

                # 缓存信号
                await self._cache_signal(signal)

            # 记录信号
            if self.logger:
                await self.logger.info(
                    f"Signal processed: {signal.signal_type.value} "
                    f"strength={signal.strength:.3f} confidence={signal.confidence:.3f}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error processing signal: {e}")

    def _update_signal_history(self, signal: CausalSignal):
        """更新信号历史"""
        signal_type = signal.signal_type
        history = self.signal_history[signal_type]

        # 添加新信号
        history.append(signal)

        # 保持历史大小限制
        if len(history) > self.max_history_size:
            history.pop(0)

    def _update_signal_metrics(self, signal: CausalSignal):
        """更新信号统计指标"""
        metrics = self.signal_metrics[signal.signal_type]

        # 更新计数
        metrics.total_signals += 1
        metrics.last_signal_time = signal.timestamp

        # 更新平均值
        total = metrics.total_signals
        metrics.avg_strength = (
            metrics.avg_strength * (total - 1) + signal.strength
        ) / total
        metrics.avg_confidence = (
            metrics.avg_confidence * (total - 1) + signal.confidence
        ) / total

    async def _publish_signal_event(self, signal: CausalSignal):
        """发布信号事件"""
        if not self.event_bus:
            return

        event = SignalEvent(
            signal_data={
                "signal_type": signal.signal_type.value,
                "strength": signal.strength,
                "confidence": signal.confidence,
                "strength_level": signal.strength_level.value,
                "is_actionable": signal.is_actionable,
                "metadata": signal.metadata,
            },
            signal_type=signal.signal_type.value,
            confidence=signal.confidence,
            source=self.component_name,
            timestamp=signal.timestamp,
        )

        await self.event_bus.publish(event)

    async def _cache_signal(self, signal: CausalSignal):
        """缓存信号数据"""
        if not self.cache_manager:
            return

        cache_key = f"causal_signal:{signal.signal_type.value}:{int(signal.timestamp.timestamp())}"
        signal_data = {
            "signal_type": signal.signal_type.value,
            "strength": signal.strength,
            "confidence": signal.confidence,
            "timestamp": signal.timestamp.isoformat(),
            "metadata": signal.metadata,
        }

        await self.cache_manager.set(cache_key, signal_data, "causal_signals")

    async def _update_dynamic_thresholds(self):
        """更新动态阈值"""
        try:
            for signal_type in SignalType:
                await self._update_threshold_for_signal_type(signal_type)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error updating dynamic thresholds: {e}")

    async def _update_threshold_for_signal_type(self, signal_type: SignalType):
        """为特定信号类型更新阈值"""
        try:
            # 获取历史信号
            history = self.signal_history[signal_type]
            if len(history) < 10:  # 需要足够的历史数据
                return

            # 计算历史成功率
            recent_signals = history[-50:]  # 最近50个信号
            success_count = sum(
                1 for s in recent_signals if self._is_signal_successful(s)
            )
            success_rate = success_count / len(recent_signals)

            # 根据成功率调整阈值
            current_threshold = self.threshold_config.signal_threshold
            adaptation_rate = self.threshold_config.adaptation_rate

            if success_rate > 0.8:  # 成功率高，可以降低阈值
                new_threshold = current_threshold * (1 - adaptation_rate)
            elif success_rate < 0.6:  # 成功率低，提高阈值
                new_threshold = current_threshold * (1 + adaptation_rate)
            else:
                new_threshold = current_threshold

            # 应用阈值限制
            new_threshold = max(
                self.threshold_config.min_threshold,
                min(self.threshold_config.max_threshold, new_threshold),
            )

            self.threshold_config.signal_threshold = new_threshold

            if self.logger:
                await self.logger.debug(
                    f"Updated threshold for {signal_type.value}: "
                    f"{current_threshold:.3f} -> {new_threshold:.3f} "
                    f"(success_rate: {success_rate:.3f})"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error updating threshold for {signal_type.value}: {e}"
                )

    def _is_signal_successful(self, signal: CausalSignal) -> bool:
        """判断信号是否成功（简化实现，实际需要根据后续价格走势判断）"""
        # 这里是简化实现，实际应该根据信号发出后的市场表现来判断
        # 可以通过回测或实时跟踪来实现
        return signal.strength > 0.7 and signal.confidence > 0.8

    async def _run_ml_optimization(self, market_data: Dict[str, Any]):
        """运行机器学习优化（占位符）"""
        # 机器学习模型优化将在后续版本实现
        # 包括特征提取、模型训练、预测等
        pass

    def _extract_price_features(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """提取价格相关特征"""
        features = {}

        try:
            # Binance价格特征
            if "binance" in market_data:
                binance_data = market_data["binance"]
                features["btc_price"] = float(binance_data.get("price", 0))
                features["btc_volume"] = float(binance_data.get("volume", 0))

            # Deribit价格特征
            if "deribit" in market_data:
                deribit_data = market_data["deribit"]
                features["option_mark_price"] = float(deribit_data.get("mark_price", 0))
                features["option_volume"] = float(deribit_data.get("volume", 0))

        except Exception as e:
            if self.logger:
                asyncio.create_task(
                    self.logger.error(f"Error extracting price features: {e}")
                )

        return features

    def _extract_volume_features(self, market_data: Dict[str, Any]) -> Dict[str, float]:
        """提取成交量相关特征"""
        features = {}

        try:
            # 成交量比率、流入流出等特征
            if "binance" in market_data and "deribit" in market_data:
                binance_vol = float(market_data["binance"].get("volume", 0))
                deribit_vol = float(market_data["deribit"].get("volume", 0))

                if binance_vol > 0 and deribit_vol > 0:
                    features["volume_ratio"] = deribit_vol / binance_vol
                    features["total_volume"] = binance_vol + deribit_vol

        except Exception as e:
            if self.logger:
                asyncio.create_task(
                    self.logger.error(f"Error extracting volume features: {e}")
                )

        return features

    def _extract_volatility_features(
        self, market_data: Dict[str, Any]
    ) -> Dict[str, float]:
        """提取波动率相关特征"""
        features = {}

        try:
            # 隐含波动率特征
            if "deribit" in market_data:
                deribit_data = market_data["deribit"]
                features["implied_volatility"] = float(deribit_data.get("mark_iv", 0))

            # Greeks相关特征
            if "greeks" in market_data:
                greeks = market_data["greeks"]
                features["gamma"] = float(greeks.get("gamma", 0))
                features["vega"] = float(greeks.get("vega", 0))
                features["theta"] = float(greeks.get("theta", 0))

        except Exception as e:
            if self.logger:
                asyncio.create_task(
                    self.logger.error(f"Error extracting volatility features: {e}")
                )

        return features

    def _extract_sentiment_features(
        self, market_data: Dict[str, Any]
    ) -> Dict[str, float]:
        """提取市场情绪相关特征"""
        features = {}

        try:
            # 持仓量变化、资金费率等情绪指标
            if "deribit" in market_data:
                deribit_data = market_data["deribit"]
                features["open_interest"] = float(deribit_data.get("open_interest", 0))

            # 可以添加更多情绪指标

        except Exception as e:
            if self.logger:
                asyncio.create_task(
                    self.logger.error(f"Error extracting sentiment features: {e}")
                )

        return features

    # 公共接口方法

    async def get_signal_history(
        self, signal_type: SignalType, limit: int = 100
    ) -> List[CausalSignal]:
        """获取信号历史"""
        history = self.signal_history.get(signal_type, [])
        return history[-limit:] if limit > 0 else history

    async def get_signal_metrics(
        self, signal_type: Optional[SignalType] = None
    ) -> Dict[str, Any]:
        """获取信号统计指标"""
        if signal_type:
            metrics = self.signal_metrics.get(signal_type)
            if metrics:
                return {
                    "total_signals": metrics.total_signals,
                    "successful_signals": metrics.successful_signals,
                    "success_rate": metrics.success_rate,
                    "avg_strength": metrics.avg_strength,
                    "avg_confidence": metrics.avg_confidence,
                    "last_signal_time": metrics.last_signal_time.isoformat()
                    if metrics.last_signal_time
                    else None,
                }
        else:
            return {
                signal_type.value: {
                    "total_signals": metrics.total_signals,
                    "successful_signals": metrics.successful_signals,
                    "success_rate": metrics.success_rate,
                    "avg_strength": metrics.avg_strength,
                    "avg_confidence": metrics.avg_confidence,
                    "last_signal_time": metrics.last_signal_time.isoformat()
                    if metrics.last_signal_time
                    else None,
                }
                for signal_type, metrics in self.signal_metrics.items()
            }

    async def get_current_thresholds(self) -> Dict[str, float]:
        """获取当前阈值配置"""
        return {
            "signal_threshold": self.threshold_config.signal_threshold,
            "confidence_threshold": self.threshold_config.confidence_threshold,
            "min_threshold": self.threshold_config.min_threshold,
            "max_threshold": self.threshold_config.max_threshold,
        }

    async def update_threshold_config(self, **kwargs):
        """更新阈值配置"""
        for key, value in kwargs.items():
            if hasattr(self.threshold_config, key):
                setattr(self.threshold_config, key, value)

    async def register_analyzer(self, analyzer_type: str, analyzer):
        """注册信号分析器"""
        if analyzer_type == "structure":
            self.structure_analyzer = analyzer
        elif analyzer_type == "volatility":
            self.volatility_analyzer = analyzer
        elif analyzer_type == "gamma":
            self.gamma_analyzer = analyzer
        else:
            raise ValueError(f"Unknown analyzer type: {analyzer_type}")

        if self.logger:
            await self.logger.info(f"Registered {analyzer_type} analyzer")

    async def force_analysis(self) -> Dict[str, Any]:
        """强制执行一次分析"""
        try:
            await self._run_analysis_cycle()
            return {
                "status": "success",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "message": "Analysis completed successfully",
            }
        except Exception as e:
            return {
                "status": "error",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "message": f"Analysis failed: {str(e)}",
            }
