#!/usr/bin/env python3
"""
BTC期权网格交易机器人 - 主程序入口

启动整个交易系统，包括所有核心组件的初始化和协调。
"""

import asyncio
import signal
import sys
from pathlib import Path
from typing import Any, Dict

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.analysis.causal_engine import CausalEngine
from src.analysis.structure_analyzer import StructureDivergenceAnalyzer
from src.core.base_component import (
    BaseComponent,
    ComponentState,
    HealthCheckResult,
    HealthStatus,
)
from src.core.celery_manager import CeleryTaskManager
from src.core.config_manager import ConfigManager
from src.core.event_bus import EventBus
from src.data.cache_manager import CacheManager
from src.data.data_engine import DataEngine
from src.gateways.binance_client import BinanceClient
from src.gateways.deribit_client import DeribitClient
from src.notifications.telegram_handler import TelegramBotHandler
from src.risk.risk_engine import RiskEngine
from src.strategy.strategy_coordinator import StrategyCoordinator
from src.utils.async_logger import AsyncLogger


class TradingSystemMain(BaseComponent):
    """交易系统主控制器"""

    def __init__(self):
        super().__init__(component_name="TradingSystemMain")
        self.config_manager: ConfigManager = None
        self.event_bus: EventBus = None
        self.components: Dict[str, Any] = {}

    async def initialize(self):
        """初始化系统组件"""
        try:
            # 1. 初始化配置管理器
            self.config_manager = ConfigManager("config/config.yaml")
            await self.config_manager.load_config()

            # 2. 初始化日志系统
            log_config = await self.config_manager.get_config("logging", {})
            self.logger = AsyncLogger(
                level=log_config.get("level", "INFO"),
                log_file=log_config.get("file", "logs/btc_option_grid_bot.log"),
            )
            await self.logger.start()
            await self.logger.info("🚀 BTC期权网格交易机器人启动中...")

            # 3. 初始化事件总线
            self.event_bus = EventBus()
            await self.event_bus.start()

            # 4. 初始化缓存管理器
            cache_manager = CacheManager(self.config_manager)
            await cache_manager.initialize()
            await cache_manager.start()
            self.components["cache_manager"] = cache_manager

            # 5. 初始化Celery任务管理器
            celery_config = await self.config_manager.get_config("celery", {})
            celery_manager = CeleryTaskManager()
            celery_manager.configure(celery_config)
            self.components["celery_manager"] = celery_manager

            # 6. 初始化交易所连接器
            binance_config = await self.config_manager.get_config(
                "exchanges.binance", {}
            )
            binance_client = BinanceClient(
                api_key=binance_config.get("api_key"),
                api_secret=binance_config.get("api_secret"),
                celery_manager=celery_manager,
                config_manager=self.config_manager,
                cache_manager=cache_manager,
            )
            self.components["binance_client"] = binance_client

            deribit_config = await self.config_manager.get_config(
                "exchanges.deribit", {}
            )
            deribit_client = DeribitClient(
                api_key=deribit_config.get("api_key"),
                api_secret=deribit_config.get("api_secret"),
                celery_manager=celery_manager,
                cache_manager=cache_manager,
            )
            self.components["deribit_client"] = deribit_client

            # 7. 初始化数据引擎
            data_engine = DataEngine(
                binance_client=binance_client,
                deribit_client=deribit_client,
                event_bus=self.event_bus,
                logger=self.logger,
            )
            self.components["data_engine"] = data_engine

            # 8. 初始化风险引擎
            risk_config = await self.config_manager.get_config("risk_limits", {})
            risk_engine = RiskEngine(risk_config)
            self.components["risk_engine"] = risk_engine

            # 8. 初始化策略协调器
            strategy_configs = await self.config_manager.get_config("strategies", {})
            strategy_coordinator = StrategyCoordinator(
                strategy_configs=strategy_configs,
                risk_engine=risk_engine,
                event_bus=self.event_bus,
            )
            self.components["strategy_coordinator"] = strategy_coordinator

            # 9. 初始化因果分析引擎
            causal_engine = CausalEngine(self.config_manager)
            causal_engine.event_bus = self.event_bus
            causal_engine.cache_manager = data_engine.cache_manager
            self.components["causal_engine"] = causal_engine

            # 10. 初始化结构分歧分析器
            structure_analyzer = StructureDivergenceAnalyzer(self.config_manager)
            structure_analyzer.event_bus = self.event_bus
            structure_analyzer.cache_manager = data_engine.cache_manager
            self.components["structure_analyzer"] = structure_analyzer

            # 11. 初始化波动率错配分析器
            from src.analysis.volatility_analyzer import VolatilityMismatchAnalyzer

            volatility_analyzer = VolatilityMismatchAnalyzer(self.config_manager)
            volatility_analyzer.event_bus = self.event_bus
            volatility_analyzer.cache_manager = data_engine.cache_manager
            self.components["volatility_analyzer"] = volatility_analyzer

            # 12. 初始化Gamma清算分析器
            from src.analysis.gamma_analyzer import GammaLiquidationAnalyzer

            gamma_analyzer = GammaLiquidationAnalyzer(self.config_manager)
            gamma_analyzer.event_bus = self.event_bus
            gamma_analyzer.cache_manager = data_engine.cache_manager
            self.components["gamma_analyzer"] = gamma_analyzer

            # 13. 注册分析器到因果引擎
            await causal_engine.register_analyzer("structure", structure_analyzer)
            await causal_engine.register_analyzer("volatility", volatility_analyzer)
            await causal_engine.register_analyzer("gamma", gamma_analyzer)

            # 12. 初始化Telegram Bot
            telegram_config = await self.config_manager.get_config("telegram", {})
            if telegram_config.get("bot_token"):
                telegram_handler = TelegramBotHandler(
                    bot_token=telegram_config["bot_token"],
                    authorized_users=telegram_config.get("authorized_users", []),
                )
                telegram_handler.inject_dependencies(self.components)
                self.components["telegram_handler"] = telegram_handler

            await self.logger.info("✅ 所有组件初始化完成")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"❌ 系统初始化失败: {e}")
            else:
                print(f"❌ 系统初始化失败: {e}")
            raise

    async def start(self):
        """启动系统"""
        try:
            await self.logger.info("🔄 启动系统组件...")

            # 启动所有组件
            startup_tasks = []

            # 启动数据引擎
            startup_tasks.append(self.components["data_engine"].start())

            # 启动策略协调器
            startup_tasks.append(self.components["strategy_coordinator"].start())

            # 启动风险引擎
            startup_tasks.append(self.components["risk_engine"].start())

            # 启动因果分析引擎
            startup_tasks.append(self.components["causal_engine"].start())

            # 启动结构分歧分析器
            startup_tasks.append(self.components["structure_analyzer"].start())

            # 启动波动率错配分析器
            startup_tasks.append(self.components["volatility_analyzer"].start())

            # 启动Gamma清算分析器
            startup_tasks.append(self.components["gamma_analyzer"].start())

            # 启动Telegram Bot
            if "telegram_handler" in self.components:
                startup_tasks.append(self.components["telegram_handler"].start_bot())

            # 并发启动所有组件
            await asyncio.gather(*startup_tasks)

            # 设置组件状态为运行中
            self._state = ComponentState.RUNNING
            await self.logger.info("🚀 BTC期权网格交易机器人启动成功！")
            await self.logger.info("📊 系统状态: 运行中")

            # 发送启动通知
            if "telegram_handler" in self.components:
                await self.components["telegram_handler"].send_startup_notification()

        except Exception as e:
            await self.logger.error(f"❌ 系统启动失败: {e}")
            raise

    async def stop(self):
        """停止系统"""
        try:
            await self.logger.info("🛑 正在停止系统...")
            # 设置组件状态为停止中
            self._state = ComponentState.STOPPED

            # 停止所有组件
            shutdown_tasks = []

            for component_name, component in self.components.items():
                if hasattr(component, "stop"):
                    shutdown_tasks.append(component.stop())

            # 并发停止所有组件
            if shutdown_tasks:
                await asyncio.gather(*shutdown_tasks, return_exceptions=True)

            # 停止基础组件
            if self.event_bus:
                await self.event_bus.stop()

            await self.logger.info("✅ 系统已安全停止")

            if self.logger:
                await self.logger.stop()

        except Exception as e:
            print(f"❌ 系统停止时发生错误: {e}")

    async def run(self):
        """运行主循环"""
        try:
            # 初始化系统
            await self.initialize()

            # 启动系统
            await self.start()

            # 主循环 - 保持系统运行
            while self.is_running:
                try:
                    # 系统健康检查
                    await self.health_check()

                    # 等待一段时间再进行下次检查
                    await asyncio.sleep(30)  # 30秒检查一次

                except KeyboardInterrupt:
                    await self.logger.info("📝 收到停止信号，正在安全关闭系统...")
                    break
                except Exception as e:
                    await self.logger.error(f"⚠️ 主循环异常: {e}")
                    await asyncio.sleep(5)  # 异常后等待5秒再继续

        except Exception as e:
            if self.logger:
                await self.logger.error(f"❌ 系统运行异常: {e}")
            else:
                print(f"❌ 系统运行异常: {e}")
        finally:
            await self.stop()

    async def health_check(self):
        """系统健康检查"""
        try:
            # 检查各组件状态
            unhealthy_components = []

            for component_name, component in self.components.items():
                if hasattr(component, "health_check"):
                    try:
                        health_status = await component.health_check()
                        if not health_status.get("healthy", False):
                            unhealthy_components.append(component_name)
                    except Exception as e:
                        await self.logger.warning(
                            f"⚠️ 组件 {component_name} 健康检查失败: {e}"
                        )
                        unhealthy_components.append(component_name)

            if unhealthy_components:
                await self.logger.warning(f"⚠️ 发现不健康组件: {unhealthy_components}")

        except Exception as e:
            await self.logger.error(f"❌ 健康检查异常: {e}")

    async def _initialize_impl(self) -> bool:
        """BaseComponent抽象方法实现 - 初始化实现"""
        try:
            await self.initialize()
            return True
        except Exception:
            return False

    async def _start_impl(self) -> bool:
        """BaseComponent抽象方法实现 - 启动实现"""
        try:
            await self.start()
            return True
        except Exception:
            return False

    async def _stop_impl(self) -> bool:
        """BaseComponent抽象方法实现 - 停止实现"""
        try:
            await self.stop()
            return True
        except Exception:
            return False

    async def _health_check_impl(self):
        """BaseComponent抽象方法实现 - 健康检查实现"""
        try:
            await self.health_check()
            return HealthCheckResult(
                status=HealthStatus.HEALTHY, message="Trading system is healthy"
            )
        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Trading system health check failed: {e}",
            )


def setup_signal_handlers(trading_system: TradingSystemMain):
    """设置信号处理器"""

    def signal_handler(signum, frame):
        print(f"\n📝 收到信号 {signum}，正在安全关闭系统...")
        asyncio.create_task(trading_system.stop())

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """主函数"""
    print("🚀 BTC期权网格交易机器人")
    print("=" * 50)

    # 创建交易系统实例
    trading_system = TradingSystemMain()

    # 设置信号处理器
    setup_signal_handlers(trading_system)

    try:
        # 运行系统
        await trading_system.run()
    except KeyboardInterrupt:
        print("\n📝 用户中断，系统正在安全关闭...")
    except Exception as e:
        print(f"❌ 系统异常: {e}")
        sys.exit(1)

    print("👋 系统已退出")


if __name__ == "__main__":
    # 运行主程序
    asyncio.run(main())
