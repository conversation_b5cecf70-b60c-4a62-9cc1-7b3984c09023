"""
行权处理器

负责自动行权决策、资金准备、仓位更新和手动干预处理
"""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

from ..core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from ..core.event_bus import BaseEvent, DataUpdateEvent, EventBus
from ..data.cache_manager import CacheManager


class ExerciseDecision(Enum):
    """行权决策"""

    EXERCISE = "exercise"
    ABANDON = "abandon"
    HOLD = "hold"
    MANUAL = "manual"


class ExerciseStatus(Enum):
    """行权状态"""

    PENDING = "pending"
    APPROVED = "approved"
    EXECUTED = "executed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ExerciseType(Enum):
    """行权类型"""

    AUTOMATIC = "automatic"
    MANUAL = "manual"
    FORCED = "forced"


@dataclass
class ExerciseRequest:
    """行权请求"""

    request_id: str
    symbol: str
    option_type: str  # 'call' or 'put'
    strike_price: Decimal
    expiry_date: datetime
    position_size: Decimal
    current_price: Decimal

    # 决策信息
    decision: ExerciseDecision
    exercise_type: ExerciseType
    intrinsic_value: Decimal
    time_value: Decimal

    # 状态信息
    status: ExerciseStatus = ExerciseStatus.PENDING
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    executed_at: Optional[datetime] = None

    # 资金信息
    required_funds: Decimal = Decimal("0")
    available_funds: Decimal = Decimal("0")

    # 关联信息
    account_id: str = "default"
    strategy_id: Optional[str] = None

    # 错误信息
    error_message: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3

    # 元数据
    tags: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ExerciseResult:
    """行权结果"""

    request_id: str
    success: bool
    executed_at: datetime

    # 执行详情
    exercised_quantity: Decimal = Decimal("0")
    settlement_amount: Decimal = Decimal("0")
    commission: Decimal = Decimal("0")

    # 仓位变化
    position_changes: Dict[str, Decimal] = field(default_factory=dict)

    # 错误信息
    error_message: Optional[str] = None


@dataclass
class ExerciseStats:
    """行权统计"""

    total_requests: int = 0
    auto_exercises: int = 0
    manual_exercises: int = 0
    abandoned_options: int = 0

    # 成功率
    success_rate: float = 0.0
    auto_success_rate: float = 0.0

    # 价值统计
    total_intrinsic_value: Decimal = Decimal("0")
    total_settlement: Decimal = Decimal("0")

    # 时间统计
    avg_processing_time: float = 0.0  # 秒


class ExerciseHandler(BaseComponent):
    """
    行权处理器

    功能特性：
    - 自动行权决策和执行
    - 行权资金准备和验证
    - 行权后仓位更新和调整
    - 手动干预和特殊情况处理
    - 行权历史记录和统计分析
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("ExerciseHandler", config)

        # 组件依赖
        self.event_bus: Optional[EventBus] = None
        self.cache_manager: Optional[CacheManager] = None

        # 行权配置
        self._load_exercise_config()

        # 行权存储
        self.exercise_requests: Dict[str, ExerciseRequest] = {}
        self.exercise_history: List[ExerciseResult] = []
        self.pending_exercises: List[str] = []  # request_ids

        # 监控任务
        self._monitor_task: Optional[asyncio.Task] = None
        self._execution_task: Optional[asyncio.Task] = None
        self._monitor_interval = 60  # 1分钟监控间隔

        # 决策参数
        self.decision_threshold = Decimal("0.01")  # 1美分阈值
        self.time_value_threshold = Decimal("0.05")  # 5美分时间价值阈值

        # 资金管理
        self.fund_buffer_ratio = Decimal("0.1")  # 10%资金缓冲

        # 统计信息
        self.stats = ExerciseStats()

        # 支持的期权类型
        self.supported_option_types = {"call", "put"}

    def _load_exercise_config(self):
        """加载行权配置"""
        exercise_config = self.config.get("exercise", {}) if self.config else {}

        # 基础配置
        self.exercise_settings = {
            "enable_auto_exercise": exercise_config.get("enable_auto_exercise", True),
            "exercise_threshold": Decimal(
                str(exercise_config.get("exercise_threshold", "0.01"))
            ),
            "time_value_threshold": Decimal(
                str(exercise_config.get("time_value_threshold", "0.05"))
            ),
            "fund_buffer_ratio": Decimal(
                str(exercise_config.get("fund_buffer_ratio", "0.1"))
            ),
            "max_processing_time": exercise_config.get(
                "max_processing_time", 300
            ),  # 5分钟
            "enable_manual_override": exercise_config.get(
                "enable_manual_override", True
            ),
            "auto_abandon_otm": exercise_config.get(
                "auto_abandon_otm", True
            ),  # 自动放弃虚值期权
        }

        # 更新配置
        self.decision_threshold = self.exercise_settings["exercise_threshold"]
        self.time_value_threshold = self.exercise_settings["time_value_threshold"]
        self.fund_buffer_ratio = self.exercise_settings["fund_buffer_ratio"]

    async def _initialize_impl(self) -> bool:
        """初始化行权处理器"""
        try:
            # 订阅事件
            if self.event_bus:
                await self.event_bus.subscribe(
                    "option_expiry_alert", self._handle_expiry_alert
                )
                await self.event_bus.subscribe(
                    "position_updated", self._handle_position_update
                )
                await self.event_bus.subscribe(
                    "market_data_update", self._handle_market_update
                )
                await self.event_bus.subscribe(
                    "manual_exercise_request", self._handle_manual_request
                )

            # 加载历史数据
            await self._load_exercise_history()

            if self.logger:
                await self.logger.info("ExerciseHandler initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"ExerciseHandler initialization failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动行权处理器"""
        try:
            # 启动监控任务
            self._monitor_task = asyncio.create_task(self._exercise_monitor_loop())
            self._execution_task = asyncio.create_task(self._exercise_execution_loop())

            if self.logger:
                await self.logger.info("ExerciseHandler started")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"ExerciseHandler start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止行权处理器"""
        try:
            # 停止监控任务
            if self._monitor_task and not self._monitor_task.done():
                self._monitor_task.cancel()
                try:
                    await self._monitor_task
                except asyncio.CancelledError:
                    pass

            if self._execution_task and not self._execution_task.done():
                self._execution_task.cancel()
                try:
                    await self._execution_task
                except asyncio.CancelledError:
                    pass

            # 保存数据
            await self._save_exercise_data()

            if self.logger:
                await self.logger.info("ExerciseHandler stopped")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"ExerciseHandler stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查"""
        try:
            # 检查监控任务状态
            if not self._monitor_task or self._monitor_task.done():
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message="Exercise monitor task not running",
                )

            # 检查待处理请求
            pending_count = len(self.pending_exercises)
            if pending_count > 50:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"High number of pending exercises: {pending_count}",
                )

            # 检查超时请求
            timeout_requests = 0
            current_time = datetime.now(timezone.utc)

            for request_id in self.pending_exercises:
                if request_id in self.exercise_requests:
                    request = self.exercise_requests[request_id]
                    age_seconds = (current_time - request.created_at).total_seconds()
                    if age_seconds > self.exercise_settings["max_processing_time"]:
                        timeout_requests += 1

            if timeout_requests > 0:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"Timeout exercise requests: {timeout_requests}",
                )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message=f"Exercise system operational, {pending_count} pending requests",
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    async def _load_exercise_history(self):
        """加载行权历史"""
        try:
            if self.cache_manager:
                cached_history = await self.cache_manager.get("exercise_history")
                if cached_history:
                    for result_data in cached_history:
                        result = self._dict_to_exercise_result(result_data)
                        self.exercise_history.append(result)

            # 重建统计
            await self._update_statistics()

            if self.logger:
                await self.logger.info(
                    f"Loaded {len(self.exercise_history)} exercise records"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Exercise history loading failed: {e}")

    async def _exercise_monitor_loop(self):
        """行权监控循环"""
        while True:
            try:
                await self._check_expiring_options()
                await self._monitor_pending_requests()
                await self._update_statistics()
                await asyncio.sleep(self._monitor_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Exercise monitor error: {e}")
                await asyncio.sleep(60)

    async def _exercise_execution_loop(self):
        """行权执行循环"""
        while True:
            try:
                if self.pending_exercises:
                    request_id = self.pending_exercises.pop(0)
                    if request_id in self.exercise_requests:
                        await self._process_exercise_request(request_id)
                else:
                    await asyncio.sleep(1)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Exercise execution error: {e}")
                await asyncio.sleep(5)

    async def _check_expiring_options(self):
        """检查即将到期的期权"""
        try:
            current_time = datetime.now(timezone.utc)

            # 通过事件总线请求期权仓位信息
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type="request_option_positions",
                        data={"requester": "exercise_handler"},
                    )
                )

            # 检查缓存中的期权信息
            if self.cache_manager:
                option_positions = await self.cache_manager.get("option_positions")
                if option_positions:
                    for symbol, position_data in option_positions.items():
                        await self._evaluate_option_exercise(
                            symbol, position_data, current_time
                        )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiring options check failed: {e}")

    async def _evaluate_option_exercise(
        self, symbol: str, position_data: Dict[str, Any], current_time: datetime
    ):
        """评估期权行权"""
        try:
            expiry_str = position_data.get("expiry_date")
            if not expiry_str:
                return

            expiry_date = datetime.fromisoformat(expiry_str)
            time_to_expiry = (expiry_date - current_time).total_seconds() / 3600  # 小时

            # 只处理24小时内到期的期权
            if time_to_expiry > 24:
                return

            # 获取市场价格
            current_price = await self._get_current_price(symbol)
            if not current_price:
                return

            # 创建行权请求
            strike_price = Decimal(str(position_data.get("strike_price", 0)))
            option_type = position_data.get("option_type", "call")
            position_size = Decimal(str(position_data.get("size", 0)))

            if position_size <= 0:
                return

            # 计算内在价值
            intrinsic_value = self._calculate_intrinsic_value(
                current_price, strike_price, option_type
            )

            # 获取时间价值（从市场数据）
            time_value = await self._get_time_value(symbol, intrinsic_value)

            # 做出行权决策
            decision = await self._make_exercise_decision(
                intrinsic_value, time_value, time_to_expiry, option_type
            )

            if decision != ExerciseDecision.HOLD:
                await self._create_exercise_request(
                    symbol=symbol,
                    option_type=option_type,
                    strike_price=strike_price,
                    expiry_date=expiry_date,
                    position_size=position_size,
                    current_price=current_price,
                    intrinsic_value=intrinsic_value,
                    time_value=time_value,
                    decision=decision,
                    account_id=position_data.get("account_id", "default"),
                    strategy_id=position_data.get("strategy_id"),
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Option exercise evaluation failed: {e}")

    def _calculate_intrinsic_value(
        self, current_price: Decimal, strike_price: Decimal, option_type: str
    ) -> Decimal:
        """计算内在价值"""
        try:
            if option_type == "call":
                return max(Decimal("0"), current_price - strike_price)
            elif option_type == "put":
                return max(Decimal("0"), strike_price - current_price)
            else:
                return Decimal("0")

        except Exception:
            return Decimal("0")

    async def _get_current_price(self, symbol: str) -> Optional[Decimal]:
        """获取当前价格"""
        try:
            if self.cache_manager:
                market_data = await self.cache_manager.get(f"market_data:{symbol}")
                if market_data:
                    return Decimal(str(market_data.get("last_price", 0)))
            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get current price failed for {symbol}: {e}")
            return None

    async def _get_time_value(self, symbol: str, intrinsic_value: Decimal) -> Decimal:
        """获取时间价值"""
        try:
            if self.cache_manager:
                option_data = await self.cache_manager.get(f"option_data:{symbol}")
                if option_data:
                    market_price = Decimal(str(option_data.get("mark_price", 0)))
                    return max(Decimal("0"), market_price - intrinsic_value)
            return Decimal("0")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get time value failed for {symbol}: {e}")
            return Decimal("0")

    async def _make_exercise_decision(
        self,
        intrinsic_value: Decimal,
        time_value: Decimal,
        time_to_expiry: float,
        option_type: str,
    ) -> ExerciseDecision:
        """做出行权决策"""
        try:
            # 如果没有内在价值，放弃行权
            if intrinsic_value <= self.decision_threshold:
                if self.exercise_settings["auto_abandon_otm"]:
                    return ExerciseDecision.ABANDON
                else:
                    return ExerciseDecision.MANUAL

            # 如果时间价值很小且接近到期，行权
            if (
                time_value <= self.time_value_threshold and time_to_expiry <= 1
            ):  # 1小时内
                return ExerciseDecision.EXERCISE

            # 如果时间价值较大，需要手动决策
            if time_value > self.time_value_threshold:
                return ExerciseDecision.MANUAL

            # 默认持有
            return ExerciseDecision.HOLD

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Exercise decision failed: {e}")
            return ExerciseDecision.MANUAL

    async def _create_exercise_request(self, **kwargs) -> str:
        """创建行权请求"""
        try:
            import uuid

            request_id = str(uuid.uuid4())

            # 计算所需资金
            required_funds = Decimal("0")
            if (
                kwargs["option_type"] == "call"
                and kwargs["decision"] == ExerciseDecision.EXERCISE
            ):
                required_funds = kwargs["strike_price"] * kwargs["position_size"]

            # 检查可用资金
            available_funds = await self._get_available_funds(
                kwargs.get("account_id", "default")
            )

            request = ExerciseRequest(
                request_id=request_id,
                symbol=kwargs["symbol"],
                option_type=kwargs["option_type"],
                strike_price=kwargs["strike_price"],
                expiry_date=kwargs["expiry_date"],
                position_size=kwargs["position_size"],
                current_price=kwargs["current_price"],
                decision=kwargs["decision"],
                exercise_type=ExerciseType.AUTOMATIC,
                intrinsic_value=kwargs["intrinsic_value"],
                time_value=kwargs["time_value"],
                required_funds=required_funds,
                available_funds=available_funds,
                account_id=kwargs.get("account_id", "default"),
                strategy_id=kwargs.get("strategy_id"),
            )

            self.exercise_requests[request_id] = request

            # 如果是自动行权决策，加入执行队列
            if request.decision in [
                ExerciseDecision.EXERCISE,
                ExerciseDecision.ABANDON,
            ]:
                self.pending_exercises.append(request_id)

            # 发布事件
            if self.event_bus:
                await self.event_bus.publish(
                    DataUpdateEvent(
                        event_type="exercise_request_created",
                        data={
                            "request_id": request_id,
                            "symbol": request.symbol,
                            "decision": request.decision.value,
                            "intrinsic_value": float(request.intrinsic_value),
                            "required_funds": float(request.required_funds),
                        },
                    )
                )

            if self.logger:
                await self.logger.info(
                    f"Exercise request created: {request_id} - {request.symbol}"
                )

            return request_id

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Exercise request creation failed: {e}")
            return ""

    async def _get_available_funds(self, account_id: str) -> Decimal:
        """获取可用资金"""
        try:
            if self.cache_manager:
                account_data = await self.cache_manager.get(f"account:{account_id}")
                if account_data:
                    return Decimal(str(account_data.get("available_balance", 0)))

            # 模拟返回
            return Decimal("100000")  # 10万美元

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get available funds failed: {e}")
            return Decimal("0")

    async def _monitor_pending_requests(self):
        """监控待处理请求"""
        try:
            current_time = datetime.now(timezone.utc)
            timeout_requests = []

            for request_id in self.pending_exercises:
                if request_id in self.exercise_requests:
                    request = self.exercise_requests[request_id]
                    age_seconds = (current_time - request.created_at).total_seconds()

                    # 检查超时
                    if age_seconds > self.exercise_settings["max_processing_time"]:
                        timeout_requests.append(request_id)

            # 处理超时请求
            for request_id in timeout_requests:
                await self._handle_timeout_request(request_id)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Pending requests monitoring failed: {e}")

    async def _handle_timeout_request(self, request_id: str):
        """处理超时请求"""
        try:
            if request_id in self.exercise_requests:
                request = self.exercise_requests[request_id]
                request.status = ExerciseStatus.FAILED
                request.error_message = "Request timeout"

                # 从待处理队列移除
                if request_id in self.pending_exercises:
                    self.pending_exercises.remove(request_id)

                # 记录失败结果
                result = ExerciseResult(
                    request_id=request_id,
                    success=False,
                    executed_at=datetime.now(timezone.utc),
                    error_message="Request timeout",
                )

                self.exercise_history.append(result)

                if self.logger:
                    await self.logger.warning(f"Exercise request timeout: {request_id}")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Timeout request handling failed: {e}")

    async def _process_exercise_request(self, request_id: str):
        """处理行权请求"""
        try:
            if request_id not in self.exercise_requests:
                return

            request = self.exercise_requests[request_id]

            # 更新状态
            request.status = ExerciseStatus.APPROVED

            # 执行行权
            if request.decision == ExerciseDecision.EXERCISE:
                result = await self._execute_exercise(request)
            elif request.decision == ExerciseDecision.ABANDON:
                result = await self._abandon_option(request)
            else:
                result = ExerciseResult(
                    request_id=request_id,
                    success=False,
                    executed_at=datetime.now(timezone.utc),
                    error_message="Invalid decision",
                )

            # 记录结果
            self.exercise_history.append(result)

            # 更新请求状态
            if result.success:
                request.status = ExerciseStatus.EXECUTED
                request.executed_at = result.executed_at
            else:
                request.status = ExerciseStatus.FAILED
                request.error_message = result.error_message

            # 发布结果事件
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type="exercise_completed",
                        data={
                            "request_id": request_id,
                            "success": result.success,
                            "decision": request.decision.value,
                            "settlement_amount": float(result.settlement_amount),
                        },
                    )
                )

            if self.logger:
                status = "succeeded" if result.success else "failed"
                await self.logger.info(f"Exercise request {status}: {request_id}")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Exercise request processing failed: {e}")

    async def _execute_exercise(self, request: ExerciseRequest) -> ExerciseResult:
        """执行行权"""
        try:
            # 检查资金充足性
            if request.required_funds > request.available_funds:
                return ExerciseResult(
                    request_id=request.request_id,
                    success=False,
                    executed_at=datetime.now(timezone.utc),
                    error_message="Insufficient funds",
                )

            # 模拟行权执行
            # 实际实现中需要调用交易所API

            settlement_amount = request.intrinsic_value * request.position_size
            commission = settlement_amount * Decimal("0.001")  # 0.1%手续费

            # 计算仓位变化
            position_changes = {}

            if request.option_type == "call":
                # 看涨期权行权：获得标的资产，支付行权价
                underlying_symbol = request.symbol.split("-")[0]  # 提取BTC
                position_changes[underlying_symbol] = request.position_size
                position_changes["USD"] = -(
                    request.strike_price * request.position_size
                )
            elif request.option_type == "put":
                # 看跌期权行权：卖出标的资产，获得行权价
                underlying_symbol = request.symbol.split("-")[0]
                position_changes[underlying_symbol] = -request.position_size
                position_changes["USD"] = request.strike_price * request.position_size

            # 移除期权仓位
            position_changes[request.symbol] = -request.position_size

            return ExerciseResult(
                request_id=request.request_id,
                success=True,
                executed_at=datetime.now(timezone.utc),
                exercised_quantity=request.position_size,
                settlement_amount=settlement_amount,
                commission=commission,
                position_changes=position_changes,
            )

        except Exception as e:
            return ExerciseResult(
                request_id=request.request_id,
                success=False,
                executed_at=datetime.now(timezone.utc),
                error_message=str(e),
            )

    async def _abandon_option(self, request: ExerciseRequest) -> ExerciseResult:
        """放弃期权"""
        try:
            # 期权到期作废，仓位清零
            position_changes = {request.symbol: -request.position_size}

            return ExerciseResult(
                request_id=request.request_id,
                success=True,
                executed_at=datetime.now(timezone.utc),
                exercised_quantity=Decimal("0"),
                settlement_amount=Decimal("0"),
                commission=Decimal("0"),
                position_changes=position_changes,
            )

        except Exception as e:
            return ExerciseResult(
                request_id=request.request_id,
                success=False,
                executed_at=datetime.now(timezone.utc),
                error_message=str(e),
            )

    # 事件处理方法

    async def _handle_expiry_alert(self, event: BaseEvent):
        """处理到期提醒事件"""
        try:
            data = event.data
            symbol = data.get("symbol")
            days_to_expiry = data.get("days_to_expiry", 0)

            # 只处理即将到期的期权（1天内）
            if days_to_expiry <= 1:
                await self._evaluate_option_exercise(
                    symbol, data, datetime.now(timezone.utc)
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiry alert handling failed: {e}")

    async def _handle_position_update(self, event: BaseEvent):
        """处理仓位更新事件"""
        try:
            data = event.data
            symbol = data.get("symbol")

            # 检查是否是期权仓位
            if "BTC-" in symbol and ("-C" in symbol or "-P" in symbol):
                # 更新缓存中的期权仓位信息
                if self.cache_manager:
                    await self.cache_manager.set(
                        f"option_position:{symbol}", data, ttl=300
                    )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Position update handling failed: {e}")

    async def _handle_market_update(self, event: BaseEvent):
        """处理市场数据更新"""
        try:
            data = event.data
            symbol = data.get("symbol")

            # 更新期权市场数据
            if "BTC-" in symbol and ("-C" in symbol or "-P" in symbol):
                if self.cache_manager:
                    await self.cache_manager.set(f"option_data:{symbol}", data, ttl=60)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Market update handling failed: {e}")

    async def _handle_manual_request(self, event: BaseEvent):
        """处理手动行权请求"""
        try:
            data = event.data
            symbol = data.get("symbol")
            decision = data.get("decision", "exercise")

            # 获取期权信息
            if self.cache_manager:
                position_data = await self.cache_manager.get(
                    f"option_position:{symbol}"
                )
                if position_data:
                    current_price = await self._get_current_price(symbol)
                    if current_price:
                        strike_price = Decimal(
                            str(position_data.get("strike_price", 0))
                        )
                        option_type = position_data.get("option_type", "call")
                        position_size = Decimal(str(position_data.get("size", 0)))
                        expiry_date = datetime.fromisoformat(
                            position_data.get("expiry_date")
                        )

                        intrinsic_value = self._calculate_intrinsic_value(
                            current_price, strike_price, option_type
                        )
                        time_value = await self._get_time_value(symbol, intrinsic_value)

                        # 创建手动行权请求
                        request_id = await self._create_manual_exercise_request(
                            symbol=symbol,
                            option_type=option_type,
                            strike_price=strike_price,
                            expiry_date=expiry_date,
                            position_size=position_size,
                            current_price=current_price,
                            intrinsic_value=intrinsic_value,
                            time_value=time_value,
                            decision=ExerciseDecision(decision),
                            account_id=data.get("account_id", "default"),
                            strategy_id=data.get("strategy_id"),
                        )

                        if self.logger:
                            await self.logger.info(
                                f"Manual exercise request created: {request_id}"
                            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Manual request handling failed: {e}")

    async def _create_manual_exercise_request(self, **kwargs) -> str:
        """创建手动行权请求"""
        try:
            import uuid

            request_id = str(uuid.uuid4())

            # 计算所需资金
            required_funds = Decimal("0")
            if (
                kwargs["option_type"] == "call"
                and kwargs["decision"] == ExerciseDecision.EXERCISE
            ):
                required_funds = kwargs["strike_price"] * kwargs["position_size"]

            # 检查可用资金
            available_funds = await self._get_available_funds(
                kwargs.get("account_id", "default")
            )

            request = ExerciseRequest(
                request_id=request_id,
                symbol=kwargs["symbol"],
                option_type=kwargs["option_type"],
                strike_price=kwargs["strike_price"],
                expiry_date=kwargs["expiry_date"],
                position_size=kwargs["position_size"],
                current_price=kwargs["current_price"],
                decision=kwargs["decision"],
                exercise_type=ExerciseType.MANUAL,
                intrinsic_value=kwargs["intrinsic_value"],
                time_value=kwargs["time_value"],
                required_funds=required_funds,
                available_funds=available_funds,
                account_id=kwargs.get("account_id", "default"),
                strategy_id=kwargs.get("strategy_id"),
            )

            self.exercise_requests[request_id] = request
            self.pending_exercises.append(request_id)

            return request_id

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Manual exercise request creation failed: {e}")
            return ""

    async def _update_statistics(self):
        """更新统计信息"""
        try:
            total_requests = len(self.exercise_requests)
            auto_exercises = sum(
                1
                for req in self.exercise_requests.values()
                if req.exercise_type == ExerciseType.AUTOMATIC
                and req.decision == ExerciseDecision.EXERCISE
            )
            manual_exercises = sum(
                1
                for req in self.exercise_requests.values()
                if req.exercise_type == ExerciseType.MANUAL
                and req.decision == ExerciseDecision.EXERCISE
            )
            abandoned_options = sum(
                1
                for req in self.exercise_requests.values()
                if req.decision == ExerciseDecision.ABANDON
            )

            # 成功率计算
            successful_results = sum(
                1 for result in self.exercise_history if result.success
            )
            success_rate = (
                successful_results / len(self.exercise_history)
                if self.exercise_history
                else 0.0
            )

            auto_successful = sum(
                1
                for req_id, result in zip(
                    self.exercise_requests.keys(), self.exercise_history
                )
                if self.exercise_requests.get(req_id, {}).exercise_type
                == ExerciseType.AUTOMATIC
                and result.success
            )
            auto_total = sum(
                1
                for req in self.exercise_requests.values()
                if req.exercise_type == ExerciseType.AUTOMATIC
            )
            auto_success_rate = auto_successful / auto_total if auto_total > 0 else 0.0

            # 价值统计
            total_intrinsic_value = sum(
                req.intrinsic_value * req.position_size
                for req in self.exercise_requests.values()
            )
            total_settlement = sum(
                result.settlement_amount for result in self.exercise_history
            )

            # 处理时间统计
            processing_times = []
            for req in self.exercise_requests.values():
                if req.executed_at:
                    processing_time = (req.executed_at - req.created_at).total_seconds()
                    processing_times.append(processing_time)

            avg_processing_time = (
                sum(processing_times) / len(processing_times)
                if processing_times
                else 0.0
            )

            self.stats = ExerciseStats(
                total_requests=total_requests,
                auto_exercises=auto_exercises,
                manual_exercises=manual_exercises,
                abandoned_options=abandoned_options,
                success_rate=success_rate,
                auto_success_rate=auto_success_rate,
                total_intrinsic_value=total_intrinsic_value,
                total_settlement=total_settlement,
                avg_processing_time=avg_processing_time,
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Statistics update failed: {e}")

    async def _save_exercise_data(self):
        """保存行权数据"""
        try:
            if self.cache_manager:
                # 保存历史记录
                history_data = [
                    self._exercise_result_to_dict(result)
                    for result in self.exercise_history[-1000:]
                ]
                await self.cache_manager.set(
                    "exercise_history", history_data, ttl=86400
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Exercise data saving failed: {e}")

    # 公共接口方法

    async def submit_manual_exercise(
        self, symbol: str, decision: str, account_id: str = "default"
    ) -> str:
        """提交手动行权请求"""
        try:
            if decision not in ["exercise", "abandon"]:
                raise ValueError("Invalid decision, must be 'exercise' or 'abandon'")

            # 发布手动请求事件
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type="manual_exercise_request",
                        data={
                            "symbol": symbol,
                            "decision": decision,
                            "account_id": account_id,
                        },
                    )
                )

            return "Manual exercise request submitted"

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Manual exercise submission failed: {e}")
            return f"Error: {e}"

    async def get_exercise_request(self, request_id: str) -> Optional[Dict[str, Any]]:
        """获取行权请求"""
        try:
            if request_id in self.exercise_requests:
                return self._exercise_request_to_dict(
                    self.exercise_requests[request_id]
                )
            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get exercise request failed: {e}")
            return None

    async def get_exercise_statistics(self) -> Dict[str, Any]:
        """获取行权统计"""
        try:
            return {
                "total_requests": self.stats.total_requests,
                "auto_exercises": self.stats.auto_exercises,
                "manual_exercises": self.stats.manual_exercises,
                "abandoned_options": self.stats.abandoned_options,
                "success_rate": round(self.stats.success_rate, 4),
                "auto_success_rate": round(self.stats.auto_success_rate, 4),
                "total_intrinsic_value": float(self.stats.total_intrinsic_value),
                "total_settlement": float(self.stats.total_settlement),
                "avg_processing_time": round(self.stats.avg_processing_time, 2),
                "pending_requests": len(self.pending_exercises),
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get exercise statistics failed: {e}")
            return {"status": "error", "message": str(e)}

    async def get_pending_exercises(self) -> List[Dict[str, Any]]:
        """获取待处理行权请求"""
        try:
            pending_requests = []
            for request_id in self.pending_exercises:
                if request_id in self.exercise_requests:
                    request_dict = self._exercise_request_to_dict(
                        self.exercise_requests[request_id]
                    )
                    pending_requests.append(request_dict)
            return pending_requests

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get pending exercises failed: {e}")
            return []

    def _exercise_request_to_dict(self, request: ExerciseRequest) -> Dict[str, Any]:
        """行权请求转字典"""
        return {
            "request_id": request.request_id,
            "symbol": request.symbol,
            "option_type": request.option_type,
            "strike_price": float(request.strike_price),
            "expiry_date": request.expiry_date.isoformat(),
            "position_size": float(request.position_size),
            "current_price": float(request.current_price),
            "decision": request.decision.value,
            "exercise_type": request.exercise_type.value,
            "intrinsic_value": float(request.intrinsic_value),
            "time_value": float(request.time_value),
            "status": request.status.value,
            "created_at": request.created_at.isoformat(),
            "executed_at": request.executed_at.isoformat()
            if request.executed_at
            else None,
            "required_funds": float(request.required_funds),
            "available_funds": float(request.available_funds),
            "account_id": request.account_id,
            "strategy_id": request.strategy_id,
            "error_message": request.error_message,
            "retry_count": request.retry_count,
            "tags": request.tags,
        }

    def _exercise_result_to_dict(self, result: ExerciseResult) -> Dict[str, Any]:
        """行权结果转字典"""
        return {
            "request_id": result.request_id,
            "success": result.success,
            "executed_at": result.executed_at.isoformat(),
            "exercised_quantity": float(result.exercised_quantity),
            "settlement_amount": float(result.settlement_amount),
            "commission": float(result.commission),
            "position_changes": {
                k: float(v) for k, v in result.position_changes.items()
            },
            "error_message": result.error_message,
        }

    def _dict_to_exercise_result(self, data: Dict[str, Any]) -> ExerciseResult:
        """字典转行权结果"""
        position_changes = {
            k: Decimal(str(v)) for k, v in data.get("position_changes", {}).items()
        }

        return ExerciseResult(
            request_id=data["request_id"],
            success=data["success"],
            executed_at=datetime.fromisoformat(data["executed_at"]),
            exercised_quantity=Decimal(str(data.get("exercised_quantity", 0))),
            settlement_amount=Decimal(str(data.get("settlement_amount", 0))),
            commission=Decimal(str(data.get("commission", 0))),
            position_changes=position_changes,
            error_message=data.get("error_message"),
        )

    def set_event_bus(self, event_bus: EventBus):
        """设置事件总线"""
        self.event_bus = event_bus

    def set_cache_manager(self, cache_manager: CacheManager):
        """设置缓存管理器"""
        self.cache_manager = cache_manager
