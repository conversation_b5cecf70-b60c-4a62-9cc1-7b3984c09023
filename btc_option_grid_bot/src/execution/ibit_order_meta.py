from __future__ import annotations
"""
IBIT 期权订单元数据结构化定义
- 替代 order.tags 的弱类型字典，提供强类型与验证
- 用于 pretrade 校验与 IBKR 执行路径
"""

from dataclasses import dataclass
from decimal import Decimal
from typing import Optional
from datetime import date

from ..gateways.ibkr_client import OptionContract, Right


@dataclass
class IBITOptionOrderMeta:
    """IBIT 期权订单元数据"""
    
    # 策略动作
    action: str  # 'SELL_PUT_CSP' | 'LONG_CALL' | 'BUY_SPOT'
    
    # 合约信息
    contract: Optional[OptionContract] = None
    
    # 市场数据（用于 pretrade 校验）
    spread_bps: Optional[float] = None
    open_interest: Optional[int] = None
    volume: Optional[int] = None
    iv: Optional[float] = None
    delta: Optional[float] = None
    
    # 风控参数
    buying_power: Optional[Decimal] = None
    days_to_expiry: Optional[int] = None
    
    # 映射来源
    btc_price: Optional[Decimal] = None
    ibit_estimated_price: Optional[Decimal] = None
    tracking_error_bps: Optional[float] = None
    
    def validate_for_pretrade(self) -> tuple[bool, str]:
        """验证是否具备 pretrade 校验所需的最小字段"""
        if not self.action:
            return False, "Missing action"
        
        if self.action == "SELL_PUT_CSP":
            if self.contract is None or self.buying_power is None:
                return False, "CSP requires contract and buying_power"
        
        elif self.action == "LONG_CALL":
            if self.days_to_expiry is None:
                return False, "Long Call requires days_to_expiry"
        
        # 流动性校验字段
        if self.spread_bps is None or self.open_interest is None or self.volume is None:
            return False, "Missing liquidity data (spread_bps/oi/volume)"
        
        return True, "OK"
    
    @classmethod
    def from_tags(cls, tags: dict) -> 'IBITOptionOrderMeta':
        """从旧的 tags 字典创建（向后兼容）"""
        # 尝试从 symbol 解析合约信息（简化实现）
        contract = None
        symbol = tags.get("symbol", "")
        if symbol.startswith("IBIT ") and (" C" in symbol or " P" in symbol):
            try:
                parts = symbol.split()
                if len(parts) >= 4:
                    underlying = parts[0]
                    expiry_str = parts[1]
                    strike_str = parts[2]
                    right_str = parts[3]

                    from datetime import datetime
                    expiry = datetime.strptime(expiry_str, "%Y-%m-%d").date()
                    strike = Decimal(strike_str)
                    right = Right.CALL if right_str.endswith("C") else Right.PUT

                    contract = OptionContract(underlying, expiry, strike, right)
            except Exception:
                pass  # 解析失败，保持 None

        return cls(
            action=tags.get("action", ""),
            contract=contract,
            spread_bps=tags.get("spread_bps"),
            open_interest=tags.get("oi"),
            volume=tags.get("volume"),
            iv=tags.get("iv"),
            delta=tags.get("delta"),
            buying_power=Decimal(str(tags["bp"])) if tags.get("bp") else None,
            days_to_expiry=tags.get("days_to_expiry"),
            btc_price=Decimal(str(tags["btc_price"])) if tags.get("btc_price") else None,
            ibit_estimated_price=Decimal(str(tags["ibit_est"])) if tags.get("ibit_est") else None,
            tracking_error_bps=tags.get("tracking_error_bps"),
        )
