"""
仓位管理器

负责实时仓位跟踪、多账户管理、异常检测和修复
"""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

from ..core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from ..core.event_bus import BaseEvent, EventBus
from ..data.cache_manager import CacheManager
from ..risk.pretrade_filters import PretradeFilters


class PositionType(Enum):
    """仓位类型"""

    SPOT = "spot"
    OPTION = "option"
    FUTURE = "future"


class PositionSide(Enum):
    """仓位方向"""

    LONG = "long"
    SHORT = "short"
    NEUTRAL = "neutral"


@dataclass
class Position:
    """仓位对象"""

    symbol: str
    position_type: PositionType
    side: PositionSide
    size: Decimal
    avg_price: Decimal
    market_value: Decimal
    unrealized_pnl: Decimal = Decimal("0")
    realized_pnl: Decimal = Decimal("0")

    # 期权特有属性
    strike_price: Optional[Decimal] = None
    expiry_date: Optional[datetime] = None
    option_type: Optional[str] = None  # 'call' or 'put'

    # 时间戳
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

    # 账户信息
    account_id: str = "default"
    strategy_id: Optional[str] = None

    # 风险指标
    delta: Optional[Decimal] = None
    gamma: Optional[Decimal] = None
    theta: Optional[Decimal] = None
    vega: Optional[Decimal] = None

    # 元数据
    tags: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PositionSnapshot:
    """仓位快照"""

    timestamp: datetime
    positions: Dict[str, Position]
    total_value: Decimal
    total_pnl: Decimal
    account_id: str


@dataclass
class PositionStats:
    """仓位统计"""

    total_positions: int = 0
    long_positions: int = 0
    short_positions: int = 0
    total_value: Decimal = Decimal("0")
    total_unrealized_pnl: Decimal = Decimal("0")
    total_realized_pnl: Decimal = Decimal("0")
    largest_position: Optional[str] = None
    largest_position_value: Decimal = Decimal("0")


class PositionManager(BaseComponent):
    """
    仓位管理器

    功能特性：
    - 实时仓位跟踪和同步
    - 多账户和多策略仓位管理
    - 仓位异常检测和修复
    - 仓位历史查询和导出
    - 仓位风险监控和告警
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("PositionManager", config)

        # 组件依赖
        self.event_bus: Optional[EventBus] = None
        self.cache_manager: Optional[CacheManager] = None

        # 仓位配置
        self._load_position_config()

        # 仓位存储
        self.positions: Dict[str, Position] = {}  # symbol -> Position
        self.account_positions: Dict[
            str, Dict[str, Position]
        ] = {}  # account_id -> positions
        self.strategy_positions: Dict[
            str, Dict[str, Position]
        ] = {}  # strategy_id -> positions

        # 历史快照
        self.position_snapshots: List[PositionSnapshot] = []
        self._max_snapshots = 1000

        # 同步任务
        self._sync_task: Optional[asyncio.Task] = None
        self._monitor_task: Optional[asyncio.Task] = None
        self._sync_interval = 10  # 10秒同步间隔

        # 异常检测
        self.anomaly_threshold = Decimal("0.05")  # 5%差异阈值
        self.last_sync_time: Optional[datetime] = None

        # 统计信息
        self.stats = PositionStats()

        # 支持的交易所
        self.supported_exchanges = set()
        # 预交易过滤器（仅对 IBIT/IBKR 路径启用）
        self.pretrade_filters = PretradeFilters()

        self._load_exchange_config()

    def _load_position_config(self):
        """加载仓位配置"""
        position_config = self.config.get("positions", {}) if self.config else {}

        # 基础配置
        self.position_settings = {
            "enable_real_time_sync": position_config.get("enable_real_time_sync", True),
            "sync_interval": position_config.get("sync_interval", 10),
            "enable_anomaly_detection": position_config.get(
                "enable_anomaly_detection", True
            ),
            "anomaly_threshold": Decimal(
                str(position_config.get("anomaly_threshold", "0.05"))
            ),
            "max_position_age_hours": position_config.get("max_position_age_hours", 24),
            "enable_auto_reconciliation": position_config.get(
                "enable_auto_reconciliation", True
            ),
        }

        # 更新配置
        self._sync_interval = self.position_settings["sync_interval"]
        self.anomaly_threshold = self.position_settings["anomaly_threshold"]

    def _load_exchange_config(self):
        """加载交易所配置"""
        exchange_config = self.config.get("exchanges", {}) if self.config else {}

        # 默认支持的交易所
        self.supported_exchanges = set(
            exchange_config.get("supported", ["deribit", "binance"])
        )

    async def _initialize_impl(self) -> bool:
        """初始化仓位管理器"""
        try:
            # 订阅事件
            if self.event_bus:
                await self.event_bus.subscribe("order_filled", self._handle_order_fill)
                await self.event_bus.subscribe(
                    "trade_executed", self._handle_trade_execution
                )
                await self.event_bus.subscribe(
                    "position_update", self._handle_position_update
                )
                await self.event_bus.subscribe(
                    "market_data_update", self._handle_market_update
                )

            # 初始化账户仓位
            self.account_positions["default"] = {}

            # 加载初始仓位
            await self._load_initial_positions()

            if self.logger:
                await self.logger.info("PositionManager initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"PositionManager initialization failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动仓位管理器"""
        try:
            # 启动同步任务
            if self.position_settings["enable_real_time_sync"]:
                self._sync_task = asyncio.create_task(self._position_sync_loop())
                self._monitor_task = asyncio.create_task(self._position_monitor_loop())

            if self.logger:
                await self.logger.info("PositionManager started")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"PositionManager start failed: {e}")
            return False

    async def pretrade_check_ibit(self,
                                 action: str,
                                 strike: Optional[Decimal] = None,
                                 multiplier: int = 100,
                                 qty: int = 1,
                                 spread_bps: Optional[float] = None,
                                 oi: Optional[int] = None,
                                 volume: Optional[int] = None,
                                 buying_power: Optional[Decimal] = None,
                                 days_to_expiry: Optional[int] = None,
                                 iv: Optional[float] = None) -> Dict[str, Any]:
        """
        IBIT/IBKR 路径的最小预交易校验：
        - action: 'SELL_PUT_CSP' or 'LONG_CALL'
        返回: { ok: bool, reasons: [..] }
        """
        reasons: List[str] = []
        ok = True
        # 流动性过滤
        liq = self.pretrade_filters.liquidity(spread_bps, oi, volume)
        if not (liq.spread_bps_ok and liq.oi_ok and liq.vol_ok):
            ok = False
            reasons.append(liq.reason or "LIQ_FAIL")
        # 动作特定校验
        if action == "SELL_PUT_CSP":
            if strike is None or buying_power is None:
                ok = False
                reasons.append("MISSING_STRIKE_OR_BP")
            else:
                chk = self.pretrade_filters.csp_buying_power(strike, multiplier, qty, buying_power)
                if not chk.ok:
                    ok = False
                    reasons.append(chk.reason or "BP_FAIL")
        elif action == "LONG_CALL":
            if days_to_expiry is None:
                reasons.append("MISSING_TTE")
            if not self.pretrade_filters.long_call_theta_guard(days_to_expiry or 0, iv):
                ok = False
                reasons.append("THETA_GUARD")
        return {"ok": ok, "reasons": reasons}


    async def _stop_impl(self) -> bool:
        """停止仓位管理器"""
        try:
            # 停止同步任务
            if self._sync_task and not self._sync_task.done():
                self._sync_task.cancel()
                try:
                    await self._sync_task
                except asyncio.CancelledError:
                    pass

            if self._monitor_task and not self._monitor_task.done():
                self._monitor_task.cancel()
                try:
                    await self._monitor_task
                except asyncio.CancelledError:
                    pass

            # 保存最终快照
            await self._create_snapshot()

            if self.logger:
                await self.logger.info("PositionManager stopped")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"PositionManager stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查"""
        try:
            # 检查同步任务状态
            if self.position_settings["enable_real_time_sync"]:
                if not self._sync_task or self._sync_task.done():
                    return HealthCheckResult(
                        status=HealthStatus.UNHEALTHY,
                        message="Position sync task not running",
                    )

            # 检查最后同步时间
            if self.last_sync_time:
                time_since_sync = (
                    datetime.now(timezone.utc) - self.last_sync_time
                ).total_seconds()
                if time_since_sync > self._sync_interval * 3:
                    return HealthCheckResult(
                        status=HealthStatus.DEGRADED,
                        message=f"Position sync delayed: {time_since_sync:.1f}s",
                    )

            # 检查仓位数量
            position_count = len(self.positions)
            if position_count > 1000:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"Large number of positions: {position_count}",
                )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message=f"Position system operational, {position_count} positions tracked",
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    async def _load_initial_positions(self):
        """加载初始仓位"""
        try:
            # 从缓存加载仓位
            if self.cache_manager:
                cached_positions = await self.cache_manager.get("positions")
                if cached_positions:
                    for symbol, position_data in cached_positions.items():
                        position = self._dict_to_position(position_data)
                        await self._add_position(position)

            # 从交易所同步仓位
            await self._sync_from_exchanges()

            if self.logger:
                await self.logger.info(
                    f"Loaded {len(self.positions)} initial positions"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Initial position loading failed: {e}")

    async def _position_sync_loop(self):
        """仓位同步循环"""
        while True:
            try:
                await self._sync_from_exchanges()
                await self._detect_anomalies()
                await self._update_statistics()
                self.last_sync_time = datetime.now(timezone.utc)

                await asyncio.sleep(self._sync_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Position sync error: {e}")
                await asyncio.sleep(30)  # 错误后等待30秒

    async def _position_monitor_loop(self):
        """仓位监控循环"""
        while True:
            try:
                await self._monitor_position_health()
                await self._create_snapshot()
                await self._cleanup_old_data()
                await asyncio.sleep(60)  # 1分钟监控间隔

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Position monitor error: {e}")
                await asyncio.sleep(60)

    async def _sync_from_exchanges(self):
        """从交易所同步仓位"""
        try:
            # 模拟从交易所获取仓位数据
            # 实际实现中需要调用各交易所的API

            for exchange in self.supported_exchanges:
                exchange_positions = await self._fetch_exchange_positions(exchange)

                for position_data in exchange_positions:
                    position = self._create_position_from_data(position_data)
                    await self._update_position(position)

            # 缓存仓位数据
            if self.cache_manager:
                positions_data = {
                    symbol: self._position_to_dict(pos)
                    for symbol, pos in self.positions.items()
                }
                await self.cache_manager.set("positions", positions_data, ttl=300)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Exchange sync failed: {e}")

    async def _fetch_exchange_positions(self, exchange: str) -> List[Dict[str, Any]]:
        """获取交易所仓位数据"""
        try:
            # 模拟交易所API调用
            # 实际实现中需要调用具体的交易所API

            # 模拟返回数据
            return [
                {
                    "symbol": "BTC-25DEC21-50000-C",
                    "size": "10",
                    "avg_price": "0.05",
                    "market_value": "500",
                    "unrealized_pnl": "50",
                    "position_type": "option",
                    "exchange": exchange,
                }
            ]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Fetch positions from {exchange} failed: {e}")
            return []

    # 事件处理方法

    async def _handle_order_fill(self, event: BaseEvent):
        """处理订单成交事件"""
        try:
            data = event.data
            symbol = data.get("symbol")
            side = data.get("side")
            quantity = Decimal(str(data.get("quantity", 0)))
            price = Decimal(str(data.get("price", 0)))

            if symbol in self.positions:
                position = self.positions[symbol]

                # 更新仓位
                if side == "buy":
                    new_size = position.size + quantity
                    new_avg_price = (
                        (position.size * position.avg_price) + (quantity * price)
                    ) / new_size
                    position.size = new_size
                    position.avg_price = new_avg_price
                else:  # sell
                    position.size = max(Decimal("0"), position.size - quantity)
                    # 计算已实现盈亏
                    realized_pnl = quantity * (price - position.avg_price)
                    position.realized_pnl += realized_pnl

                position.updated_at = datetime.now(timezone.utc)

                # 更新市值
                position.market_value = position.size * price

                if self.logger:
                    await self.logger.info(
                        f"Position updated from order fill: {symbol}"
                    )
            else:
                # 创建新仓位
                position_type = (
                    PositionType.OPTION if "BTC-" in symbol else PositionType.SPOT
                )
                position_side = (
                    PositionSide.LONG if side == "buy" else PositionSide.SHORT
                )

                new_position = Position(
                    symbol=symbol,
                    position_type=position_type,
                    side=position_side,
                    size=quantity,
                    avg_price=price,
                    market_value=quantity * price,
                    account_id=data.get("account_id", "default"),
                    strategy_id=data.get("strategy_id"),
                )

                await self._add_position(new_position)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order fill handling failed: {e}")

    async def _handle_trade_execution(self, event: BaseEvent):
        """处理交易执行事件"""
        try:
            # 类似于订单成交处理
            await self._handle_order_fill(event)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Trade execution handling failed: {e}")

    async def _handle_position_update(self, event: BaseEvent):
        """处理仓位更新事件"""
        try:
            data = event.data
            symbol = data.get("symbol")

            if symbol in self.positions:
                position = self.positions[symbol]

                # 更新仓位属性
                if "size" in data:
                    position.size = Decimal(str(data["size"]))
                if "market_value" in data:
                    position.market_value = Decimal(str(data["market_value"]))
                if "unrealized_pnl" in data:
                    position.unrealized_pnl = Decimal(str(data["unrealized_pnl"]))

                position.updated_at = datetime.now(timezone.utc)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Position update handling failed: {e}")

    async def _handle_market_update(self, event: BaseEvent):
        """处理市场数据更新"""
        try:
            data = event.data
            symbol = data.get("symbol")

            if symbol in self.positions:
                position = self.positions[symbol]

                # 更新市值和未实现盈亏
                if "last_price" in data:
                    last_price = Decimal(str(data["last_price"]))
                    position.market_value = position.size * last_price
                    position.unrealized_pnl = position.size * (
                        last_price - position.avg_price
                    )

                # 更新Greeks（期权）
                if position.position_type == PositionType.OPTION:
                    if "delta" in data:
                        position.delta = Decimal(str(data["delta"]))
                    if "gamma" in data:
                        position.gamma = Decimal(str(data["gamma"]))
                    if "theta" in data:
                        position.theta = Decimal(str(data["theta"]))
                    if "vega" in data:
                        position.vega = Decimal(str(data["vega"]))

                position.updated_at = datetime.now(timezone.utc)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Market update handling failed: {e}")

    # 公共接口方法

    async def get_position(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取单个仓位"""
        try:
            if symbol in self.positions:
                return self._position_to_dict(self.positions[symbol])
            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get position failed: {e}")
            return None

    async def get_all_positions(
        self, account_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取所有仓位"""
        try:
            if account_id:
                positions = self.account_positions.get(account_id, {})
                return [self._position_to_dict(pos) for pos in positions.values()]
            else:
                return [self._position_to_dict(pos) for pos in self.positions.values()]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get all positions failed: {e}")
            return []

    async def get_positions_by_strategy(self, strategy_id: str) -> List[Dict[str, Any]]:
        """按策略获取仓位"""
        try:
            positions = self.strategy_positions.get(strategy_id, {})
            return [self._position_to_dict(pos) for pos in positions.values()]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get positions by strategy failed: {e}")
            return []

    async def get_position_statistics(self) -> Dict[str, Any]:
        """获取仓位统计"""
        try:
            return {
                "total_positions": self.stats.total_positions,
                "long_positions": self.stats.long_positions,
                "short_positions": self.stats.short_positions,
                "total_value": float(self.stats.total_value),
                "total_unrealized_pnl": float(self.stats.total_unrealized_pnl),
                "total_realized_pnl": float(self.stats.total_realized_pnl),
                "largest_position": self.stats.largest_position,
                "largest_position_value": float(self.stats.largest_position_value),
                "last_sync_time": self.last_sync_time.isoformat()
                if self.last_sync_time
                else None,
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get statistics failed: {e}")
            return {"status": "error", "message": str(e)}

    def _position_to_dict(self, position: Position) -> Dict[str, Any]:
        """仓位对象转字典"""
        return {
            "symbol": position.symbol,
            "position_type": position.position_type.value,
            "side": position.side.value,
            "size": float(position.size),
            "avg_price": float(position.avg_price),
            "market_value": float(position.market_value),
            "unrealized_pnl": float(position.unrealized_pnl),
            "realized_pnl": float(position.realized_pnl),
            "strike_price": float(position.strike_price)
            if position.strike_price
            else None,
            "expiry_date": position.expiry_date.isoformat()
            if position.expiry_date
            else None,
            "option_type": position.option_type,
            "created_at": position.created_at.isoformat(),
            "updated_at": position.updated_at.isoformat(),
            "account_id": position.account_id,
            "strategy_id": position.strategy_id,
            "delta": float(position.delta) if position.delta else None,
            "gamma": float(position.gamma) if position.gamma else None,
            "theta": float(position.theta) if position.theta else None,
            "vega": float(position.vega) if position.vega else None,
            "tags": position.tags,
        }

    def _dict_to_position(self, data: Dict[str, Any]) -> Position:
        """字典转仓位对象"""
        return Position(
            symbol=data["symbol"],
            position_type=PositionType(data["position_type"]),
            side=PositionSide(data["side"]),
            size=Decimal(str(data["size"])),
            avg_price=Decimal(str(data["avg_price"])),
            market_value=Decimal(str(data["market_value"])),
            unrealized_pnl=Decimal(str(data.get("unrealized_pnl", 0))),
            realized_pnl=Decimal(str(data.get("realized_pnl", 0))),
            strike_price=Decimal(str(data["strike_price"]))
            if data.get("strike_price")
            else None,
            expiry_date=datetime.fromisoformat(data["expiry_date"])
            if data.get("expiry_date")
            else None,
            option_type=data.get("option_type"),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"]),
            account_id=data.get("account_id", "default"),
            strategy_id=data.get("strategy_id"),
            delta=Decimal(str(data["delta"])) if data.get("delta") else None,
            gamma=Decimal(str(data["gamma"])) if data.get("gamma") else None,
            theta=Decimal(str(data["theta"])) if data.get("theta") else None,
            vega=Decimal(str(data["vega"])) if data.get("vega") else None,
            tags=data.get("tags", {}),
        )

    def set_event_bus(self, event_bus: EventBus):
        """设置事件总线"""
        self.event_bus = event_bus

    def set_cache_manager(self, cache_manager: CacheManager):
        """设置缓存管理器"""
        self.cache_manager = cache_manager

    def _create_position_from_data(self, data: Dict[str, Any]) -> Position:
        """从数据创建仓位对象"""
        try:
            symbol = data["symbol"]
            size = Decimal(str(data["size"]))

            # 确定仓位类型和方向
            position_type = PositionType(data.get("position_type", "spot"))
            side = (
                PositionSide.LONG
                if size > 0
                else PositionSide.SHORT
                if size < 0
                else PositionSide.NEUTRAL
            )

            # 解析期权信息
            strike_price = None
            expiry_date = None
            option_type = None

            if position_type == PositionType.OPTION and "BTC-" in symbol:
                parts = symbol.split("-")
                if len(parts) >= 4:
                    strike_price = Decimal(parts[2])
                    option_type = "call" if parts[3] == "C" else "put"
                    # 解析到期日期
                    try:
                        expiry_date = datetime.strptime(parts[1], "%d%b%y").replace(
                            tzinfo=timezone.utc
                        )
                    except ValueError:
                        pass

            return Position(
                symbol=symbol,
                position_type=position_type,
                side=side,
                size=abs(size),
                avg_price=Decimal(str(data.get("avg_price", "0"))),
                market_value=Decimal(str(data.get("market_value", "0"))),
                unrealized_pnl=Decimal(str(data.get("unrealized_pnl", "0"))),
                strike_price=strike_price,
                expiry_date=expiry_date,
                option_type=option_type,
                account_id=data.get("account_id", "default"),
            )

        except Exception as e:
            if self.logger:
                asyncio.create_task(self.logger.error(f"Position creation failed: {e}"))
            # 返回默认仓位
            return Position(
                symbol=data.get("symbol", "UNKNOWN"),
                position_type=PositionType.SPOT,
                side=PositionSide.NEUTRAL,
                size=Decimal("0"),
                avg_price=Decimal("0"),
                market_value=Decimal("0"),
            )

    async def _add_position(self, position: Position):
        """添加仓位"""
        try:
            self.positions[position.symbol] = position

            # 按账户分组
            if position.account_id not in self.account_positions:
                self.account_positions[position.account_id] = {}
            self.account_positions[position.account_id][position.symbol] = position

            # 按策略分组
            if position.strategy_id:
                if position.strategy_id not in self.strategy_positions:
                    self.strategy_positions[position.strategy_id] = {}
                self.strategy_positions[position.strategy_id][position.symbol] = (
                    position
                )

            # 发布事件
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type="position_added",
                        data={
                            "symbol": position.symbol,
                            "size": float(position.size),
                            "side": position.side.value,
                            "account_id": position.account_id,
                        },
                    )
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Add position failed: {e}")

    async def _update_position(self, new_position: Position):
        """更新仓位"""
        try:
            symbol = new_position.symbol

            if symbol in self.positions:
                old_position = self.positions[symbol]

                # 检查是否有显著变化
                size_change = abs(new_position.size - old_position.size)
                if size_change > self.anomaly_threshold * old_position.size:
                    if self.logger:
                        await self.logger.warning(
                            f"Significant position change detected: {symbol}"
                        )

                # 更新仓位
                new_position.updated_at = datetime.now(timezone.utc)
                await self._add_position(new_position)

                # 发布更新事件
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type="position_updated",
                            data={
                                "symbol": symbol,
                                "old_size": float(old_position.size),
                                "new_size": float(new_position.size),
                                "size_change": float(size_change),
                            },
                        )
                    )
            else:
                # 新仓位
                await self._add_position(new_position)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Update position failed: {e}")

    async def _detect_anomalies(self):
        """检测仓位异常"""
        try:
            if not self.position_settings["enable_anomaly_detection"]:
                return

            anomalies = []
            current_time = datetime.now(timezone.utc)

            for symbol, position in self.positions.items():
                # 检查仓位年龄
                age_hours = (current_time - position.updated_at).total_seconds() / 3600
                if age_hours > self.position_settings["max_position_age_hours"]:
                    anomalies.append(
                        {
                            "type": "stale_position",
                            "symbol": symbol,
                            "age_hours": age_hours,
                        }
                    )

                # 检查异常大小
                if position.size == 0 and position.market_value != 0:
                    anomalies.append(
                        {
                            "type": "zero_size_nonzero_value",
                            "symbol": symbol,
                            "market_value": float(position.market_value),
                        }
                    )

                # 检查价格异常
                if position.avg_price <= 0 and position.size > 0:
                    anomalies.append(
                        {
                            "type": "invalid_price",
                            "symbol": symbol,
                            "avg_price": float(position.avg_price),
                        }
                    )

            # 处理异常
            if anomalies:
                await self._handle_anomalies(anomalies)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Anomaly detection failed: {e}")

    async def _handle_anomalies(self, anomalies: List[Dict[str, Any]]):
        """处理仓位异常"""
        try:
            for anomaly in anomalies:
                if self.logger:
                    await self.logger.warning(f"Position anomaly detected: {anomaly}")

                # 发布异常事件
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(event_type="position_anomaly", data=anomaly)
                    )

                # 自动修复
                if self.position_settings["enable_auto_reconciliation"]:
                    await self._auto_reconcile_position(anomaly)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Anomaly handling failed: {e}")

    async def _auto_reconcile_position(self, anomaly: Dict[str, Any]):
        """自动修复仓位"""
        try:
            symbol = anomaly.get("symbol")
            anomaly_type = anomaly.get("type")

            if anomaly_type == "stale_position":
                # 重新同步该仓位
                await self._force_sync_position(symbol)
            elif anomaly_type == "zero_size_nonzero_value":
                # 清零市值
                if symbol in self.positions:
                    self.positions[symbol].market_value = Decimal("0")
            elif anomaly_type == "invalid_price":
                # 重新获取价格
                await self._update_position_price(symbol)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Auto reconciliation failed: {e}")

    async def _force_sync_position(self, symbol: str):
        """强制同步单个仓位"""
        try:
            # 模拟强制同步
            # 实际实现中需要调用交易所API
            pass

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Force sync failed for {symbol}: {e}")

    async def _update_position_price(self, symbol: str):
        """更新仓位价格"""
        try:
            # 从市场数据获取最新价格
            if self.cache_manager:
                market_data = await self.cache_manager.get(f"market_data:{symbol}")
                if market_data and symbol in self.positions:
                    latest_price = Decimal(str(market_data.get("last_price", 0)))
                    if latest_price > 0:
                        position = self.positions[symbol]
                        position.market_value = position.size * latest_price
                        position.updated_at = datetime.now(timezone.utc)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Price update failed for {symbol}: {e}")

    async def _monitor_position_health(self):
        """监控仓位健康状态"""
        try:
            # 检查总仓位价值
            total_value = sum(pos.market_value for pos in self.positions.values())

            # 检查集中度风险
            if self.positions:
                largest_position = max(
                    self.positions.values(), key=lambda p: p.market_value
                )
                concentration = (
                    largest_position.market_value / total_value
                    if total_value > 0
                    else 0
                )

                if concentration > Decimal("0.5"):  # 50%集中度告警
                    if self.event_bus:
                        await self.event_bus.publish(
                            BaseEvent(
                                event_type="position_concentration_alert",
                                data={
                                    "symbol": largest_position.symbol,
                                    "concentration": float(concentration),
                                    "value": float(largest_position.market_value),
                                },
                            )
                        )

            # 检查到期风险
            await self._check_expiry_risk()

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Position health monitoring failed: {e}")

    async def _check_expiry_risk(self):
        """检查到期风险"""
        try:
            current_time = datetime.now(timezone.utc)

            for position in self.positions.values():
                if (
                    position.expiry_date
                    and position.position_type == PositionType.OPTION
                ):
                    days_to_expiry = (position.expiry_date - current_time).days

                    if days_to_expiry <= 7:  # 7天内到期
                        if self.event_bus:
                            await self.event_bus.publish(
                                BaseEvent(
                                    event_type="position_expiry_alert",
                                    data={
                                        "symbol": position.symbol,
                                        "days_to_expiry": days_to_expiry,
                                        "size": float(position.size),
                                        "market_value": float(position.market_value),
                                    },
                                )
                            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiry risk check failed: {e}")

    async def _create_snapshot(self):
        """创建仓位快照"""
        try:
            snapshot = PositionSnapshot(
                timestamp=datetime.now(timezone.utc),
                positions=self.positions.copy(),
                total_value=sum(pos.market_value for pos in self.positions.values()),
                total_pnl=sum(
                    pos.unrealized_pnl + pos.realized_pnl
                    for pos in self.positions.values()
                ),
                account_id="default",
            )

            self.position_snapshots.append(snapshot)

            # 限制快照数量
            if len(self.position_snapshots) > self._max_snapshots:
                self.position_snapshots = self.position_snapshots[
                    -self._max_snapshots :
                ]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Snapshot creation failed: {e}")

    async def _update_statistics(self):
        """更新统计信息"""
        try:
            total_positions = len(self.positions)
            long_positions = sum(
                1 for pos in self.positions.values() if pos.side == PositionSide.LONG
            )
            short_positions = sum(
                1 for pos in self.positions.values() if pos.side == PositionSide.SHORT
            )

            total_value = sum(pos.market_value for pos in self.positions.values())
            total_unrealized_pnl = sum(
                pos.unrealized_pnl for pos in self.positions.values()
            )
            total_realized_pnl = sum(
                pos.realized_pnl for pos in self.positions.values()
            )

            # 找到最大仓位
            largest_position = None
            largest_position_value = Decimal("0")

            if self.positions:
                largest_pos = max(self.positions.values(), key=lambda p: p.market_value)
                largest_position = largest_pos.symbol
                largest_position_value = largest_pos.market_value

            self.stats = PositionStats(
                total_positions=total_positions,
                long_positions=long_positions,
                short_positions=short_positions,
                total_value=total_value,
                total_unrealized_pnl=total_unrealized_pnl,
                total_realized_pnl=total_realized_pnl,
                largest_position=largest_position,
                largest_position_value=largest_position_value,
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Statistics update failed: {e}")

    async def _cleanup_old_data(self):
        """清理旧数据"""
        try:
            # 清理旧快照
            if len(self.position_snapshots) > self._max_snapshots:
                self.position_snapshots = self.position_snapshots[
                    -self._max_snapshots :
                ]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Data cleanup failed: {e}")
