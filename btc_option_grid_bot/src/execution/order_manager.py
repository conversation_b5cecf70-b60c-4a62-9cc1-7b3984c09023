"""
订单管理器

负责智能订单路由、执行优化、状态跟踪和重试机制
"""

import asyncio
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

from ..core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from ..core.event_bus import BaseEvent, EventBus
from ..data.cache_manager import CacheManager
from ..risk.pretrade_filters import PretradeFilters
from .ibit_order_meta import IBITOptionOrderMeta


class OrderType(Enum):
    """订单类型"""

    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    CONDITIONAL = "conditional"
    ICEBERG = "iceberg"
    TWO_WAY = "two_way"


class OrderSide(Enum):
    """订单方向"""

    BUY = "buy"
    SELL = "sell"


class OrderStatus(Enum):
    """订单状态"""

    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"
    FAILED = "failed"


class OrderPriority(Enum):
    """订单优先级"""

    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class Order:
    """订单对象"""

    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: Decimal
    price: Optional[Decimal] = None
    stop_price: Optional[Decimal] = None
    status: OrderStatus = OrderStatus.PENDING
    priority: OrderPriority = OrderPriority.NORMAL

    # 时间戳
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    submitted_at: Optional[datetime] = None
    filled_at: Optional[datetime] = None

    # 执行信息
    filled_quantity: Decimal = Decimal("0")
    avg_fill_price: Optional[Decimal] = None
    exchange_order_id: Optional[str] = None

    # 高级选项
    time_in_force: str = "GTC"  # GTC, IOC, FOK
    reduce_only: bool = False
    post_only: bool = False

    # 条件订单参数
    trigger_condition: Optional[Dict[str, Any]] = None
    parent_order_id: Optional[str] = None
    child_orders: List[str] = field(default_factory=list)

    # 重试信息
    retry_count: int = 0
    max_retries: int = 3
    last_error: Optional[str] = None

    # 元数据
    strategy_id: Optional[str] = None
    tags: Dict[str, Any] = field(default_factory=dict)
    ibit_meta: Optional[IBITOptionOrderMeta] = None  # IBIT 期权专用元数据


@dataclass
class OrderExecution:
    """订单执行记录"""

    execution_id: str
    order_id: str
    quantity: Decimal
    price: Decimal
    timestamp: datetime
    commission: Decimal = Decimal("0")
    exchange_execution_id: Optional[str] = None


@dataclass
class OrderStats:
    """订单统计"""

    total_orders: int = 0
    filled_orders: int = 0
    cancelled_orders: int = 0
    rejected_orders: int = 0
    avg_fill_time: float = 0.0
    success_rate: float = 0.0
    total_volume: Decimal = Decimal("0")


class OrderManager(BaseComponent):
    """
    订单管理器

    功能特性：
    - 智能订单路由和执行优化
    - 订单状态跟踪和重试机制
    - 批量订单处理和流控管理
    - 条件订单和高级订单类型
    - 订单执行统计和分析
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("OrderManager", config)

        # 组件依赖
        self.event_bus: Optional[EventBus] = None
        self.cache_manager: Optional[CacheManager] = None

        # 订单配置
        self._load_order_config()

        # 订单存储
        self.active_orders: Dict[str, Order] = {}
        self.order_history: List[Order] = []
        self.executions: List[OrderExecution] = []

        # 执行队列
        self.order_queue: asyncio.Queue = asyncio.Queue()
        self.priority_queue: asyncio.PriorityQueue = asyncio.PriorityQueue()

        # 执行任务
        self._executor_task: Optional[asyncio.Task] = None
        self._monitor_task: Optional[asyncio.Task] = None

        # 流控管理
        self.rate_limiter = {}
        self._max_orders_per_second = 10
        self._max_concurrent_orders = 50

        # 统计信息
        self.stats = OrderStats()
        # 预交易过滤器（仅对 IBIT/IBKR 路径使用）
        pretrade_config = self.config.get("pretrade_filters", {}) if self.config else {}
        self.pretrade_filters = PretradeFilters(pretrade_config)

        self._max_history_size = 10000

        # 路由配置
        self.routing_rules = {}
        self._load_routing_rules()

    def _load_order_config(self):
        """加载订单配置"""
        order_config = self.config.get("orders", {}) if self.config else {}

        # 基础配置
        self.order_settings = {
            "default_time_in_force": order_config.get("default_time_in_force", "GTC"),
            "max_order_size": Decimal(str(order_config.get("max_order_size", "1000"))),
            "min_order_size": Decimal(str(order_config.get("min_order_size", "0.001"))),
            "default_retry_count": order_config.get("default_retry_count", 3),
            "execution_timeout": order_config.get("execution_timeout", 30),
            "enable_smart_routing": order_config.get("enable_smart_routing", True),
            "enable_order_splitting": order_config.get("enable_order_splitting", True),
        }

        # 流控配置
        flow_control = order_config.get("flow_control", {})
        self._max_orders_per_second = flow_control.get("max_orders_per_second", 10)
        self._max_concurrent_orders = flow_control.get("max_concurrent_orders", 50)

    def _load_routing_rules(self):
        """加载路由规则"""
        routing_config = self.config.get("routing", {}) if self.config else {}

        # 默认路由规则
        self.routing_rules = {
            "default_exchange": routing_config.get("default_exchange", "deribit"),
            "option_exchange": routing_config.get("option_exchange", "deribit"),
            "spot_exchange": routing_config.get("spot_exchange", "binance"),
            "routing_strategy": routing_config.get("routing_strategy", "best_price"),
            "enable_cross_exchange": routing_config.get("enable_cross_exchange", False),
        }

    async def _initialize_impl(self) -> bool:
        """初始化订单管理器"""
        try:
            # 订阅事件
            if self.event_bus:
                await self.event_bus.subscribe(
                    "market_data_update", self._handle_market_update
                )
                await self.event_bus.subscribe("order_fill", self._handle_order_fill)
                await self.event_bus.subscribe(
                    "order_cancel", self._handle_order_cancel
                )
                await self.event_bus.subscribe(
                    "risk_limit_breach", self._handle_risk_breach
                )

            # 初始化队列
            self.order_queue = asyncio.Queue(maxsize=1000)
            self.priority_queue = asyncio.PriorityQueue(maxsize=1000)

            if self.logger:
                await self.logger.info("OrderManager initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"OrderManager initialization failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动订单管理器"""
        try:
            # 启动执行任务
            self._executor_task = asyncio.create_task(self._order_executor_loop())
            self._monitor_task = asyncio.create_task(self._order_monitor_loop())

            if self.logger:
                await self.logger.info("OrderManager started")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"OrderManager start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止订单管理器"""
        try:
            # 停止执行任务
            if self._executor_task and not self._executor_task.done():
                self._executor_task.cancel()
                try:
                    await self._executor_task
                except asyncio.CancelledError:
                    pass

            if self._monitor_task and not self._monitor_task.done():
                self._monitor_task.cancel()
                try:
                    await self._monitor_task
                except asyncio.CancelledError:
                    pass

            # 取消所有活跃订单
            await self._cancel_all_active_orders()

            if self.logger:
                await self.logger.info("OrderManager stopped")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"OrderManager stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查"""
        try:
            # 检查执行任务状态
            if not self._executor_task or self._executor_task.done():
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message="Order executor task not running",
                )

            # 检查队列状态
            queue_size = self.order_queue.qsize()
            if queue_size > 500:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"Order queue overloaded: {queue_size} orders",
                )

            # 检查活跃订单数量
            active_count = len(self.active_orders)
            if active_count > self._max_concurrent_orders:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"Too many active orders: {active_count}",
                )

            # 检查成功率
            if self.stats.success_rate < 0.95 and self.stats.total_orders > 10:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"Low success rate: {self.stats.success_rate:.2%}",
                )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message=f"Order system operational, {active_count} active orders",
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    # 核心订单方法

    async def submit_order(
        self,
        symbol: str,
        side: OrderSide,
        order_type: OrderType,
        quantity: Decimal,
        price: Optional[Decimal] = None,
        **kwargs,
    ) -> str:
        """提交订单"""
        try:
            # 生成订单ID
            order_id = str(uuid.uuid4())

            # 创建订单对象
            order = Order(
                order_id=order_id,
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=price,
                priority=kwargs.get("priority", OrderPriority.NORMAL),
                time_in_force=kwargs.get(
                    "time_in_force", self.order_settings["default_time_in_force"]
                ),
                reduce_only=kwargs.get("reduce_only", False),
                post_only=kwargs.get("post_only", False),
                strategy_id=kwargs.get("strategy_id"),
                tags=kwargs.get("tags", {}),
            )

            # 订单验证
            validation_result = await self._validate_order(order)
            if not validation_result["valid"]:
                order.status = OrderStatus.REJECTED
                order.last_error = validation_result["error"]
                self.order_history.append(order)

                if self.logger:
                    await self.logger.warning(
                        f"Order rejected: {validation_result['error']}"
                    )
                return order_id

            # 添加到活跃订单
            self.active_orders[order_id] = order

            # 加入执行队列
            if order.priority == OrderPriority.URGENT:
                await self.priority_queue.put((1, order))
            elif order.priority == OrderPriority.HIGH:
                await self.priority_queue.put((2, order))
            else:
                await self.order_queue.put(order)

            if self.logger:
                await self.logger.info(
                    f"Order submitted: {order_id} - {symbol} {side.value} {quantity}"
                )

            # 发布事件
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type="order_submitted",
                        data={
                            "order_id": order_id,
                            "symbol": symbol,
                            "side": side.value,
                            "quantity": float(quantity),
                            "order_type": order_type.value,
                        },
                    )
                )

            return order_id

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order submission failed: {e}")
            return ""

    async def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        try:
            if order_id not in self.active_orders:
                if self.logger:
                    await self.logger.warning(
                        f"Order not found for cancellation: {order_id}"
                    )
                return False

            order = self.active_orders[order_id]

            # 检查订单状态
            if order.status in [
                OrderStatus.FILLED,
                OrderStatus.CANCELLED,
                OrderStatus.REJECTED,
            ]:
                if self.logger:
                    await self.logger.warning(
                        f"Cannot cancel order in status: {order.status}"
                    )
                return False

            # 执行取消
            cancel_success = await self._execute_cancel(order)

            if cancel_success:
                order.status = OrderStatus.CANCELLED
                self._move_to_history(order_id)

                if self.logger:
                    await self.logger.info(f"Order cancelled: {order_id}")

                # 发布事件
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type="order_cancelled", data={"order_id": order_id}
                        )
                    )

                return True
            else:
                if self.logger:
                    await self.logger.error(f"Failed to cancel order: {order_id}")
                return False

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order cancellation failed: {e}")
            return False

    async def _validate_order(self, order: Order) -> Dict[str, Any]:
        """验证订单"""
        try:
            # 基础验证
            if order.quantity <= 0:
                return {"valid": False, "error": "Invalid quantity"}

            if order.quantity < self.order_settings["min_order_size"]:
                return {"valid": False, "error": "Quantity below minimum"}

            if order.quantity > self.order_settings["max_order_size"]:
                return {"valid": False, "error": "Quantity above maximum"}

            # 价格验证
            if (
                order.order_type in [OrderType.LIMIT, OrderType.STOP_LIMIT]
                and order.price is None
            ):
                return {"valid": False, "error": "Price required for limit orders"}

            if (
                order.order_type in [OrderType.STOP, OrderType.STOP_LIMIT]
                and order.stop_price is None
            ):
                return {"valid": False, "error": "Stop price required for stop orders"}

            # 流控检查
            if len(self.active_orders) >= self._max_concurrent_orders:
                return {"valid": False, "error": "Too many active orders"}

            # 风险检查
            risk_check = await self._check_order_risk(order)
            if not risk_check["valid"]:
                return risk_check

            return {"valid": True, "error": None}

        except Exception as e:
            return {"valid": False, "error": f"Validation error: {e}"}

    async def _check_order_risk(self, order: Order) -> Dict[str, Any]:
        """检查订单风险"""
        try:
            # 通过事件总线请求风险检查
            if self.event_bus:
                pass

            # 简化的风险检查
            return {"valid": True, "error": None}

        except Exception as e:
            return {"valid": False, "error": f"Risk check failed: {e}"}

    async def _order_executor_loop(self):
        """订单执行循环"""
        while True:
            try:
                # 优先处理高优先级订单
                order = None
                try:
                    # 尝试从优先级队列获取
                    priority, order = await asyncio.wait_for(
                        self.priority_queue.get(), timeout=0.1
                    )
                except asyncio.TimeoutError:
                    # 从普通队列获取
                    try:
                        order = await asyncio.wait_for(
                            self.order_queue.get(), timeout=1.0
                        )
                    except asyncio.TimeoutError:
                        continue

                if order:
                    await self._execute_order(order)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Order executor error: {e}")
                await asyncio.sleep(1)

    async def _execute_order(self, order: Order):
        """执行单个订单"""
        # IBIT/IBKR 路径的结构化 pretrade 校验
        try:
            # 优先使用结构化元数据，向后兼容 tags
            meta = order.ibit_meta
            if meta is None and order.symbol.startswith("IBIT ") and (" C" in order.symbol or " P" in order.symbol):
                # 向后兼容：从 tags 构造元数据，传入 symbol 用于合约解析
                tags_with_symbol = (order.tags or {}).copy()
                tags_with_symbol["symbol"] = order.symbol
                meta = IBITOptionOrderMeta.from_tags(tags_with_symbol)

            if meta is not None:
                # 验证元数据完整性
                valid, reason = meta.validate_for_pretrade()
                if not valid:
                    raise RuntimeError(f"Invalid IBIT meta: {reason}")

                # 流动性校验
                liq_check = self.pretrade_filters.liquidity(
                    meta.spread_bps, meta.open_interest, meta.volume
                )
                if not (liq_check.spread_bps_ok and liq_check.oi_ok and liq_check.vol_ok):
                    raise RuntimeError(f"Liquidity check failed: {liq_check.reason}")

                # 动作特定校验
                if meta.action == "SELL_PUT_CSP":
                    if meta.contract is None or meta.buying_power is None:
                        raise RuntimeError("CSP missing contract or buying power")

                    csp_check = self.pretrade_filters.csp_buying_power(
                        meta.contract.strike,
                        meta.contract.multiplier,
                        int(order.quantity),
                        meta.buying_power
                    )
                    if not csp_check.ok:
                        raise RuntimeError(f"CSP check failed: {csp_check.reason}")

                elif meta.action == "LONG_CALL":
                    if not self.pretrade_filters.long_call_theta_guard(
                        meta.days_to_expiry or 0, meta.iv
                    ):
                        raise RuntimeError("Theta guard blocked long call")

        except Exception as pe:
            # 失败即拒绝该订单
            order.status = OrderStatus.REJECTED
            order.last_error = f"pretrade_reject: {pe}"
            self._move_to_history(order.order_id)
            return

        try:
            # 更新状态
            order.status = OrderStatus.SUBMITTED
            order.submitted_at = datetime.now(timezone.utc)

            # 智能路由
            exchange = await self._route_order(order)

            # 执行订单
            execution_result = await self._send_to_exchange(order, exchange)

            if execution_result["success"]:
                order.exchange_order_id = execution_result.get("exchange_order_id")

                if self.logger:
                    await self.logger.info(
                        f"Order executed: {order.order_id} on {exchange}"
                    )

                # 发布执行事件
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type="order_executed",
                            data={
                                "order_id": order.order_id,
                                "exchange": exchange,
                                "exchange_order_id": order.exchange_order_id,
                            },
                        )
                    )
            else:
                # 执行失败，处理重试
                await self._handle_execution_failure(
                    order, execution_result.get("error")
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order execution failed: {e}")
            await self._handle_execution_failure(order, str(e))

    async def _route_order(self, order: Order) -> str:
        """智能订单路由"""
        try:
            # 根据交易对类型路由
            if "BTC-" in order.symbol and (
                "-C" in order.symbol or "-P" in order.symbol
            ):
                # 期权订单
                return self.routing_rules["option_exchange"]
            else:
                # 现货订单
                return self.routing_rules["spot_exchange"]

        except Exception:
            return self.routing_rules["default_exchange"]

    async def _send_to_exchange(self, order: Order, exchange: str) -> Dict[str, Any]:
        """发送订单到交易所"""
        try:
            # 模拟交易所API调用
            # 实际实现中需要调用具体的交易所API

            # 模拟延迟
            await asyncio.sleep(0.1)

            # 模拟成功响应
            exchange_order_id = f"{exchange}_{order.order_id[:8]}"

            return {
                "success": True,
                "exchange_order_id": exchange_order_id,
                "message": "Order submitted successfully",
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Order submission failed",
            }

    async def _execute_cancel(self, order: Order) -> bool:
        """执行订单取消"""
        try:
            # 模拟取消API调用
            await asyncio.sleep(0.05)
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Cancel execution failed: {e}")
            return False

    async def _handle_execution_failure(self, order: Order, error: str):
        """处理执行失败"""
        try:
            order.retry_count += 1
            order.last_error = error

            if order.retry_count < order.max_retries:
                # 重新加入队列
                await asyncio.sleep(2**order.retry_count)  # 指数退避
                await self.order_queue.put(order)

                if self.logger:
                    await self.logger.warning(
                        f"Retrying order {order.order_id}, attempt {order.retry_count}"
                    )
            else:
                # 超过重试次数，标记为失败
                order.status = OrderStatus.FAILED
                self._move_to_history(order.order_id)

                if self.logger:
                    await self.logger.error(
                        f"Order failed after {order.max_retries} retries: {order.order_id}"
                    )

                # 发布失败事件
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type="order_failed",
                            data={
                                "order_id": order.order_id,
                                "error": error,
                                "retry_count": order.retry_count,
                            },
                        )
                    )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failure handling error: {e}")

    async def _order_monitor_loop(self):
        """订单监控循环"""
        while True:
            try:
                await self._monitor_active_orders()
                await self._update_statistics()
                await self._cleanup_old_data()
                await asyncio.sleep(5)  # 5秒监控间隔

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Order monitor error: {e}")
                await asyncio.sleep(10)

    async def _monitor_active_orders(self):
        """监控活跃订单"""
        try:
            current_time = datetime.now(timezone.utc)
            timeout_orders = []

            for order_id, order in self.active_orders.items():
                # 检查超时订单
                if order.submitted_at:
                    elapsed = (current_time - order.submitted_at).total_seconds()
                    if elapsed > self.order_settings["execution_timeout"]:
                        timeout_orders.append(order_id)

            # 处理超时订单
            for order_id in timeout_orders:
                await self.cancel_order(order_id)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order monitoring failed: {e}")

    def _move_to_history(self, order_id: str):
        """移动订单到历史记录"""
        try:
            if order_id in self.active_orders:
                order = self.active_orders.pop(order_id)
                self.order_history.append(order)

                # 限制历史记录大小
                if len(self.order_history) > self._max_history_size:
                    self.order_history = self.order_history[-self._max_history_size :]

        except Exception as e:
            if self.logger:
                asyncio.create_task(self.logger.error(f"Move to history failed: {e}"))

    async def _update_statistics(self):
        """更新统计信息"""
        try:
            total_orders = len(self.order_history)
            if total_orders == 0:
                return

            # 计算各种统计
            filled_orders = sum(
                1 for order in self.order_history if order.status == OrderStatus.FILLED
            )
            cancelled_orders = sum(
                1
                for order in self.order_history
                if order.status == OrderStatus.CANCELLED
            )
            rejected_orders = sum(
                1
                for order in self.order_history
                if order.status == OrderStatus.REJECTED
            )

            # 计算成功率
            success_rate = filled_orders / total_orders if total_orders > 0 else 0.0

            # 计算平均成交时间
            fill_times = []
            for order in self.order_history:
                if (
                    order.status == OrderStatus.FILLED
                    and order.submitted_at
                    and order.filled_at
                ):
                    fill_time = (order.filled_at - order.submitted_at).total_seconds()
                    fill_times.append(fill_time)

            avg_fill_time = sum(fill_times) / len(fill_times) if fill_times else 0.0

            # 计算总交易量
            total_volume = sum(order.filled_quantity for order in self.order_history)

            # 更新统计
            self.stats = OrderStats(
                total_orders=total_orders,
                filled_orders=filled_orders,
                cancelled_orders=cancelled_orders,
                rejected_orders=rejected_orders,
                avg_fill_time=avg_fill_time,
                success_rate=success_rate,
                total_volume=total_volume,
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Statistics update failed: {e}")

    async def _cleanup_old_data(self):
        """清理旧数据"""
        try:
            # 清理执行记录
            if len(self.executions) > 5000:
                self.executions = self.executions[-5000:]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Data cleanup failed: {e}")

    # 事件处理方法

    async def _handle_market_update(self, event: BaseEvent):
        """处理市场数据更新"""
        try:
            # 检查条件订单
            await self._check_conditional_orders(event.data)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Market update handling failed: {e}")

    async def _handle_order_fill(self, event: BaseEvent):
        """处理订单成交"""
        try:
            data = event.data
            order_id = data.get("order_id")

            if order_id in self.active_orders:
                order = self.active_orders[order_id]

                # 更新成交信息
                fill_quantity = Decimal(str(data.get("quantity", 0)))
                fill_price = Decimal(str(data.get("price", 0)))

                order.filled_quantity += fill_quantity
                order.avg_fill_price = fill_price  # 简化处理

                # 记录执行
                execution = OrderExecution(
                    execution_id=str(uuid.uuid4()),
                    order_id=order_id,
                    quantity=fill_quantity,
                    price=fill_price,
                    timestamp=datetime.now(timezone.utc),
                    commission=Decimal(str(data.get("commission", 0))),
                )
                self.executions.append(execution)

                # 检查是否完全成交
                if order.filled_quantity >= order.quantity:
                    order.status = OrderStatus.FILLED
                    order.filled_at = datetime.now(timezone.utc)
                    self._move_to_history(order_id)
                else:
                    order.status = OrderStatus.PARTIALLY_FILLED

                if self.logger:
                    await self.logger.info(
                        f"Order fill: {order_id} - {fill_quantity} @ {fill_price}"
                    )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order fill handling failed: {e}")

    async def _handle_order_cancel(self, event: BaseEvent):
        """处理订单取消"""
        try:
            order_id = event.data.get("order_id")
            if order_id in self.active_orders:
                await self.cancel_order(order_id)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order cancel handling failed: {e}")

    async def _handle_risk_breach(self, event: BaseEvent):
        """处理风险违规"""
        try:
            # 暂停新订单提交
            if event.data.get("severity") == "critical":
                await self._cancel_all_active_orders()

                if self.logger:
                    await self.logger.warning(
                        "All orders cancelled due to critical risk breach"
                    )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk breach handling failed: {e}")

    async def _check_conditional_orders(self, market_data: Dict[str, Any]):
        """检查条件订单"""
        try:
            # 简化的条件订单检查
            # 实际实现中需要更复杂的条件评估逻辑
            pass

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Conditional order check failed: {e}")

    async def _cancel_all_active_orders(self):
        """取消所有活跃订单"""
        try:
            order_ids = list(self.active_orders.keys())
            for order_id in order_ids:
                await self.cancel_order(order_id)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Cancel all orders failed: {e}")

    # 公共接口方法

    async def get_order_status(self, order_id: str) -> Optional[Dict[str, Any]]:
        """获取订单状态"""
        try:
            # 检查活跃订单
            if order_id in self.active_orders:
                order = self.active_orders[order_id]
                return self._order_to_dict(order)

            # 检查历史订单
            for order in self.order_history:
                if order.order_id == order_id:
                    return self._order_to_dict(order)

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get order status failed: {e}")
            return None

    def _order_to_dict(self, order: Order) -> Dict[str, Any]:
        """订单对象转字典"""
        return {
            "order_id": order.order_id,
            "symbol": order.symbol,
            "side": order.side.value,
            "order_type": order.order_type.value,
            "quantity": float(order.quantity),
            "price": float(order.price) if order.price else None,
            "status": order.status.value,
            "filled_quantity": float(order.filled_quantity),
            "avg_fill_price": float(order.avg_fill_price)
            if order.avg_fill_price
            else None,
            "created_at": order.created_at.isoformat(),
            "submitted_at": order.submitted_at.isoformat()
            if order.submitted_at
            else None,
            "filled_at": order.filled_at.isoformat() if order.filled_at else None,
            "retry_count": order.retry_count,
            "last_error": order.last_error,
        }

    async def get_order_statistics(self) -> Dict[str, Any]:
        """获取订单统计"""
        try:
            return {
                "total_orders": self.stats.total_orders,
                "filled_orders": self.stats.filled_orders,
                "cancelled_orders": self.stats.cancelled_orders,
                "rejected_orders": self.stats.rejected_orders,
                "success_rate": round(self.stats.success_rate, 4),
                "avg_fill_time": round(self.stats.avg_fill_time, 2),
                "total_volume": float(self.stats.total_volume),
                "active_orders": len(self.active_orders),
                "queue_size": self.order_queue.qsize(),
                "priority_queue_size": self.priority_queue.qsize(),
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get statistics failed: {e}")
            return {"status": "error", "message": str(e)}

    async def get_active_orders(self) -> List[Dict[str, Any]]:
        """获取活跃订单"""
        try:
            return [self._order_to_dict(order) for order in self.active_orders.values()]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get active orders failed: {e}")
            return []

    def set_event_bus(self, event_bus: EventBus):
        """设置事件总线"""
        self.event_bus = event_bus

    def set_cache_manager(self, cache_manager: CacheManager):
        """设置缓存管理器"""
        self.cache_manager = cache_manager
