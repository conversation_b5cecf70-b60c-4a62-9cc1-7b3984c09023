"""
到期管理器

负责期权到期跟踪、风险评估、行权概率计算和自动化到期处理
"""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

from ..core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from ..core.event_bus import BaseEvent, EventBus
from ..data.cache_manager import CacheManager


class ExpiryAction(Enum):
    """到期处理动作"""

    HOLD = "hold"  # 持有到期
    CLOSE = "close"  # 平仓
    ROLL = "roll"  # 展期
    EXERCISE = "exercise"  # 行权
    ASSIGN = "assign"  # 被指派


class ExpiryRisk(Enum):
    """到期风险级别"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ExpiryPosition:
    """到期仓位"""

    symbol: str
    option_type: str  # "call" or "put"
    strike: Decimal
    expiry: datetime
    size: Decimal
    mark_price: Decimal
    underlying_price: Decimal
    intrinsic_value: Decimal
    time_value: Decimal
    delta: Decimal
    gamma: Decimal
    exercise_probability: float = 0.0
    days_to_expiry: int = 0
    risk_level: ExpiryRisk = ExpiryRisk.LOW


@dataclass
class ExpiryAlert:
    """到期告警"""

    alert_id: str
    symbol: str
    expiry: datetime
    days_to_expiry: int
    risk_level: ExpiryRisk
    message: str
    suggested_action: ExpiryAction
    exercise_probability: float
    potential_loss: Decimal
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


@dataclass
class ExpiryPlan:
    """到期处理计划"""

    symbol: str
    current_action: ExpiryAction
    backup_action: ExpiryAction
    execution_time: datetime
    conditions: Dict[str, Any]
    risk_assessment: str
    expected_outcome: Dict[str, Any]


class ExpiryManager(BaseComponent):
    """
    到期管理器

    功能特性：
    - 期权到期跟踪和监控
    - 行权概率计算
    - 到期风险评估
    - 自动化到期处理
    - 展期策略建议
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("ExpiryManager", config)

        # 组件依赖
        self.event_bus: Optional[EventBus] = None
        self.cache_manager: Optional[CacheManager] = None

        # 到期配置
        self._load_expiry_config()

        # 到期监控状态
        self.expiry_positions: Dict[str, ExpiryPosition] = {}
        self.expiry_alerts: List[ExpiryAlert] = []
        self.expiry_plans: Dict[str, ExpiryPlan] = {}

        # 监控任务
        self._monitoring_task: Optional[asyncio.Task] = None
        self._monitoring_interval = 3600  # 1小时检查间隔

        # 到期处理配置
        self.auto_close_threshold = 1  # 1天内自动平仓
        self.exercise_threshold = 0.7  # 70%行权概率阈值
        self.roll_threshold = 7  # 7天内考虑展期
        # IBIT/IBKR American 提前指派阈值（可配置）
        self.american_settings = {
            "enable": True,
            "extrinsic_roll_threshold": Decimal("0.05"),  # 外在价值<阈值时考虑展期/平仓
            "deep_itm_moneyness": Decimal("0.05"),        # 深 ITM 判定阈值（5%）
        }


    def _load_expiry_config(self):
        """加载到期配置"""
        expiry_config = self.config.get("expiry", {}) if self.config else {}

        # 风险阈值配置
        self.risk_thresholds = {
            "critical_days": expiry_config.get("critical_days", 1),  # 1天内为关键
            "high_risk_days": expiry_config.get("high_risk_days", 3),  # 3天内为高风险
            "medium_risk_days": expiry_config.get(
                "medium_risk_days", 7
            ),  # 7天内为中风险
            "exercise_prob_threshold": expiry_config.get(
                "exercise_prob_threshold", 0.5
            ),  # 50%行权概率
            "gamma_risk_threshold": expiry_config.get(
                "gamma_risk_threshold", 0.1
            ),  # Gamma风险阈值
        }

        # 自动处理配置
        self.auto_actions = {
            "enable_auto_close": expiry_config.get("enable_auto_close", True),
            "enable_auto_roll": expiry_config.get("enable_auto_roll", False),
            "enable_exercise_alert": expiry_config.get("enable_exercise_alert", True),
            "min_time_value": Decimal(
                str(expiry_config.get("min_time_value", "0.01"))
            ),  # 最小时间价值
        }

    async def _initialize_impl(self) -> bool:
        """初始化到期管理器"""
        try:
            if self.logger:
                await self.logger.info("ExpiryManager initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"ExpiryManager initialization failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动到期监控"""
        try:
            # 启动监控任务
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())

            if self.logger:
                await self.logger.info("ExpiryManager monitoring started")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"ExpiryManager start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止到期监控"""
        try:
            # 停止监控任务
            if self._monitoring_task and not self._monitoring_task.done():
                self._monitoring_task.cancel()
                try:
                    await self._monitoring_task
                except asyncio.CancelledError:
                    pass

            if self.logger:
                await self.logger.info("ExpiryManager stopped")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"ExpiryManager stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查"""
        try:
            # 检查监控任务状态
            if not self._monitoring_task or self._monitoring_task.done():
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message="Expiry monitoring task not running",
                )

            # 检查是否有关键到期风险
            critical_positions = [
                pos
                for pos in self.expiry_positions.values()
                if pos.risk_level == ExpiryRisk.CRITICAL
            ]

            if critical_positions:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"Found {len(critical_positions)} critical expiry positions",
                )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY, message="Expiry monitoring operational"
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    async def _monitoring_loop(self):
        """到期监控循环"""
        while True:
            try:
                await self._scan_expiry_positions()
                await self._assess_expiry_risks()
                await self._execute_auto_actions()
                await asyncio.sleep(self._monitoring_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Expiry monitoring error: {e}")
                await asyncio.sleep(60)  # 错误后等待1分钟

    async def _scan_expiry_positions(self):
        """扫描到期仓位"""
        try:
            if not self.cache_manager:
                return

            # 获取当前仓位
            positions_data = await self.cache_manager.get("positions")
            if not positions_data:
                return

            current_time = datetime.now(timezone.utc)
            self.expiry_positions.clear()

            for symbol, position_data in positions_data.items():
                if isinstance(position_data, dict) and "option" in symbol.lower():
                    # 解析期权信息
                    expiry_position = await self._parse_expiry_position(
                        symbol, position_data, current_time
                    )
                    if expiry_position:
                        self.expiry_positions[symbol] = expiry_position

            if self.logger:
                await self.logger.info(
                    f"Scanned {len(self.expiry_positions)} expiry positions"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiry position scan failed: {e}")

    async def _parse_expiry_position(
        self, symbol: str, position_data: Dict[str, Any], current_time: datetime
    ) -> Optional[ExpiryPosition]:
        """解析到期仓位"""
        try:
            # 解析基本信息
            size = Decimal(str(position_data.get("size", 0)))
            mark_price = Decimal(str(position_data.get("mark_price", 0)))

            # 从symbol解析期权信息
            parts = symbol.split("-")
            if len(parts) < 4:
                return None

            strike = Decimal(parts[2])
            option_type = "call" if parts[3] == "C" else "put"

            # 解析到期日期（需要根据实际格式调整）
            expiry_str = parts[1]  # 例如: 25DEC21
            expiry = self._parse_expiry_date(expiry_str)
            if not expiry:
                return None

            # 计算到期天数
            days_to_expiry = (expiry - current_time).days

            # 获取标的价格
            underlying_price = await self._get_underlying_price()

            # 计算内在价值和时间价值
            if option_type == "call":
                intrinsic_value = max(Decimal("0"), underlying_price - strike)
            else:  # put
                intrinsic_value = max(Decimal("0"), strike - underlying_price)

            time_value = mark_price - intrinsic_value

            # 获取Greeks
            greeks_data = await self.cache_manager.get(f"greeks:{symbol}")
            delta = (
                Decimal(str(greeks_data.get("delta", 0)))
                if greeks_data
                else Decimal("0")
            )
            gamma = (
                Decimal(str(greeks_data.get("gamma", 0)))
                if greeks_data
                else Decimal("0")
            )

            # 计算行权概率
            exercise_prob = await self._calculate_exercise_probability(
                option_type, strike, underlying_price, days_to_expiry
            )

            # 评估风险级别
            risk_level = self._assess_position_risk(
                days_to_expiry, exercise_prob, gamma
            )

            return ExpiryPosition(
                symbol=symbol,
                option_type=option_type,
                strike=strike,
                expiry=expiry,
                size=size,
                mark_price=mark_price,
                underlying_price=underlying_price,
                intrinsic_value=intrinsic_value,
                time_value=time_value,
                delta=delta,
                gamma=gamma,
                exercise_probability=exercise_prob,
                days_to_expiry=days_to_expiry,
                risk_level=risk_level,
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiry position parsing failed: {e}")
            return None

    def _parse_expiry_date(self, expiry_str: str) -> Optional[datetime]:
        """解析到期日期"""
        try:
            # 示例格式: 25DEC21
            # 需要根据实际格式调整
            from datetime import datetime

            # 简化解析，实际需要更复杂的逻辑
            if len(expiry_str) >= 7:
                day = int(expiry_str[:2])
                month_str = expiry_str[2:5]
                year = int("20" + expiry_str[5:7])

                month_map = {
                    "JAN": 1,
                    "FEB": 2,
                    "MAR": 3,
                    "APR": 4,
                    "MAY": 5,
                    "JUN": 6,
                    "JUL": 7,
                    "AUG": 8,
                    "SEP": 9,
                    "OCT": 10,
                    "NOV": 11,
                    "DEC": 12,
                }

                month = month_map.get(month_str)
                if month:
                    return datetime(
                        year, month, day, 8, 0, 0, tzinfo=timezone.utc
                    )  # 8:00 UTC到期

            return None

        except Exception:
            return None

    async def _get_underlying_price(self) -> Decimal:
        """获取标的价格"""
        try:
            if self.cache_manager:
                price_data = await self.cache_manager.get("btc_price")
                if price_data:
                    return Decimal(str(price_data.get("price", 50000)))

            return Decimal("50000")  # 默认价格

        except Exception:
            return Decimal("50000")

    async def _calculate_exercise_probability(
        self,
        option_type: str,
        strike: Decimal,
        underlying_price: Decimal,
        days_to_expiry: int,
    ) -> float:
        """计算行权概率"""
        try:
            # 简化的行权概率计算
            if days_to_expiry <= 0:
                # 到期日当天，基于内在价值
                if option_type == "call":
                    return 1.0 if underlying_price > strike else 0.0
                else:  # put
                    return 1.0 if underlying_price < strike else 0.0

            # 基于Delta近似行权概率
            greeks_data = await self.cache_manager.get(f"greeks:{option_type}")
            if greeks_data:
                delta = abs(float(greeks_data.get("delta", 0)))
                return min(1.0, max(0.0, delta))

            # 简化计算：基于价内程度
            if option_type == "call":
                moneyness = float(underlying_price / strike)
                if moneyness > 1.05:  # 5%价内
                    return 0.8
                elif moneyness > 1.0:
                    return 0.5
                else:
                    return 0.1
            else:  # put
                moneyness = float(strike / underlying_price)
                if moneyness > 1.05:  # 5%价内
                    return 0.8
                elif moneyness > 1.0:
                    return 0.5
                else:
                    return 0.1

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Exercise probability calculation failed: {e}")
            return 0.0

    def _assess_position_risk(
        self, days_to_expiry: int, exercise_prob: float, gamma: Decimal
    ) -> ExpiryRisk:
        """评估仓位风险级别"""
        try:
            # 基于到期天数的基础风险
            if days_to_expiry <= self.risk_thresholds["critical_days"]:
                base_risk = ExpiryRisk.CRITICAL
            elif days_to_expiry <= self.risk_thresholds["high_risk_days"]:
                base_risk = ExpiryRisk.HIGH
            elif days_to_expiry <= self.risk_thresholds["medium_risk_days"]:
                base_risk = ExpiryRisk.MEDIUM
            else:
                base_risk = ExpiryRisk.LOW

            # 基于行权概率调整风险
            if exercise_prob > self.risk_thresholds["exercise_prob_threshold"]:
                if base_risk == ExpiryRisk.LOW:
                    base_risk = ExpiryRisk.MEDIUM
                elif base_risk == ExpiryRisk.MEDIUM:
                    base_risk = ExpiryRisk.HIGH

            # 基于Gamma风险调整
            if abs(gamma) > Decimal(str(self.risk_thresholds["gamma_risk_threshold"])):
                if base_risk == ExpiryRisk.LOW:
                    base_risk = ExpiryRisk.MEDIUM
                elif base_risk == ExpiryRisk.MEDIUM:
                    base_risk = ExpiryRisk.HIGH
                elif base_risk == ExpiryRisk.HIGH:
                    base_risk = ExpiryRisk.CRITICAL

            return base_risk

        except Exception:
            return ExpiryRisk.MEDIUM

    async def _assess_expiry_risks(self):
        """评估到期风险并生成告警"""
        try:
            current_alerts = []

            for symbol, position in self.expiry_positions.items():
                # 生成风险告警
                if position.risk_level in [ExpiryRisk.HIGH, ExpiryRisk.CRITICAL]:
                    alert = await self._create_expiry_alert(position)
                    if alert:
                        current_alerts.append(alert)

                # 创建处理计划
                if position.days_to_expiry <= self.roll_threshold:
                    plan = await self._create_expiry_plan(position)
                    if plan:
                        self.expiry_plans[symbol] = plan

            # 更新告警列表
            self.expiry_alerts = current_alerts

            # 发布风险告警
            for alert in current_alerts:
                await self._publish_expiry_alert(alert)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiry risk assessment failed: {e}")

    async def _create_expiry_alert(
        self, position: ExpiryPosition
    ) -> Optional[ExpiryAlert]:
        """创建到期告警"""
        try:
            # 确定建议动作
            suggested_action = await self._determine_suggested_action(position)

            # 计算潜在损失
            potential_loss = await self._calculate_potential_loss(position)

            # 生成告警消息
            message = self._generate_alert_message(position, suggested_action)

            alert = ExpiryAlert(
                alert_id=f"expiry_{position.symbol}_{int(datetime.now().timestamp())}",
                symbol=position.symbol,
                expiry=position.expiry,
                days_to_expiry=position.days_to_expiry,
                risk_level=position.risk_level,
                message=message,
                suggested_action=suggested_action,
                exercise_probability=position.exercise_probability,
                potential_loss=potential_loss,
            )

            return alert

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiry alert creation failed: {e}")
            return None

    async def _american_early_assignment_guard(self, position: ExpiryPosition) -> Optional[ExpiryAction]:
        """美式提前指派保护：
        - 当 Put 深 ITM 且外在价值接近 0 时，建议 ROLL/CLOSE（避免不利提前指派）
        - 仅在 american_settings.enable 为真时生效
        """
        try:
            if not self.american_settings.get("enable", False):
                return None
            # 仅在接近到期时启用（例如 <= roll_threshold 天）
            if position.days_to_expiry is not None and position.days_to_expiry > self.roll_threshold:
                return None
            # 仅对 PUT 进行提前指派风险检查
            if position.option_type != "put":
                return None
            # 估算 moneyness：标的价与行权价偏离
            # 已在 _evaluate_position 中计算 intrinsic/time_value
            extrinsic = position.time_value
            if extrinsic <= self.american_settings["extrinsic_roll_threshold"]:
                return ExpiryAction.ROLL
            return None
        except Exception:
            return None

    async def _determine_suggested_action(
        self, position: ExpiryPosition
    ) -> ExpiryAction:
        """确定建议的处理动作"""
        try:
            # 关键到期（1天内）
            if position.days_to_expiry <= 1:
                if position.exercise_probability > 0.8:
                    return (
                        ExpiryAction.EXERCISE
                        if position.size > 0
                        else ExpiryAction.ASSIGN
                    )
                elif position.time_value < self.auto_actions["min_time_value"]:
                    # 美式提前指派保护（仅对 PUT 生效）
                    guard = await self._american_early_assignment_guard(position)
                    if guard:
                        return guard
                    return ExpiryAction.HOLD  # 时间价值很小，持有到期
                else:
                    return ExpiryAction.CLOSE  # 还有时间价值，建议平仓

            # 高风险到期（3天内）
            elif position.days_to_expiry <= 3:
                if position.exercise_probability > 0.6:
                    return ExpiryAction.CLOSE  # 高行权概率，建议平仓
                elif abs(position.gamma) > Decimal("0.1"):
                    return ExpiryAction.CLOSE  # 高Gamma风险，建议平仓
                else:
                    return ExpiryAction.ROLL  # 考虑展期

            # 中等风险到期（7天内）
            elif position.days_to_expiry <= 7:
                if position.time_value / position.mark_price < Decimal("0.1"):
                    return ExpiryAction.CLOSE  # 时间价值占比低，建议平仓
                else:
                    return ExpiryAction.ROLL  # 考虑展期

            else:
                return ExpiryAction.HOLD  # 距离到期较远，继续持有

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Action determination failed: {e}")
            return ExpiryAction.HOLD

    async def _calculate_potential_loss(self, position: ExpiryPosition) -> Decimal:
        """计算潜在损失"""
        try:
            # 最坏情况下的损失估算
            if position.size > 0:  # 多头仓位
                # 期权归零的损失
                return position.mark_price * position.size
            else:  # 空头仓位
                # 被行权的最大损失
                if position.option_type == "call":
                    # 看涨期权空头的最大损失理论上无限
                    max_reasonable_price = position.underlying_price * Decimal(
                        "2"
                    )  # 假设最大涨到2倍
                    max_loss = (max_reasonable_price - position.strike) * abs(
                        position.size
                    )
                else:  # put
                    # 看跌期权空头的最大损失是行权价
                    max_loss = position.strike * abs(position.size)

                return max(
                    Decimal("0"), max_loss - position.mark_price * abs(position.size)
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Potential loss calculation failed: {e}")
            return Decimal("0")

    def _generate_alert_message(
        self, position: ExpiryPosition, action: ExpiryAction
    ) -> str:
        """生成告警消息"""
        try:
            risk_desc = {
                ExpiryRisk.CRITICAL: "关键",
                ExpiryRisk.HIGH: "高",
                ExpiryRisk.MEDIUM: "中等",
                ExpiryRisk.LOW: "低",
            }

            action_desc = {
                ExpiryAction.CLOSE: "建议平仓",
                ExpiryAction.ROLL: "建议展期",
                ExpiryAction.EXERCISE: "准备行权",
                ExpiryAction.ASSIGN: "准备被指派",
                ExpiryAction.HOLD: "继续持有",
            }

            return (
                f"{position.symbol} {risk_desc[position.risk_level]}到期风险: "
                f"{position.days_to_expiry}天到期, "
                f"行权概率{position.exercise_probability:.1%}, "
                f"{action_desc[action]}"
            )

        except Exception:
            return f"{position.symbol} 到期风险告警"

    async def _create_expiry_plan(
        self, position: ExpiryPosition
    ) -> Optional[ExpiryPlan]:
        """创建到期处理计划"""
        try:
            suggested_action = await self._determine_suggested_action(position)

            # 确定执行时间
            if position.days_to_expiry <= 1:
                execution_time = position.expiry - timedelta(hours=2)  # 到期前2小时
            else:
                execution_time = position.expiry - timedelta(days=1)  # 到期前1天

            # 设置执行条件
            conditions = {
                "min_time_value": float(self.auto_actions["min_time_value"]),
                "max_exercise_prob": 0.8,
                "min_liquidity": 0.3,
            }

            # 风险评估
            risk_assessment = f"风险级别: {position.risk_level.value}, 行权概率: {position.exercise_probability:.1%}"

            # 预期结果
            expected_outcome = {
                "action": suggested_action.value,
                "estimated_pnl": 0.0,  # 需要更详细的计算
                "risk_reduction": True,
            }

            plan = ExpiryPlan(
                symbol=position.symbol,
                current_action=suggested_action,
                backup_action=ExpiryAction.CLOSE,  # 备用动作总是平仓
                execution_time=execution_time,
                conditions=conditions,
                risk_assessment=risk_assessment,
                expected_outcome=expected_outcome,
            )

            return plan

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiry plan creation failed: {e}")
            return None

    async def _publish_expiry_alert(self, alert: ExpiryAlert):
        """发布到期告警"""
        try:
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type="expiry_alert",
                        data={
                            "alert_id": alert.alert_id,
                            "symbol": alert.symbol,
                            "expiry": alert.expiry.isoformat(),
                            "days_to_expiry": alert.days_to_expiry,
                            "risk_level": alert.risk_level.value,
                            "message": alert.message,
                            "suggested_action": alert.suggested_action.value,
                            "exercise_probability": alert.exercise_probability,
                            "potential_loss": float(alert.potential_loss),
                            "timestamp": alert.timestamp.isoformat(),
                        },
                    )
                )

            # 记录日志
            if self.logger:
                log_level = (
                    "critical" if alert.risk_level == ExpiryRisk.CRITICAL else "warning"
                )
                await getattr(self.logger, log_level)(alert.message)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiry alert publishing failed: {e}")

    async def _execute_auto_actions(self):
        """执行自动化处理动作"""
        try:
            current_time = datetime.now(timezone.utc)

            for symbol, plan in self.expiry_plans.items():
                if current_time >= plan.execution_time:
                    # 检查执行条件
                    if await self._check_execution_conditions(symbol, plan):
                        await self._execute_plan(plan)
                    else:
                        # 条件不满足，执行备用动作
                        if self.logger:
                            await self.logger.warning(
                                f"Execution conditions not met for {symbol}, executing backup action"
                            )
                        await self._execute_backup_action(plan)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Auto action execution failed: {e}")

    async def _check_execution_conditions(self, symbol: str, plan: ExpiryPlan) -> bool:
        """检查执行条件"""
        try:
            position = self.expiry_positions.get(symbol)
            if not position:
                return False

            # 检查时间价值条件
            if position.time_value < Decimal(str(plan.conditions["min_time_value"])):
                return True

            # 检查行权概率条件
            if position.exercise_probability > plan.conditions["max_exercise_prob"]:
                return True

            # 检查流动性条件
            liquidity_score = await self._get_liquidity_score(symbol)
            if liquidity_score < plan.conditions["min_liquidity"]:
                return False

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Execution condition check failed: {e}")
            return False

    async def _get_liquidity_score(self, symbol: str) -> float:
        """获取流动性评分"""
        try:
            if self.cache_manager:
                liquidity_data = await self.cache_manager.get(f"liquidity:{symbol}")
                if liquidity_data:
                    return liquidity_data.get("score", 0.5)

            return 0.5  # 默认中等流动性

        except Exception:
            return 0.5

    async def _execute_plan(self, plan: ExpiryPlan):
        """执行处理计划"""
        try:
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type="expiry_action_execute",
                        data={
                            "symbol": plan.symbol,
                            "action": plan.current_action.value,
                            "execution_time": plan.execution_time.isoformat(),
                            "risk_assessment": plan.risk_assessment,
                        },
                    )
                )

            if self.logger:
                await self.logger.info(
                    f"Executing expiry plan for {plan.symbol}: {plan.current_action.value}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Plan execution failed: {e}")

    async def _execute_backup_action(self, plan: ExpiryPlan):
        """执行备用动作"""
        try:
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type="expiry_backup_action",
                        data={
                            "symbol": plan.symbol,
                            "backup_action": plan.backup_action.value,
                            "original_action": plan.current_action.value,
                            "execution_time": datetime.now(timezone.utc).isoformat(),
                        },
                    )
                )

            if self.logger:
                await self.logger.warning(
                    f"Executing backup action for {plan.symbol}: {plan.backup_action.value}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Backup action execution failed: {e}")

    # 公共接口方法

    async def get_expiry_summary(self) -> Dict[str, Any]:
        """获取到期摘要"""
        try:
            summary = {
                "total_positions": len(self.expiry_positions),
                "risk_breakdown": {"critical": 0, "high": 0, "medium": 0, "low": 0},
                "expiry_timeline": {},
                "active_alerts": len(self.expiry_alerts),
                "pending_plans": len(self.expiry_plans),
            }

            # 统计风险分布
            for position in self.expiry_positions.values():
                summary["risk_breakdown"][position.risk_level.value] += 1

                # 按到期日分组
                expiry_key = position.expiry.strftime("%Y-%m-%d")
                if expiry_key not in summary["expiry_timeline"]:
                    summary["expiry_timeline"][expiry_key] = []

                summary["expiry_timeline"][expiry_key].append(
                    {
                        "symbol": position.symbol,
                        "days_to_expiry": position.days_to_expiry,
                        "risk_level": position.risk_level.value,
                        "exercise_probability": position.exercise_probability,
                    }
                )

            return summary

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiry summary generation failed: {e}")
            return {"status": "error", "message": str(e)}

    async def get_expiry_alerts(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取到期告警"""
        try:
            alerts_data = []
            for alert in self.expiry_alerts[-limit:]:
                alerts_data.append(
                    {
                        "alert_id": alert.alert_id,
                        "symbol": alert.symbol,
                        "expiry": alert.expiry.isoformat(),
                        "days_to_expiry": alert.days_to_expiry,
                        "risk_level": alert.risk_level.value,
                        "message": alert.message,
                        "suggested_action": alert.suggested_action.value,
                        "exercise_probability": alert.exercise_probability,
                        "potential_loss": float(alert.potential_loss),
                        "timestamp": alert.timestamp.isoformat(),
                    }
                )

            return alerts_data

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiry alerts retrieval failed: {e}")
            return []

    async def get_expiry_plans(self) -> Dict[str, Dict[str, Any]]:
        """获取到期处理计划"""
        try:
            plans_data = {}
            for symbol, plan in self.expiry_plans.items():
                plans_data[symbol] = {
                    "current_action": plan.current_action.value,
                    "backup_action": plan.backup_action.value,
                    "execution_time": plan.execution_time.isoformat(),
                    "conditions": plan.conditions,
                    "risk_assessment": plan.risk_assessment,
                    "expected_outcome": plan.expected_outcome,
                }

            return plans_data

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiry plans retrieval failed: {e}")
            return {}

    async def force_expiry_check(self) -> bool:
        """强制执行到期检查"""
        try:
            await self._scan_expiry_positions()
            await self._assess_expiry_risks()

            if self.logger:
                await self.logger.info("Force expiry check completed")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Force expiry check failed: {e}")
            return False

    async def update_expiry_config(self, new_config: Dict[str, Any]) -> bool:
        """更新到期配置"""
        try:
            # 更新风险阈值
            if "risk_thresholds" in new_config:
                self.risk_thresholds.update(new_config["risk_thresholds"])

            # 更新自动处理配置
            if "auto_actions" in new_config:
                self.auto_actions.update(new_config["auto_actions"])

            # 更新监控间隔
            if "monitoring_interval" in new_config:
                self._monitoring_interval = new_config["monitoring_interval"]

            if self.logger:
                await self.logger.info(f"Expiry config updated: {new_config}")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiry config update failed: {e}")
            return False

    def set_event_bus(self, event_bus: EventBus):
        """设置事件总线"""
        self.event_bus = event_bus

    def set_cache_manager(self, cache_manager: CacheManager):
        """设置缓存管理器"""
        self.cache_manager = cache_manager
