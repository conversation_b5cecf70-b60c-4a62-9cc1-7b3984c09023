from __future__ import annotations
"""
Pre-trade 过滤器（IBIT/IBKR 专用最小实现）
- 资金校验：CSP BuyingPower 足额
- 名义规模：日内 CSP 名义上限
- 流动性：价差/OI/成交量阈值
- Long Call θ 风险：简单禁入条件占位（MVP）
"""
from dataclasses import dataclass
from decimal import Decimal
from typing import Optional


@dataclass
class CSPCheck:
    required_cash: Decimal
    ok: bool
    reason: Optional[str] = None


@dataclass
class LiquidityCheck:
    spread_bps_ok: bool
    oi_ok: bool
    vol_ok: bool
    reason: Optional[str] = None


class PretradeFilters:
    def __init__(self, config: Optional[dict] = None):
        # 从配置加载参数，提供默认值
        config = config or {}
        self.max_spread_bps = config.get("max_spread_bps", 500)
        self.min_oi = config.get("min_oi", 200)
        self.min_volume = config.get("min_volume", 50)
        self.daily_csp_notional_cap = Decimal(str(config.get("daily_csp_notional_cap", "100000")))
        self._today_csp_notional: Decimal = Decimal("0")

    def reset_daily_counters(self) -> None:
        self._today_csp_notional = Decimal("0")

    def csp_buying_power(self, strike: Decimal, multiplier: int, qty: int, buying_power: Decimal) -> CSPCheck:
        required = strike * Decimal(multiplier) * Decimal(qty)
        if buying_power < required:
            return CSPCheck(required_cash=required, ok=False, reason="INSUFFICIENT_BP")
        # 名义上限检查
        if (self._today_csp_notional + required) >= self.daily_csp_notional_cap:
            return CSPCheck(required_cash=required, ok=False, reason="DAILY_CAP")
        return CSPCheck(required_cash=required, ok=True)

    def add_csp_notional(self, strike: Decimal, multiplier: int, qty: int) -> None:
        self._today_csp_notional += strike * Decimal(multiplier) * Decimal(qty)

    def liquidity(self, spread_bps: Optional[float], oi: Optional[int], volume: Optional[int]) -> LiquidityCheck:
        if spread_bps is None:
            return LiquidityCheck(False, False, False, reason="NO_SPREAD_DATA")
        spread_ok = spread_bps <= self.max_spread_bps
        oi_ok = (oi or 0) >= self.min_oi
        vol_ok = (volume or 0) >= self.min_volume
        ok = spread_ok and oi_ok and vol_ok
        return LiquidityCheck(spread_ok, oi_ok, vol_ok, None if ok else "LIQUIDITY_FAIL")

    def long_call_theta_guard(self, days_to_expiry: int, iv: Optional[float]) -> bool:
        # MVP：到期太近且 IV 偏高时禁入，避免 θ 侵蚀过快
        if days_to_expiry <= 7 and (iv or 0) > 0.9:
            return False
        return True

