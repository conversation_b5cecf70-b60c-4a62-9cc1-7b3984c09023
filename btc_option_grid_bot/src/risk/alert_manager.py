"""
告警管理器

负责多渠道告警发送、告警分类去重、历史跟踪和统计分析
"""

import asyncio
import hashlib
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Dict, List, Optional, Set

from ..core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from ..core.event_bus import EventBus
from ..data.cache_manager import CacheManager


class AlertLevel(Enum):
    """告警级别"""

    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertChannel(Enum):
    """告警渠道"""

    EMAIL = "email"
    TELEGRAM = "telegram"
    WEBHOOK = "webhook"
    SYSTEM = "system"
    SMS = "sms"


class AlertCategory(Enum):
    """告警分类"""

    RISK = "risk"
    MARGIN = "margin"
    EXPIRY = "expiry"
    SYSTEM = "system"
    TRADING = "trading"
    NETWORK = "network"


@dataclass
class Alert:
    """告警消息"""

    alert_id: str
    category: AlertCategory
    level: AlertLevel
    title: str
    message: str
    source: str
    data: Dict[str, Any] = field(default_factory=dict)
    channels: List[AlertChannel] = field(default_factory=list)
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    sent_channels: Set[AlertChannel] = field(default_factory=set)
    retry_count: int = 0
    max_retries: int = 3
    dedup_key: Optional[str] = None


@dataclass
class AlertRule:
    """告警规则"""

    rule_id: str
    name: str
    category: AlertCategory
    conditions: Dict[str, Any]
    channels: List[AlertChannel]
    level: AlertLevel
    enabled: bool = True
    cooldown_minutes: int = 60  # 冷却时间
    template: str = ""


@dataclass
class AlertStats:
    """告警统计"""

    total_alerts: int = 0
    alerts_by_level: Dict[str, int] = field(default_factory=dict)
    alerts_by_category: Dict[str, int] = field(default_factory=dict)
    alerts_by_channel: Dict[str, int] = field(default_factory=dict)
    success_rate: float = 0.0
    avg_response_time: float = 0.0
    last_24h_count: int = 0


class AlertManager(BaseComponent):
    """
    告警管理器

    功能特性：
    - 多渠道告警发送（邮件、Telegram、Webhook等）
    - 告警分类和优先级管理
    - 告警去重和频率控制
    - 告警历史跟踪和统计
    - 告警模板和规则管理
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("AlertManager", config)

        # 组件依赖
        self.event_bus: Optional[EventBus] = None
        self.cache_manager: Optional[CacheManager] = None

        # 告警状态
        self.pending_alerts: List[Alert] = []
        self.alert_history: List[Alert] = []
        self.alert_rules: Dict[str, AlertRule] = {}
        self.dedup_cache: Dict[str, datetime] = {}

        # 告警配置
        self._load_alert_config()

        # 发送任务
        self._sender_task: Optional[asyncio.Task] = None
        self._send_interval = 5  # 5秒发送间隔

        # 统计信息
        self.stats = AlertStats()
        self._max_history_size = 1000

        # 渠道配置
        self.channel_configs = {}
        self._load_channel_configs()

    def _load_alert_config(self):
        """加载告警配置"""
        alert_config = self.config.get("alerts", {}) if self.config else {}

        # 基础配置
        self.alert_settings = {
            "max_pending_alerts": alert_config.get("max_pending_alerts", 100),
            "dedup_window_minutes": alert_config.get("dedup_window_minutes", 60),
            "batch_size": alert_config.get("batch_size", 10),
            "retry_delay_seconds": alert_config.get("retry_delay_seconds", 30),
            "enable_rate_limiting": alert_config.get("enable_rate_limiting", True),
            "max_alerts_per_minute": alert_config.get("max_alerts_per_minute", 10),
        }

        # 默认告警规则
        self._create_default_rules()

    def _create_default_rules(self):
        """创建默认告警规则"""
        default_rules = [
            AlertRule(
                rule_id="risk_critical",
                name="关键风险告警",
                category=AlertCategory.RISK,
                conditions={"level": "critical"},
                channels=[AlertChannel.TELEGRAM, AlertChannel.EMAIL],
                level=AlertLevel.CRITICAL,
                cooldown_minutes=30,
            ),
            AlertRule(
                rule_id="margin_warning",
                name="保证金告警",
                category=AlertCategory.MARGIN,
                conditions={"usage_ratio": ">0.8"},
                channels=[AlertChannel.TELEGRAM],
                level=AlertLevel.WARNING,
                cooldown_minutes=60,
            ),
            AlertRule(
                rule_id="expiry_alert",
                name="到期告警",
                category=AlertCategory.EXPIRY,
                conditions={"days_to_expiry": "<=3"},
                channels=[AlertChannel.TELEGRAM, AlertChannel.SYSTEM],
                level=AlertLevel.WARNING,
                cooldown_minutes=360,  # 6小时
            ),
            AlertRule(
                rule_id="system_error",
                name="系统错误告警",
                category=AlertCategory.SYSTEM,
                conditions={"level": "error"},
                channels=[AlertChannel.EMAIL, AlertChannel.WEBHOOK],
                level=AlertLevel.ERROR,
                cooldown_minutes=15,
            ),
        ]

        for rule in default_rules:
            self.alert_rules[rule.rule_id] = rule

    def _load_channel_configs(self):
        """加载渠道配置"""
        channels_config = self.config.get("alert_channels", {}) if self.config else {}

        # Telegram配置
        self.channel_configs[AlertChannel.TELEGRAM] = {
            "bot_token": channels_config.get("telegram", {}).get("bot_token", ""),
            "chat_id": channels_config.get("telegram", {}).get("chat_id", ""),
            "enabled": bool(channels_config.get("telegram", {}).get("bot_token")),
        }

        # 邮件配置
        self.channel_configs[AlertChannel.EMAIL] = {
            "smtp_server": channels_config.get("email", {}).get("smtp_server", ""),
            "smtp_port": channels_config.get("email", {}).get("smtp_port", 587),
            "username": channels_config.get("email", {}).get("username", ""),
            "password": channels_config.get("email", {}).get("password", ""),
            "to_addresses": channels_config.get("email", {}).get("to_addresses", []),
            "enabled": bool(channels_config.get("email", {}).get("smtp_server")),
        }

        # Webhook配置
        self.channel_configs[AlertChannel.WEBHOOK] = {
            "url": channels_config.get("webhook", {}).get("url", ""),
            "headers": channels_config.get("webhook", {}).get("headers", {}),
            "enabled": bool(channels_config.get("webhook", {}).get("url")),
        }

    async def _initialize_impl(self) -> bool:
        """初始化告警管理器"""
        try:
            # 订阅事件
            if self.event_bus:
                await self.event_bus.subscribe("risk_event", self._handle_risk_event)
                await self.event_bus.subscribe(
                    "margin_alert", self._handle_margin_alert
                )
                await self.event_bus.subscribe(
                    "expiry_alert", self._handle_expiry_alert
                )
                await self.event_bus.subscribe(
                    "system_error", self._handle_system_error
                )

            if self.logger:
                await self.logger.info("AlertManager initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"AlertManager initialization failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动告警管理器"""
        try:
            # 启动发送任务
            self._sender_task = asyncio.create_task(self._sender_loop())

            if self.logger:
                await self.logger.info("AlertManager started")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"AlertManager start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止告警管理器"""
        try:
            # 停止发送任务
            if self._sender_task and not self._sender_task.done():
                self._sender_task.cancel()
                try:
                    await self._sender_task
                except asyncio.CancelledError:
                    pass

            # 发送剩余告警
            if self.pending_alerts:
                await self._send_pending_alerts()

            if self.logger:
                await self.logger.info("AlertManager stopped")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"AlertManager stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查"""
        try:
            # 检查发送任务状态
            if not self._sender_task or self._sender_task.done():
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message="Alert sender task not running",
                )

            # 检查待发送告警数量
            if len(self.pending_alerts) > self.alert_settings["max_pending_alerts"]:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"Too many pending alerts: {len(self.pending_alerts)}",
                )

            # 检查渠道可用性
            available_channels = sum(
                1 for config in self.channel_configs.values() if config.get("enabled")
            )
            if available_channels == 0:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED, message="No alert channels configured"
                )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message=f"Alert system operational, {available_channels} channels available",
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    async def _sender_loop(self):
        """告警发送循环"""
        while True:
            try:
                await self._send_pending_alerts()
                await self._cleanup_old_data()
                await asyncio.sleep(self._send_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Alert sender error: {e}")
                await asyncio.sleep(10)  # 错误后等待10秒

    async def _send_pending_alerts(self):
        """发送待处理告警"""
        try:
            if not self.pending_alerts:
                return

            # 批量处理告警
            batch_size = self.alert_settings["batch_size"]
            alerts_to_send = self.pending_alerts[:batch_size]

            for alert in alerts_to_send:
                success = await self._send_alert(alert)
                if success:
                    self.pending_alerts.remove(alert)
                    self.alert_history.append(alert)
                    self._update_stats(alert, success=True)
                else:
                    alert.retry_count += 1
                    if alert.retry_count >= alert.max_retries:
                        self.pending_alerts.remove(alert)
                        self.alert_history.append(alert)
                        self._update_stats(alert, success=False)

                        if self.logger:
                            await self.logger.error(
                                f"Alert {alert.alert_id} failed after {alert.max_retries} retries"
                            )

            # 清理历史记录
            if len(self.alert_history) > self._max_history_size:
                self.alert_history = self.alert_history[-self._max_history_size :]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Pending alerts sending failed: {e}")

    async def _send_alert(self, alert: Alert) -> bool:
        """发送单个告警"""
        try:
            success_count = 0

            for channel in alert.channels:
                if channel in alert.sent_channels:
                    continue  # 已发送过的渠道跳过

                channel_success = await self._send_to_channel(alert, channel)
                if channel_success:
                    alert.sent_channels.add(channel)
                    success_count += 1

            # 如果至少有一个渠道发送成功，认为告警发送成功
            return success_count > 0

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Alert sending failed: {e}")
            return False

    async def _send_to_channel(self, alert: Alert, channel: AlertChannel) -> bool:
        """发送到指定渠道"""
        try:
            config = self.channel_configs.get(channel, {})
            if not config.get("enabled"):
                return False

            if channel == AlertChannel.TELEGRAM:
                return await self._send_telegram(alert, config)
            elif channel == AlertChannel.EMAIL:
                return await self._send_email(alert, config)
            elif channel == AlertChannel.WEBHOOK:
                return await self._send_webhook(alert, config)
            elif channel == AlertChannel.SYSTEM:
                return await self._send_system(alert)
            else:
                if self.logger:
                    await self.logger.warning(f"Unsupported alert channel: {channel}")
                return False

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Channel {channel.value} sending failed: {e}")
            return False

    # 公共接口方法

    async def send_alert(
        self,
        category: AlertCategory,
        level: AlertLevel,
        title: str,
        message: str,
        source: str,
        data: Optional[Dict[str, Any]] = None,
        channels: Optional[List[AlertChannel]] = None,
    ) -> str:
        """发送告警"""
        try:
            # 生成告警ID
            alert_id = hashlib.md5(
                f"{title}_{message}_{source}_{datetime.now().timestamp()}".encode()
            ).hexdigest()[:16]

            # 去重检查
            dedup_key = hashlib.md5(
                f"{category.value}_{title}_{source}".encode()
            ).hexdigest()
            current_time = datetime.now(timezone.utc)

            if dedup_key in self.dedup_cache:
                last_time = self.dedup_cache[dedup_key]
                window_minutes = self.alert_settings["dedup_window_minutes"]
                if (current_time - last_time).total_seconds() < window_minutes * 60:
                    if self.logger:
                        await self.logger.debug(f"Alert deduplicated: {title}")
                    return alert_id

            # 更新去重缓存
            self.dedup_cache[dedup_key] = current_time

            # 确定发送渠道
            if channels is None:
                # 根据规则确定渠道
                channels = self._determine_channels(category, level)

            # 创建告警
            alert = Alert(
                alert_id=alert_id,
                category=category,
                level=level,
                title=title,
                message=message,
                source=source,
                data=data or {},
                channels=channels,
                dedup_key=dedup_key,
            )

            # 添加到待发送队列
            self.pending_alerts.append(alert)

            # 检查队列长度
            if len(self.pending_alerts) > self.alert_settings["max_pending_alerts"]:
                self.pending_alerts.pop(0)  # 移除最旧的告警

            if self.logger:
                await self.logger.info(f"Alert queued: {alert_id} - {title}")

            return alert_id

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Alert creation failed: {e}")
            return ""

    def _determine_channels(
        self, category: AlertCategory, level: AlertLevel
    ) -> List[AlertChannel]:
        """根据规则确定发送渠道"""
        try:
            channels = []

            # 根据级别确定基础渠道
            if level == AlertLevel.CRITICAL:
                channels = [
                    AlertChannel.TELEGRAM,
                    AlertChannel.EMAIL,
                    AlertChannel.SYSTEM,
                ]
            elif level == AlertLevel.ERROR:
                channels = [AlertChannel.TELEGRAM, AlertChannel.SYSTEM]
            elif level == AlertLevel.WARNING:
                channels = [AlertChannel.TELEGRAM]
            else:  # INFO
                channels = [AlertChannel.SYSTEM]

            # 根据分类调整渠道
            if category == AlertCategory.SYSTEM:
                if AlertChannel.WEBHOOK not in channels:
                    channels.append(AlertChannel.WEBHOOK)

            # 过滤可用渠道
            available_channels = []
            for channel in channels:
                config = self.channel_configs.get(channel, {})
                if config.get("enabled", False):
                    available_channels.append(channel)

            return available_channels if available_channels else [AlertChannel.SYSTEM]

        except Exception:
            return [AlertChannel.SYSTEM]

    async def get_alert_stats(self) -> Dict[str, Any]:
        """获取告警统计"""
        try:
            return {
                "total_alerts": self.stats.total_alerts,
                "alerts_by_level": dict(self.stats.alerts_by_level),
                "alerts_by_category": dict(self.stats.alerts_by_category),
                "alerts_by_channel": dict(self.stats.alerts_by_channel),
                "success_rate": round(self.stats.success_rate, 3),
                "last_24h_count": self.stats.last_24h_count,
                "pending_count": len(self.pending_alerts),
                "history_count": len(self.alert_history),
                "active_channels": sum(
                    1
                    for config in self.channel_configs.values()
                    if config.get("enabled")
                ),
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Alert stats generation failed: {e}")
            return {"status": "error", "message": str(e)}

    async def get_recent_alerts(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取最近的告警"""
        try:
            recent_alerts = []
            all_alerts = self.alert_history + self.pending_alerts

            # 按时间排序
            sorted_alerts = sorted(all_alerts, key=lambda x: x.timestamp, reverse=True)

            for alert in sorted_alerts[:limit]:
                recent_alerts.append(
                    {
                        "alert_id": alert.alert_id,
                        "category": alert.category.value,
                        "level": alert.level.value,
                        "title": alert.title,
                        "message": alert.message,
                        "source": alert.source,
                        "timestamp": alert.timestamp.isoformat(),
                        "channels": [ch.value for ch in alert.channels],
                        "sent_channels": [ch.value for ch in alert.sent_channels],
                        "retry_count": alert.retry_count,
                        "status": "sent" if alert in self.alert_history else "pending",
                    }
                )

            return recent_alerts

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Recent alerts retrieval failed: {e}")
            return []

    async def test_channel(self, channel: AlertChannel) -> bool:
        """测试告警渠道"""
        try:
            test_alert = Alert(
                alert_id="test_alert",
                category=AlertCategory.SYSTEM,
                level=AlertLevel.INFO,
                title="告警渠道测试",
                message="这是一条测试告警消息",
                source="AlertManager",
                channels=[channel],
            )

            success = await self._send_to_channel(test_alert, channel)

            if self.logger:
                status = "成功" if success else "失败"
                await self.logger.info(f"Channel {channel.value} test {status}")

            return success

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Channel test failed: {e}")
            return False

    def set_event_bus(self, event_bus: EventBus):
        """设置事件总线"""
        self.event_bus = event_bus

    def set_cache_manager(self, cache_manager: CacheManager):
        """设置缓存管理器"""
        self.cache_manager = cache_manager
