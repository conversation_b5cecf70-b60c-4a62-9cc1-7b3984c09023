"""
风险控制引擎

负责实时风险监控、告警和自动化风险处理，专门针对期权交易的特有风险
"""

import asyncio
import math
import time
from dataclasses import dataclass, field
from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple, Union

from ..core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from ..core.event_bus import BaseEvent, EventBus
from ..data.cache_manager import CacheManager


class RiskLevel(Enum):
    """风险级别"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RiskType(Enum):
    """风险类型"""

    MARGIN = "margin"
    DELTA = "delta"
    GAMMA = "gamma"
    THETA = "theta"
    VEGA = "vega"
    LIQUIDITY = "liquidity"
    CONCENTRATION = "concentration"
    EXPIRY = "expiry"
    VAR = "var"


@dataclass
class RiskMetrics:
    """风险指标"""

    total_delta: Decimal = Decimal("0")
    total_gamma: Decimal = Decimal("0")
    total_theta: Decimal = Decimal("0")
    total_vega: Decimal = Decimal("0")
    margin_usage_ratio: Decimal = Decimal("0")
    portfolio_var: Decimal = Decimal("0")
    max_single_position_risk: Decimal = Decimal("0")
    liquidity_score: float = 1.0
    concentration_risk: float = 0.0
    days_to_nearest_expiry: int = 999
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


@dataclass
class RiskEvent:
    """风险事件"""

    event_id: str
    risk_type: RiskType
    risk_level: RiskLevel
    message: str
    current_value: Union[Decimal, float]
    threshold: Union[Decimal, float]
    suggested_action: str
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RiskLimits:
    """风险限制配置"""

    # Greeks风险限制
    max_delta_exposure: Decimal = Decimal("0.5")  # 最大Delta敞口
    max_gamma_exposure: Decimal = Decimal("0.05")  # 最大Gamma敞口
    max_vega_exposure: Decimal = Decimal("1000")  # 最大Vega敞口

    # 保证金风险限制
    max_margin_usage: Decimal = Decimal("0.7")  # 最大保证金使用率70%
    margin_warning_level: Decimal = Decimal("0.5")  # 保证金警告水平50%

    # 组合风险限制
    max_portfolio_var: Decimal = Decimal("0.05")  # 最大VaR 5%
    max_daily_loss: Decimal = Decimal("0.08")  # 最大日损失8%
    max_single_position: Decimal = Decimal("0.08")  # 单仓位最大占比8%

    # 流动性风险限制
    min_liquidity_score: float = 0.2  # 最小流动性评分
    max_bid_ask_spread: Decimal = Decimal("0.05")  # 最大买卖价差5%

    # 到期风险限制
    min_days_to_expiry: int = 1  # 最小到期天数
    expiry_warning_days: int = 7  # 到期预警天数


class RiskEngine(BaseComponent):
    """
    风险控制引擎

    实时监控期权交易的特有风险，包括：
    - Greeks风险（Delta、Gamma、Theta、Vega）
    - 保证金风险
    - 流动性风险
    - 到期风险
    - 集中度风险
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("RiskEngine", config)

        # 组件依赖
        self.event_bus: Optional[EventBus] = None
        self.cache_manager: Optional[CacheManager] = None

        # 风险配置
        self.risk_limits = RiskLimits()
        self._load_risk_limits_from_config()

        # 风险监控状态
        self.current_metrics: Optional[RiskMetrics] = None
        self.risk_events: List[RiskEvent] = []
        self.last_risk_check: Optional[datetime] = None

        # 监控任务
        self._monitoring_task: Optional[asyncio.Task] = None
        self._monitoring_interval = 30  # 30秒监控间隔

        # 风险历史
        self._risk_history: List[RiskMetrics] = []
        self._max_history_size = 1000

        # 紧急处理标志
        self._emergency_mode = False
        self._emergency_actions_enabled = True

    def _load_risk_limits_from_config(self):
        """从配置加载风险限制"""
        if not self.config:
            return

        risk_config = self.config.get("risk_limits", {})

        # 更新风险限制
        for attr_name in dir(self.risk_limits):
            if not attr_name.startswith("_"):
                config_value = risk_config.get(attr_name)
                if config_value is not None:
                    if isinstance(getattr(self.risk_limits, attr_name), Decimal):
                        setattr(self.risk_limits, attr_name, Decimal(str(config_value)))
                    else:
                        setattr(self.risk_limits, attr_name, config_value)

    async def _initialize_impl(self) -> bool:
        """初始化风险引擎"""
        try:
            # 初始化风险指标
            self.current_metrics = RiskMetrics()

            if self.logger:
                await self.logger.info("RiskEngine initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"RiskEngine initialization failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动风险监控"""
        try:
            # 启动监控任务
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())

            if self.logger:
                await self.logger.info("RiskEngine monitoring started")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"RiskEngine start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止风险监控"""
        try:
            # 停止监控任务
            if self._monitoring_task and not self._monitoring_task.done():
                self._monitoring_task.cancel()
                try:
                    await self._monitoring_task
                except asyncio.CancelledError:
                    pass

            if self.logger:
                await self.logger.info("RiskEngine stopped")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"RiskEngine stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查"""
        try:
            # 检查监控任务状态
            if not self._monitoring_task or self._monitoring_task.done():
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message="Risk monitoring task not running",
                )

            # 检查最近的风险检查时间
            if self.last_risk_check:
                time_since_check = datetime.now(timezone.utc) - self.last_risk_check
                if time_since_check.total_seconds() > self._monitoring_interval * 2:
                    return HealthCheckResult(
                        status=HealthStatus.DEGRADED,
                        message=f"Risk check delayed by {time_since_check.total_seconds():.1f}s",
                    )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY, message="Risk monitoring operational"
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    async def _monitoring_loop(self):
        """风险监控循环"""
        while True:
            try:
                await self._perform_risk_check()
                await asyncio.sleep(self._monitoring_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Risk monitoring error: {e}")
                await asyncio.sleep(5)  # 错误后短暂等待

    async def _perform_risk_check(self):
        """执行风险检查"""
        try:
            # 计算当前风险指标
            await self._calculate_risk_metrics()

            # 检查风险限制
            risk_events = await self._check_risk_limits()

            # 处理风险事件
            if risk_events:
                await self._handle_risk_events(risk_events)

            # 更新检查时间
            self.last_risk_check = datetime.now(timezone.utc)

            # 保存风险历史
            if self.current_metrics:
                self._risk_history.append(self.current_metrics)
                if len(self._risk_history) > self._max_history_size:
                    self._risk_history.pop(0)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk check failed: {e}")

    async def _calculate_risk_metrics(self):
        """计算风险指标"""
        try:
            if not self.cache_manager:
                return

            # 获取当前仓位数据
            positions_data = await self.cache_manager.get("positions")
            if not positions_data:
                return

            # 初始化指标
            total_delta = Decimal("0")
            total_gamma = Decimal("0")
            total_theta = Decimal("0")
            total_vega = Decimal("0")
            max_single_position = Decimal("0")
            total_portfolio_value = Decimal("0")

            # 计算Greeks敞口
            for symbol, position_data in positions_data.items():
                if isinstance(position_data, dict):
                    size = Decimal(str(position_data.get("size", 0)))
                    market_value = Decimal(str(position_data.get("market_value", 0)))

                    # 获取Greeks数据
                    greeks_data = await self.cache_manager.get(f"greeks:{symbol}")
                    if greeks_data:
                        delta = Decimal(str(greeks_data.get("delta", 0)))
                        gamma = Decimal(str(greeks_data.get("gamma", 0)))
                        theta = Decimal(str(greeks_data.get("theta", 0)))
                        vega = Decimal(str(greeks_data.get("vega", 0)))

                        # 计算仓位Greeks
                        position_delta = size * delta
                        position_gamma = size * gamma
                        position_theta = size * theta
                        position_vega = size * vega

                        total_delta += position_delta
                        total_gamma += position_gamma
                        total_theta += position_theta
                        total_vega += position_vega

                    total_portfolio_value += market_value
                    max_single_position = max(max_single_position, market_value)

            # 计算保证金使用率
            margin_data = await self.cache_manager.get("margin_info")
            margin_usage_ratio = Decimal("0")
            if margin_data:
                used_margin = Decimal(str(margin_data.get("used_margin", 0)))
                available_margin = Decimal(str(margin_data.get("available_margin", 0)))
                total_margin = used_margin + available_margin
                if total_margin > 0:
                    margin_usage_ratio = used_margin / total_margin

            # 计算流动性评分
            liquidity_score = await self._calculate_liquidity_score()

            # 计算集中度风险
            concentration_risk = 0.0
            if total_portfolio_value > 0:
                concentration_risk = float(max_single_position / total_portfolio_value)

            # 计算最近到期天数
            days_to_nearest_expiry = await self._calculate_nearest_expiry()

            # 计算组合VaR
            current_btc_price = await self._get_current_btc_price()
            current_volatility = await self._get_current_volatility()
            portfolio_var = await self._calculate_portfolio_var(
                positions_data,
                {"current_price": current_btc_price, "volatility": current_volatility},
            )

            # 更新风险指标
            self.current_metrics = RiskMetrics(
                total_delta=total_delta,
                total_gamma=total_gamma,
                total_theta=total_theta,
                total_vega=total_vega,
                margin_usage_ratio=margin_usage_ratio,
                portfolio_var=portfolio_var,
                max_single_position_risk=concentration_risk,
                liquidity_score=liquidity_score,
                concentration_risk=concentration_risk,
                days_to_nearest_expiry=days_to_nearest_expiry,
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk metrics calculation failed: {e}")

    async def _calculate_liquidity_score(self) -> float:
        """计算流动性评分"""
        try:
            # 获取期权链数据
            option_chain = await self.cache_manager.get("btc_option_chain")
            if not option_chain:
                return 1.0

            total_score = 0.0
            count = 0

            for option_data in option_chain:
                if isinstance(option_data, dict):
                    bid_ask_spread = option_data.get("bid_ask_spread", 0)
                    volume = option_data.get("volume", 0)
                    open_interest = option_data.get("open_interest", 0)

                    # 计算单个期权的流动性评分
                    spread_score = max(0, 1 - bid_ask_spread / 0.05)  # 5%为最差
                    volume_score = min(1, volume / 100)  # 100为满分
                    oi_score = min(1, open_interest / 500)  # 500为满分

                    option_score = (spread_score + volume_score + oi_score) / 3
                    total_score += option_score
                    count += 1

            return total_score / count if count > 0 else 1.0

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Liquidity score calculation failed: {e}")
            return 1.0

    async def _calculate_nearest_expiry(self) -> int:
        """计算最近到期天数"""
        try:
            positions_data = await self.cache_manager.get("positions")
            if not positions_data:
                return 999

            nearest_expiry = None
            current_time = datetime.now(timezone.utc)

            for symbol, position_data in positions_data.items():
                if isinstance(position_data, dict):
                    expiry_str = position_data.get("expiry")
                    if expiry_str:
                        try:
                            expiry_date = datetime.fromisoformat(
                                expiry_str.replace("Z", "+00:00")
                            )
                            if nearest_expiry is None or expiry_date < nearest_expiry:
                                nearest_expiry = expiry_date
                        except Exception:
                            continue

            if nearest_expiry:
                days_to_expiry = (nearest_expiry - current_time).days
                return max(0, days_to_expiry)

            return 999

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Nearest expiry calculation failed: {e}")
            return 999

    async def _check_risk_limits(self) -> List[RiskEvent]:
        """检查风险限制"""
        risk_events = []

        if not self.current_metrics:
            return risk_events

        try:
            # 检查Delta敞口
            if (
                abs(self.current_metrics.total_delta)
                > self.risk_limits.max_delta_exposure
            ):
                risk_events.append(
                    RiskEvent(
                        event_id=f"delta_risk_{int(time.time())}",
                        risk_type=RiskType.DELTA,
                        risk_level=RiskLevel.HIGH,
                        message=f"Delta exposure exceeded: {self.current_metrics.total_delta}",
                        current_value=abs(self.current_metrics.total_delta),
                        threshold=self.risk_limits.max_delta_exposure,
                        suggested_action="Execute delta hedging or reduce positions",
                    )
                )

            # 检查Gamma敞口
            if (
                abs(self.current_metrics.total_gamma)
                > self.risk_limits.max_gamma_exposure
            ):
                risk_events.append(
                    RiskEvent(
                        event_id=f"gamma_risk_{int(time.time())}",
                        risk_type=RiskType.GAMMA,
                        risk_level=RiskLevel.HIGH,
                        message=f"Gamma exposure exceeded: {self.current_metrics.total_gamma}",
                        current_value=abs(self.current_metrics.total_gamma),
                        threshold=self.risk_limits.max_gamma_exposure,
                        suggested_action="Close high gamma positions or adjust strikes",
                    )
                )

            # 检查保证金使用率
            if (
                self.current_metrics.margin_usage_ratio
                > self.risk_limits.max_margin_usage
            ):
                risk_level = (
                    RiskLevel.CRITICAL
                    if self.current_metrics.margin_usage_ratio > Decimal("0.8")
                    else RiskLevel.HIGH
                )
                risk_events.append(
                    RiskEvent(
                        event_id=f"margin_risk_{int(time.time())}",
                        risk_type=RiskType.MARGIN,
                        risk_level=risk_level,
                        message=f"Margin usage exceeded: {self.current_metrics.margin_usage_ratio:.2%}",
                        current_value=self.current_metrics.margin_usage_ratio,
                        threshold=self.risk_limits.max_margin_usage,
                        suggested_action="Reduce positions or add margin",
                    )
                )
            elif (
                self.current_metrics.margin_usage_ratio
                > self.risk_limits.margin_warning_level
            ):
                risk_events.append(
                    RiskEvent(
                        event_id=f"margin_warning_{int(time.time())}",
                        risk_type=RiskType.MARGIN,
                        risk_level=RiskLevel.MEDIUM,
                        message=f"Margin usage warning: {self.current_metrics.margin_usage_ratio:.2%}",
                        current_value=self.current_metrics.margin_usage_ratio,
                        threshold=self.risk_limits.margin_warning_level,
                        suggested_action="Monitor margin usage closely",
                    )
                )

            # 检查流动性风险
            if (
                self.current_metrics.liquidity_score
                < self.risk_limits.min_liquidity_score
            ):
                risk_events.append(
                    RiskEvent(
                        event_id=f"liquidity_risk_{int(time.time())}",
                        risk_type=RiskType.LIQUIDITY,
                        risk_level=RiskLevel.MEDIUM,
                        message=f"Low liquidity score: {self.current_metrics.liquidity_score:.2f}",
                        current_value=self.current_metrics.liquidity_score,
                        threshold=self.risk_limits.min_liquidity_score,
                        suggested_action="Avoid new positions in illiquid options",
                    )
                )

            # 检查集中度风险
            if self.current_metrics.concentration_risk > float(
                self.risk_limits.max_single_position
            ):
                risk_events.append(
                    RiskEvent(
                        event_id=f"concentration_risk_{int(time.time())}",
                        risk_type=RiskType.CONCENTRATION,
                        risk_level=RiskLevel.MEDIUM,
                        message=f"Position concentration too high: {self.current_metrics.concentration_risk:.2%}",
                        current_value=self.current_metrics.concentration_risk,
                        threshold=float(self.risk_limits.max_single_position),
                        suggested_action="Diversify positions across strikes and expiries",
                    )
                )

            # 检查到期风险
            if (
                self.current_metrics.days_to_nearest_expiry
                <= self.risk_limits.expiry_warning_days
            ):
                risk_level = (
                    RiskLevel.CRITICAL
                    if self.current_metrics.days_to_nearest_expiry
                    <= self.risk_limits.min_days_to_expiry
                    else RiskLevel.HIGH
                )
                risk_events.append(
                    RiskEvent(
                        event_id=f"expiry_risk_{int(time.time())}",
                        risk_type=RiskType.EXPIRY,
                        risk_level=risk_level,
                        message=f"Options expiring soon: {self.current_metrics.days_to_nearest_expiry} days",
                        current_value=self.current_metrics.days_to_nearest_expiry,
                        threshold=self.risk_limits.expiry_warning_days,
                        suggested_action="Prepare for expiry or roll positions",
                    )
                )

            return risk_events

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk limit check failed: {e}")
            return risk_events

    async def _handle_risk_events(self, risk_events: List[RiskEvent]):
        """处理风险事件"""
        try:
            for event in risk_events:
                # 记录风险事件
                self.risk_events.append(event)

                # 发布风险事件
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type="risk_event",
                            data={
                                "event_id": event.event_id,
                                "risk_type": event.risk_type.value,
                                "risk_level": event.risk_level.value,
                                "message": event.message,
                                "current_value": float(event.current_value),
                                "threshold": float(event.threshold),
                                "suggested_action": event.suggested_action,
                                "timestamp": event.timestamp.isoformat(),
                            },
                        )
                    )

                # 记录日志
                if self.logger:
                    log_level = (
                        "critical"
                        if event.risk_level == RiskLevel.CRITICAL
                        else "warning"
                    )
                    await getattr(self.logger, log_level)(
                        f"Risk Event: {event.message} - {event.suggested_action}"
                    )

                # 执行自动化风险处理
                if self._emergency_actions_enabled and event.risk_level in [
                    RiskLevel.HIGH,
                    RiskLevel.CRITICAL,
                ]:
                    await self._execute_emergency_actions(event)

            # 清理旧的风险事件（保留最近100个）
            if len(self.risk_events) > 100:
                self.risk_events = self.risk_events[-100:]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk event handling failed: {e}")

    async def _execute_emergency_actions(self, event: RiskEvent):
        """执行紧急风险处理"""
        try:
            if (
                event.risk_type == RiskType.MARGIN
                and event.risk_level == RiskLevel.CRITICAL
            ):
                # 保证金不足，触发紧急减仓
                await self._trigger_emergency_position_reduction(0.3)  # 减仓30%

            elif (
                event.risk_type == RiskType.DELTA and event.risk_level == RiskLevel.HIGH
            ):
                # Delta敞口过大，触发对冲
                await self._trigger_delta_hedging()

            elif (
                event.risk_type == RiskType.EXPIRY
                and event.risk_level == RiskLevel.CRITICAL
            ):
                # 即将到期，触发紧急处理
                await self._trigger_expiry_emergency_handling()

            # 进入紧急模式
            if not self._emergency_mode:
                self._emergency_mode = True
                if self.logger:
                    await self.logger.critical(
                        "System entered emergency mode due to critical risk"
                    )

                # 发布紧急模式事件
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type="emergency_mode_activated",
                            data={
                                "trigger_event": event.event_id,
                                "risk_type": event.risk_type.value,
                                "timestamp": datetime.now(timezone.utc).isoformat(),
                            },
                        )
                    )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Emergency action execution failed: {e}")

    async def _trigger_emergency_position_reduction(self, reduction_ratio: float):
        """触发紧急减仓"""
        if self.event_bus:
            await self.event_bus.publish(
                BaseEvent(
                    event_type="emergency_position_reduction",
                    data={
                        "reduction_ratio": reduction_ratio,
                        "reason": "margin_risk",
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    },
                )
            )

    async def _trigger_delta_hedging(self):
        """触发Delta对冲"""
        if self.event_bus:
            await self.event_bus.publish(
                BaseEvent(
                    event_type="emergency_delta_hedging",
                    data={
                        "current_delta": float(self.current_metrics.total_delta)
                        if self.current_metrics
                        else 0,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    },
                )
            )

    async def _trigger_expiry_emergency_handling(self):
        """触发到期紧急处理"""
        if self.event_bus:
            await self.event_bus.publish(
                BaseEvent(
                    event_type="emergency_expiry_handling",
                    data={
                        "days_to_expiry": self.current_metrics.days_to_nearest_expiry
                        if self.current_metrics
                        else 0,
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    },
                )
            )

    # 公共接口方法

    async def get_current_risk_metrics(self) -> Optional[RiskMetrics]:
        """获取当前风险指标"""
        return self.current_metrics

    async def get_risk_events(self, limit: int = 50) -> List[RiskEvent]:
        """获取风险事件历史"""
        return self.risk_events[-limit:] if self.risk_events else []

    async def validate_order_risk(self, order_data: Dict[str, Any]) -> Tuple[bool, str]:
        """验证订单风险"""
        try:
            if not self.current_metrics:
                return False, "Risk metrics not available"

            # 检查保证金是否充足
            if self.current_metrics.margin_usage_ratio > Decimal("0.9"):
                return False, "Insufficient margin for new orders"

            # 检查流动性
            if self.current_metrics.liquidity_score < 0.3:
                return False, "Market liquidity too low for new orders"

            # 检查是否在紧急模式
            if self._emergency_mode:
                return False, "System in emergency mode, new orders blocked"

            return True, "Order risk validation passed"

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order risk validation failed: {e}")
            return False, f"Risk validation error: {e}"

    async def update_risk_limits(self, new_limits: Dict[str, Any]) -> bool:
        """更新风险限制"""
        try:
            for key, value in new_limits.items():
                if hasattr(self.risk_limits, key):
                    if isinstance(getattr(self.risk_limits, key), Decimal):
                        setattr(self.risk_limits, key, Decimal(str(value)))
                    else:
                        setattr(self.risk_limits, key, value)

            if self.logger:
                await self.logger.info(f"Risk limits updated: {new_limits}")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk limits update failed: {e}")
            return False

    async def reset_emergency_mode(self) -> bool:
        """重置紧急模式"""
        try:
            self._emergency_mode = False

            if self.logger:
                await self.logger.info("Emergency mode reset")

            # 发布紧急模式重置事件
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type="emergency_mode_reset",
                        data={"timestamp": datetime.now(timezone.utc).isoformat()},
                    )
                )

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Emergency mode reset failed: {e}")
            return False

    async def get_risk_summary(self) -> Dict[str, Any]:
        """获取风险摘要"""
        try:
            if not self.current_metrics:
                return {"status": "no_data"}

            # 计算风险评分
            risk_score = 0.0
            risk_factors = []

            # Delta风险
            delta_risk = min(
                1.0,
                float(
                    abs(self.current_metrics.total_delta)
                    / self.risk_limits.max_delta_exposure
                ),
            )
            risk_score += delta_risk * 0.2
            if delta_risk > 0.8:
                risk_factors.append(
                    f"High Delta exposure: {self.current_metrics.total_delta}"
                )

            # 保证金风险
            margin_risk = float(
                self.current_metrics.margin_usage_ratio
                / self.risk_limits.max_margin_usage
            )
            risk_score += margin_risk * 0.3
            if margin_risk > 0.8:
                risk_factors.append(
                    f"High margin usage: {self.current_metrics.margin_usage_ratio:.2%}"
                )

            # 流动性风险
            liquidity_risk = max(
                0.0,
                1.0
                - self.current_metrics.liquidity_score
                / self.risk_limits.min_liquidity_score,
            )
            risk_score += liquidity_risk * 0.2
            if liquidity_risk > 0.5:
                risk_factors.append(
                    f"Low liquidity: {self.current_metrics.liquidity_score:.2f}"
                )

            # 到期风险
            expiry_risk = max(
                0.0,
                1.0
                - self.current_metrics.days_to_nearest_expiry
                / self.risk_limits.expiry_warning_days,
            )
            risk_score += expiry_risk * 0.3
            if expiry_risk > 0.5:
                risk_factors.append(
                    f"Near expiry: {self.current_metrics.days_to_nearest_expiry} days"
                )

            # 确定风险级别
            if risk_score > 0.8:
                risk_level = "CRITICAL"
            elif risk_score > 0.6:
                risk_level = "HIGH"
            elif risk_score > 0.4:
                risk_level = "MEDIUM"
            else:
                risk_level = "LOW"

            return {
                "status": "active",
                "risk_score": round(risk_score, 3),
                "risk_level": risk_level,
                "emergency_mode": self._emergency_mode,
                "risk_factors": risk_factors,
                "metrics": {
                    "total_delta": float(self.current_metrics.total_delta),
                    "total_gamma": float(self.current_metrics.total_gamma),
                    "margin_usage": float(self.current_metrics.margin_usage_ratio),
                    "liquidity_score": self.current_metrics.liquidity_score,
                    "days_to_expiry": self.current_metrics.days_to_nearest_expiry,
                },
                "last_check": self.last_risk_check.isoformat()
                if self.last_risk_check
                else None,
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk summary generation failed: {e}")
            return {"status": "error", "message": str(e)}

    def set_event_bus(self, event_bus: EventBus):
        """设置事件总线"""
        self.event_bus = event_bus

    def set_cache_manager(self, cache_manager: CacheManager):
        """设置缓存管理器"""
        self.cache_manager = cache_manager

    async def _get_current_btc_price(self) -> float:
        """获取当前BTC价格"""
        try:
            if not self.cache_manager:
                return 50000.0  # 默认价格

            price_data = await self.cache_manager.get("btc_price", "market_data")
            if price_data:
                return float(price_data.get("price", 50000.0))

            return 50000.0

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get current BTC price: {e}")
            return 50000.0

    async def _get_current_volatility(self) -> float:
        """获取当前波动率"""
        try:
            if not self.cache_manager:
                return 0.5  # 默认50%波动率

            vol_data = await self.cache_manager.get(
                "btc_implied_volatility", "market_data"
            )
            if vol_data:
                return float(vol_data.get("iv", 0.5))

            return 0.5

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get current volatility: {e}")
            return 0.5

    async def _calculate_portfolio_var(
        self, positions_data: Dict[str, Any], market_params: Dict[str, float]
    ) -> Decimal:
        """计算组合VaR - 使用Monte Carlo方法"""
        try:
            if not positions_data:
                return Decimal("0")

            # 导入Monte Carlo引擎
            from ..analysis.monte_carlo_engine import (
                MarketParameters,
                MonteCarloEngine,
                OptionContract,
                SimulationConfig,
                StrategyLeg,
            )

            # 初始化Monte Carlo引擎
            mc_engine = MonteCarloEngine()
            await mc_engine.initialize()

            # 构建市场参数
            market_parameters = MarketParameters(
                spot_price=market_params.get("current_price", 50000.0),
                risk_free_rate=0.05,
                volatility=market_params.get("volatility", 0.5),
                dividend_yield=0.0,
            )

            # 模拟配置
            sim_config = SimulationConfig(
                num_simulations=10000,  # 高精度VaR计算
                num_steps=50,
                antithetic_variates=True,
                parallel_execution=True,
                num_threads=2,
            )

            # 构建组合策略腿
            strategy_legs = []

            for symbol, position_data in positions_data.items():
                if isinstance(position_data, dict) and self._is_option_symbol(symbol):
                    size = position_data.get("size", 0)
                    if abs(size) < 0.001:  # 忽略极小仓位
                        continue

                    # 解析期权信息
                    option_info = self._parse_option_symbol(symbol)
                    if not option_info:
                        continue

                    # 计算到期时间
                    current_time = datetime.now(timezone.utc)
                    time_to_expiry = (
                        option_info["expiry_date"] - current_time
                    ).total_seconds() / (365.25 * 24 * 3600)
                    time_to_expiry = max(0.001, time_to_expiry)  # 避免零时间

                    # 创建期权合约
                    contract = OptionContract(
                        option_type=option_info["option_type"],
                        strike=float(option_info["strike_price"]),
                        time_to_expiry=time_to_expiry,
                        option_side="long" if size > 0 else "short",
                        quantity=abs(size),
                    )

                    # 创建策略腿
                    leg = StrategyLeg(
                        contract=contract, weight=1.0 if size > 0 else -1.0
                    )

                    strategy_legs.append(leg)

            if not strategy_legs:
                return Decimal("0")

            # 执行Monte Carlo组合定价
            mc_result = await mc_engine.price_strategy(
                strategy_legs, market_parameters, sim_config
            )

            # 返回95% VaR（取绝对值）
            portfolio_var_95 = abs(mc_result.var_95)

            if self.logger:
                await self.logger.debug(
                    f"Portfolio VaR calculation: VaR95={portfolio_var_95:.4f}, VaR99={abs(mc_result.var_99):.4f}"
                )

            return Decimal(str(portfolio_var_95))

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Portfolio VaR calculation failed: {e}")

            # 降级到简化VaR计算
            return await self._calculate_simplified_var(positions_data, market_params)

    async def _calculate_simplified_var(
        self, positions_data: Dict[str, Any], market_params: Dict[str, float]
    ) -> Decimal:
        """简化VaR计算 - 当Monte Carlo失败时的备选方案"""
        try:
            total_exposure = Decimal("0")
            total_delta_exposure = Decimal("0")

            current_price = Decimal(str(market_params.get("current_price", 50000.0)))
            volatility = market_params.get("volatility", 0.5)

            for symbol, position_data in positions_data.items():
                if isinstance(position_data, dict):
                    size = Decimal(str(position_data.get("size", 0)))
                    market_value = Decimal(str(position_data.get("market_value", 0)))

                    # 累计总敞口
                    total_exposure += abs(market_value)

                    # 获取Delta数据
                    if self.cache_manager:
                        greeks_data = await self.cache_manager.get(f"greeks:{symbol}")
                        if greeks_data:
                            delta = Decimal(str(greeks_data.get("delta", 0)))
                            delta_exposure = size * delta * current_price
                            total_delta_exposure += abs(delta_exposure)

            if total_exposure == 0:
                return Decimal("0")

            # 简化VaR：使用组合Delta敞口和波动率
            # VaR = 1.65 * volatility * sqrt(time) * exposure (95%置信度)
            time_horizon = 1 / 365  # 1天时间范围
            var_multiplier = Decimal("1.65")  # 95%置信度

            # 使用Delta调整后的敞口
            effective_exposure = max(
                total_delta_exposure, total_exposure * Decimal("0.1")
            )  # 至少10%敞口

            simplified_var = (
                var_multiplier
                * Decimal(str(volatility))
                * Decimal(str(math.sqrt(time_horizon)))
                * effective_exposure
            )

            return simplified_var

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Simplified VaR calculation failed: {e}")
            return Decimal("0")

    def _is_option_symbol(self, symbol: str) -> bool:
        """判断是否为期权符号"""
        return "-C" in symbol or "-P" in symbol

    def _parse_option_symbol(self, symbol: str) -> Optional[Dict[str, Any]]:
        """解析期权符号"""
        try:
            # 示例: BTC-07JAN25-45000-C
            parts = symbol.split("-")
            if len(parts) != 4:
                return None

            underlying = parts[0]
            expiry_str = parts[1]
            strike_str = parts[2]
            option_type = "call" if parts[3] == "C" else "put"

            # 解析到期日
            expiry_date = datetime.strptime(
                f"20{expiry_str[5:]}-{expiry_str[2:5]}-{expiry_str[:2]}", "%Y-%b-%d"
            ).replace(tzinfo=timezone.utc)

            # 解析行权价
            strike_price = Decimal(strike_str)

            return {
                "underlying": underlying,
                "expiry_date": expiry_date,
                "strike_price": strike_price,
                "option_type": option_type,
            }

        except Exception:
            return None
