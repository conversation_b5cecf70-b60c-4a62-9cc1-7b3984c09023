"""
保证金计算器

负责精确计算期权交易的保证金要求，支持组合保证金优惠和交易所规则适配
"""

from dataclasses import dataclass, field
from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional

from ..core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from ..core.event_bus import EventBus
from ..data.cache_manager import CacheManager


class MarginType(Enum):
    """保证金类型"""

    INITIAL = "initial"  # 初始保证金
    MAINTENANCE = "maintenance"  # 维持保证金
    PORTFOLIO = "portfolio"  # 组合保证金


class OptionType(Enum):
    """期权类型"""

    CALL = "call"
    PUT = "put"


@dataclass
class OptionContract:
    """期权合约"""

    symbol: str
    option_type: OptionType
    strike: Decimal
    expiry: datetime
    size: Decimal  # 正数为多头，负数为空头
    mark_price: Decimal
    underlying_price: Decimal
    implied_vol: Decimal
    delta: Decimal = Decimal("0")
    gamma: Decimal = Decimal("0")
    theta: Decimal = Decimal("0")
    vega: Decimal = Decimal("0")


@dataclass
class MarginRequirement:
    """保证金要求"""

    contract_symbol: str
    initial_margin: Decimal
    maintenance_margin: Decimal
    portfolio_margin: Decimal
    margin_type: MarginType
    calculation_method: str
    risk_factor: Decimal = Decimal("1.0")
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


@dataclass
class PortfolioMargin:
    """组合保证金"""

    total_initial_margin: Decimal
    total_maintenance_margin: Decimal
    total_portfolio_margin: Decimal
    margin_utilization: Decimal
    available_margin: Decimal
    margin_call_level: Decimal
    liquidation_level: Decimal
    diversification_benefit: Decimal = Decimal("0")
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))


class MarginCalculator(BaseComponent):
    """
    保证金计算器

    功能特性：
    - 期权组合保证金精确计算
    - 支持Deribit保证金规则
    - 组合保证金优惠计算
    - 保证金需求预测
    - 实时保证金监控
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__("MarginCalculator", config)

        # 组件依赖
        self.event_bus: Optional[EventBus] = None
        self.cache_manager: Optional[CacheManager] = None

        # 保证金参数配置
        self._load_margin_config()

        # 保证金计算缓存
        self._margin_cache: Dict[str, MarginRequirement] = {}
        self._portfolio_margin_cache: Optional[PortfolioMargin] = None
        self._cache_ttl = 300  # 5分钟缓存

        # 风险参数
        self.risk_free_rate = Decimal("0.05")  # 无风险利率5%
        self.volatility_floor = Decimal("0.1")  # 最小波动率10%
        self.volatility_cap = Decimal("3.0")  # 最大波动率300%

    def _load_margin_config(self):
        """加载保证金配置"""
        margin_config = self.config.get("margin", {}) if self.config else {}

        # Deribit保证金参数
        self.deribit_params = {
            "option_margin_rate": Decimal(
                str(margin_config.get("option_margin_rate", "0.15"))
            ),  # 15%
            "underlying_margin_rate": Decimal(
                str(margin_config.get("underlying_margin_rate", "0.1"))
            ),  # 10%
            "min_margin_rate": Decimal(
                str(margin_config.get("min_margin_rate", "0.05"))
            ),  # 5%
            "portfolio_margin_discount": Decimal(
                str(margin_config.get("portfolio_margin_discount", "0.8"))
            ),  # 20%折扣
            "stress_test_scenarios": margin_config.get(
                "stress_test_scenarios", 16
            ),  # 16个压力测试场景
        }

        # 风险参数
        self.risk_params = {
            "price_shock_up": Decimal(
                str(margin_config.get("price_shock_up", "0.15"))
            ),  # 上涨15%
            "price_shock_down": Decimal(
                str(margin_config.get("price_shock_down", "0.15"))
            ),  # 下跌15%
            "vol_shock_up": Decimal(
                str(margin_config.get("vol_shock_up", "0.25"))
            ),  # 波动率上升25%
            "vol_shock_down": Decimal(
                str(margin_config.get("vol_shock_down", "0.25"))
            ),  # 波动率下降25%
            "time_decay": Decimal(
                str(margin_config.get("time_decay", "0.1"))
            ),  # 时间衰减10%
        }

    async def _initialize_impl(self) -> bool:
        """初始化保证金计算器"""
        try:
            if self.logger:
                await self.logger.info("MarginCalculator initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"MarginCalculator initialization failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动保证金计算器"""
        try:
            if self.logger:
                await self.logger.info("MarginCalculator started")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"MarginCalculator start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止保证金计算器"""
        try:
            # 清理缓存
            self._margin_cache.clear()
            self._portfolio_margin_cache = None

            if self.logger:
                await self.logger.info("MarginCalculator stopped")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"MarginCalculator stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查"""
        try:
            # 测试保证金计算
            test_contract = OptionContract(
                symbol="BTC-TEST",
                option_type=OptionType.CALL,
                strike=Decimal("50000"),
                expiry=datetime.now(timezone.utc),
                size=Decimal("1"),
                mark_price=Decimal("1000"),
                underlying_price=Decimal("50000"),
                implied_vol=Decimal("0.8"),
            )

            margin_req = await self.calculate_option_margin(test_contract)
            if margin_req and margin_req.initial_margin > 0:
                return HealthCheckResult(
                    status=HealthStatus.HEALTHY,
                    message="Margin calculation operational",
                )
            else:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message="Margin calculation test failed",
                )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    async def calculate_option_margin(
        self, contract: OptionContract
    ) -> Optional[MarginRequirement]:
        """计算单个期权合约的保证金"""
        try:
            # 检查缓存
            cache_key = f"{contract.symbol}_{contract.size}_{contract.mark_price}"
            if cache_key in self._margin_cache:
                cached_margin = self._margin_cache[cache_key]
                if (
                    datetime.now(timezone.utc) - cached_margin.timestamp
                ).total_seconds() < self._cache_ttl:
                    return cached_margin

            # 计算初始保证金
            initial_margin = await self._calculate_initial_margin(contract)

            # 计算维持保证金（通常是初始保证金的80%）
            maintenance_margin = initial_margin * Decimal("0.8")

            # 计算组合保证金（使用压力测试）
            portfolio_margin = await self._calculate_portfolio_margin_single(contract)

            # 创建保证金要求
            margin_req = MarginRequirement(
                contract_symbol=contract.symbol,
                initial_margin=initial_margin,
                maintenance_margin=maintenance_margin,
                portfolio_margin=portfolio_margin,
                margin_type=MarginType.INITIAL,
                calculation_method="deribit_standard",
            )

            # 缓存结果
            self._margin_cache[cache_key] = margin_req

            return margin_req

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Option margin calculation failed: {e}")
            return None

    async def _calculate_initial_margin(self, contract: OptionContract) -> Decimal:
        """计算初始保证金"""
        try:
            # Deribit期权保证金公式:
            # Margin = max(
            #   Option_Price + max(0, Underlying_Price * Margin_Rate - OTM_Amount),
            #   Option_Price + Underlying_Price * Min_Margin_Rate
            # )

            option_price = contract.mark_price * abs(contract.size)
            underlying_price = contract.underlying_price

            # 计算价外金额
            if contract.option_type == OptionType.CALL:
                otm_amount = max(Decimal("0"), contract.strike - underlying_price)
            else:  # PUT
                otm_amount = max(Decimal("0"), underlying_price - contract.strike)

            # 标准保证金计算
            standard_margin = option_price + max(
                Decimal("0"),
                underlying_price * self.deribit_params["option_margin_rate"]
                - otm_amount,
            )

            # 最小保证金计算
            min_margin = (
                option_price + underlying_price * self.deribit_params["min_margin_rate"]
            )

            # 取较大值
            initial_margin = max(standard_margin, min_margin)

            # 对于空头期权，保证金更高
            if contract.size < 0:
                initial_margin = max(initial_margin, option_price * Decimal("2"))

            return initial_margin

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Initial margin calculation failed: {e}")
            return Decimal("0")

    async def _calculate_portfolio_margin_single(
        self, contract: OptionContract
    ) -> Decimal:
        """计算单个合约的组合保证金"""
        try:
            # 使用简化的压力测试方法
            max_loss = Decimal("0")

            # 价格上涨场景
            price_up = contract.underlying_price * (
                Decimal("1") + self.risk_params["price_shock_up"]
            )
            loss_up = await self._calculate_scenario_loss(
                contract, price_up, contract.implied_vol
            )
            max_loss = max(max_loss, loss_up)

            # 价格下跌场景
            price_down = contract.underlying_price * (
                Decimal("1") - self.risk_params["price_shock_down"]
            )
            loss_down = await self._calculate_scenario_loss(
                contract, price_down, contract.implied_vol
            )
            max_loss = max(max_loss, loss_down)

            # 波动率上升场景
            vol_up = contract.implied_vol * (
                Decimal("1") + self.risk_params["vol_shock_up"]
            )
            loss_vol_up = await self._calculate_scenario_loss(
                contract, contract.underlying_price, vol_up
            )
            max_loss = max(max_loss, loss_vol_up)

            # 波动率下降场景
            vol_down = contract.implied_vol * (
                Decimal("1") - self.risk_params["vol_shock_down"]
            )
            loss_vol_down = await self._calculate_scenario_loss(
                contract, contract.underlying_price, vol_down
            )
            max_loss = max(max_loss, loss_vol_down)

            return max_loss

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Portfolio margin calculation failed: {e}")
            return Decimal("0")

    async def _calculate_scenario_loss(
        self, contract: OptionContract, scenario_price: Decimal, scenario_vol: Decimal
    ) -> Decimal:
        """计算压力测试场景下的损失"""
        try:
            # 简化的Black-Scholes计算
            # 这里使用简化公式，实际应该使用完整的BS模型

            # 计算内在价值
            if contract.option_type == OptionType.CALL:
                intrinsic_value = max(Decimal("0"), scenario_price - contract.strike)
            else:  # PUT
                intrinsic_value = max(Decimal("0"), contract.strike - scenario_price)

            # 估算时间价值（简化）
            time_value = contract.mark_price - max(
                Decimal("0"),
                contract.underlying_price - contract.strike
                if contract.option_type == OptionType.CALL
                else contract.strike - contract.underlying_price,
            )

            # 波动率影响（简化）
            vol_impact = (
                time_value
                * (scenario_vol - contract.implied_vol)
                / contract.implied_vol
            )

            # 新的期权价格估算
            new_option_price = intrinsic_value + max(
                Decimal("0"), time_value + vol_impact
            )

            # 计算损失（对于空头仓位）
            if contract.size < 0:
                loss = (new_option_price - contract.mark_price) * abs(contract.size)
            else:
                loss = (contract.mark_price - new_option_price) * contract.size

            return max(Decimal("0"), loss)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Scenario loss calculation failed: {e}")
            return Decimal("0")

    async def calculate_portfolio_margin(
        self, contracts: List[OptionContract]
    ) -> Optional[PortfolioMargin]:
        """计算投资组合保证金"""
        try:
            if not contracts:
                return None

            # 计算单个合约保证金
            total_initial = Decimal("0")
            total_maintenance = Decimal("0")
            individual_margins = []

            for contract in contracts:
                margin_req = await self.calculate_option_margin(contract)
                if margin_req:
                    total_initial += margin_req.initial_margin
                    total_maintenance += margin_req.maintenance_margin
                    individual_margins.append(margin_req)

            # 计算组合保证金（压力测试）
            portfolio_margin = await self._calculate_portfolio_stress_test(contracts)

            # 计算多样化收益
            diversification_benefit = max(
                Decimal("0"), total_initial - portfolio_margin
            )

            # 获取账户信息
            account_info = await self._get_account_margin_info()
            available_margin = account_info.get("available_margin", Decimal("0"))

            # 计算保证金使用率
            total_equity = account_info.get("total_equity", Decimal("1"))
            margin_utilization = (
                portfolio_margin / total_equity if total_equity > 0 else Decimal("0")
            )

            # 计算保证金追缴和强平水平
            margin_call_level = portfolio_margin * Decimal("1.2")  # 120%
            liquidation_level = portfolio_margin * Decimal("1.1")  # 110%

            portfolio_margin_result = PortfolioMargin(
                total_initial_margin=total_initial,
                total_maintenance_margin=total_maintenance,
                total_portfolio_margin=portfolio_margin,
                margin_utilization=margin_utilization,
                available_margin=available_margin,
                margin_call_level=margin_call_level,
                liquidation_level=liquidation_level,
                diversification_benefit=diversification_benefit,
            )

            # 缓存结果
            self._portfolio_margin_cache = portfolio_margin_result

            return portfolio_margin_result

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Portfolio margin calculation failed: {e}")
            return None

    async def _calculate_portfolio_stress_test(
        self, contracts: List[OptionContract]
    ) -> Decimal:
        """计算投资组合压力测试保证金"""
        try:
            max_portfolio_loss = Decimal("0")

            # 定义压力测试场景
            scenarios = [
                # 价格冲击场景
                {"price_factor": Decimal("1.15"), "vol_factor": Decimal("1.0")},  # +15%
                {"price_factor": Decimal("0.85"), "vol_factor": Decimal("1.0")},  # -15%
                {
                    "price_factor": Decimal("1.10"),
                    "vol_factor": Decimal("1.25"),
                },  # +10%, +25% vol
                {
                    "price_factor": Decimal("0.90"),
                    "vol_factor": Decimal("1.25"),
                },  # -10%, +25% vol
                {
                    "price_factor": Decimal("1.05"),
                    "vol_factor": Decimal("0.75"),
                },  # +5%, -25% vol
                {
                    "price_factor": Decimal("0.95"),
                    "vol_factor": Decimal("0.75"),
                },  # -5%, -25% vol
                # 极端场景
                {
                    "price_factor": Decimal("1.25"),
                    "vol_factor": Decimal("1.5"),
                },  # +25%, +50% vol
                {
                    "price_factor": Decimal("0.75"),
                    "vol_factor": Decimal("1.5"),
                },  # -25%, +50% vol
            ]

            for scenario in scenarios:
                portfolio_loss = Decimal("0")

                for contract in contracts:
                    scenario_price = (
                        contract.underlying_price * scenario["price_factor"]
                    )
                    scenario_vol = contract.implied_vol * scenario["vol_factor"]

                    contract_loss = await self._calculate_scenario_loss(
                        contract, scenario_price, scenario_vol
                    )
                    portfolio_loss += contract_loss

                max_portfolio_loss = max(max_portfolio_loss, portfolio_loss)

            # 应用组合保证金折扣
            portfolio_margin = (
                max_portfolio_loss * self.deribit_params["portfolio_margin_discount"]
            )

            return portfolio_margin

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Portfolio stress test failed: {e}")
            return Decimal("0")

    async def _get_account_margin_info(self) -> Dict[str, Decimal]:
        """获取账户保证金信息"""
        try:
            if self.cache_manager:
                margin_info = await self.cache_manager.get("margin_info")
                if margin_info:
                    return {
                        "total_equity": Decimal(
                            str(margin_info.get("total_equity", 0))
                        ),
                        "available_margin": Decimal(
                            str(margin_info.get("available_margin", 0))
                        ),
                        "used_margin": Decimal(str(margin_info.get("used_margin", 0))),
                        "maintenance_margin": Decimal(
                            str(margin_info.get("maintenance_margin", 0))
                        ),
                    }

            # 默认值
            return {
                "total_equity": Decimal("100000"),
                "available_margin": Decimal("50000"),
                "used_margin": Decimal("0"),
                "maintenance_margin": Decimal("0"),
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get account margin info: {e}")
            return {}

    async def get_margin_summary(self) -> Dict[str, Any]:
        """获取保证金摘要"""
        try:
            current_positions = await self._get_current_positions()
            portfolio_margin = await self.calculate_portfolio_margin(current_positions)

            if portfolio_margin:
                return {
                    "total_initial_margin": float(
                        portfolio_margin.total_initial_margin
                    ),
                    "total_maintenance_margin": float(
                        portfolio_margin.total_maintenance_margin
                    ),
                    "total_portfolio_margin": float(
                        portfolio_margin.total_portfolio_margin
                    ),
                    "margin_utilization": float(portfolio_margin.margin_utilization),
                    "available_margin": float(portfolio_margin.available_margin),
                    "diversification_benefit": float(
                        portfolio_margin.diversification_benefit
                    ),
                    "margin_call_level": float(portfolio_margin.margin_call_level),
                    "liquidation_level": float(portfolio_margin.liquidation_level),
                    "timestamp": portfolio_margin.timestamp.isoformat(),
                }

            return {"status": "no_positions"}

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Margin summary generation failed: {e}")
            return {"status": "error", "message": str(e)}

    async def _get_current_positions(self) -> List[OptionContract]:
        """获取当前期权仓位"""
        try:
            if not self.cache_manager:
                return []

            positions_data = await self.cache_manager.get("positions")
            if not positions_data:
                return []

            contracts = []
            for symbol, position_data in positions_data.items():
                if isinstance(position_data, dict) and "option" in symbol.lower():
                    # 解析期权合约信息
                    contract = await self._parse_position_to_contract(
                        symbol, position_data
                    )
                    if contract:
                        contracts.append(contract)

            return contracts

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get current positions: {e}")
            return []

    def set_event_bus(self, event_bus: EventBus):
        """设置事件总线"""
        self.event_bus = event_bus

    def set_cache_manager(self, cache_manager: CacheManager):
        """设置缓存管理器"""
        self.cache_manager = cache_manager
