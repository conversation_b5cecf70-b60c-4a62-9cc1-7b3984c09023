"""
测试示例数据

提供各种组件测试所需的示例数据
"""

from datetime import datetime, timezone
from typing import Any, Dict, List


class SampleData:
    """测试示例数据类"""

    @staticmethod
    def get_option_chain_data() -> Dict[str, Any]:
        """获取期权链示例数据"""
        return {
            "50000": {
                "greeks": {
                    "gamma": 0.00001,
                    "delta": 0.5,
                    "theta": -10.5,
                    "vega": 25.3,
                    "rho": 15.2,
                },
                "open_interest": 1000,
                "contract_size": 1.0,
                "volume": 150,
                "bid": 2500,
                "ask": 2520,
                "last_price": 2510,
            },
            "52000": {
                "greeks": {
                    "gamma": 0.00002,
                    "delta": 0.3,
                    "theta": -8.2,
                    "vega": 20.1,
                    "rho": 12.8,
                },
                "open_interest": 800,
                "contract_size": 1.0,
                "volume": 120,
                "bid": 1800,
                "ask": 1815,
                "last_price": 1808,
            },
            "48000": {
                "greeks": {
                    "gamma": 0.000015,
                    "delta": -0.3,
                    "theta": -6.8,
                    "vega": 18.5,
                    "rho": -10.2,
                },
                "open_interest": 600,
                "contract_size": 1.0,
                "volume": 90,
                "bid": 1200,
                "ask": 1210,
                "last_price": 1205,
            },
        }

    @staticmethod
    def get_market_data() -> List[Dict[str, Any]]:
        """获取市场数据示例"""
        return [
            {
                "timestamp": "2024-01-01T00:00:00Z",
                "symbol": "BTCUSDT",
                "price": 50000,
                "volume": 100,
                "bid": 49995,
                "ask": 50005,
            },
            {
                "timestamp": "2024-01-01T00:01:00Z",
                "symbol": "BTCUSDT",
                "price": 50100,
                "volume": 150,
                "bid": 50095,
                "ask": 50105,
            },
            {
                "timestamp": "2024-01-01T00:02:00Z",
                "symbol": "BTCUSDT",
                "price": 49900,
                "volume": 120,
                "bid": 49895,
                "ask": 49905,
            },
        ]

    @staticmethod
    def get_config_data() -> Dict[str, Any]:
        """获取配置数据示例"""
        return {
            "system": {"environment": "test", "log_level": "INFO", "debug_mode": True},
            "redis": {"host": "localhost", "port": 6379, "db": 1, "password": None},
            "celery": {
                "broker_url": "redis://localhost:6379/1",
                "result_backend": "redis://localhost:6379/1",
                "worker_concurrency": 4,
            },
            "exchanges": {
                "binance": {
                    "api_key": "test_api_key",
                    "api_secret": "test_api_secret",
                    "testnet": True,
                },
                "deribit": {
                    "api_key": "test_api_key",
                    "api_secret": "test_api_secret",
                    "testnet": True,
                },
            },
        }

    @staticmethod
    def get_event_data() -> Dict[str, Any]:
        """获取事件数据示例"""
        return {
            "event_type": "market_data_update",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": {"symbol": "BTCUSDT", "price": 50000, "volume": 100},
            "source": "binance",
        }
