"""
策略回测验证系统

提供历史数据回放、策略验证、风险指标验证和参数敏感性分析功能
"""

import asyncio
import logging
from dataclasses import dataclass, field
from datetime import datetime, timezone
from decimal import Decimal
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

# 导入系统组件
from ...src.strategies.grid_strategy import GridStrategy


@dataclass
class BacktestConfig:
    """回测配置"""

    start_date: datetime
    end_date: datetime
    initial_capital: Decimal = Decimal("100000")
    commission_rate: Decimal = Decimal("0.001")
    slippage_rate: Decimal = Decimal("0.0005")

    # 策略参数
    strategy_params: Dict[str, Any] = field(default_factory=dict)

    # 风险参数
    max_position_size: Decimal = Decimal("10000")
    max_daily_loss: Decimal = Decimal("5000")

    # 数据源配置
    data_source: str = "historical"
    data_frequency: str = "1m"  # 1m, 5m, 1h, 1d

    # 输出配置
    save_trades: bool = True
    save_positions: bool = True
    generate_report: bool = True


@dataclass
class BacktestMetrics:
    """回测指标"""

    # 收益指标
    total_return: float = 0.0
    annualized_return: float = 0.0
    cumulative_return: float = 0.0

    # 风险指标
    volatility: float = 0.0
    max_drawdown: float = 0.0
    max_drawdown_duration: int = 0
    var_95: float = 0.0
    var_99: float = 0.0

    # 风险调整收益
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0

    # 交易统计
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0

    # 其他指标
    beta: float = 0.0
    alpha: float = 0.0
    information_ratio: float = 0.0


@dataclass
class Trade:
    """交易记录"""

    timestamp: datetime
    symbol: str
    side: str  # buy/sell
    quantity: Decimal
    price: Decimal
    commission: Decimal
    pnl: Decimal = Decimal("0")
    strategy_id: str = ""
    trade_id: str = ""


@dataclass
class Position:
    """仓位记录"""

    timestamp: datetime
    symbol: str
    quantity: Decimal
    avg_price: Decimal
    market_value: Decimal
    unrealized_pnl: Decimal
    strategy_id: str = ""


class HistoricalDataLoader:
    """历史数据加载器"""

    def __init__(self, data_path: str = "data/historical"):
        self.data_path = Path(data_path)
        self.logger = logging.getLogger(__name__)

    async def load_market_data(
        self,
        symbols: List[str],
        start_date: datetime,
        end_date: datetime,
        frequency: str = "1m",
    ) -> Dict[str, pd.DataFrame]:
        """加载市场数据"""
        try:
            market_data = {}

            for symbol in symbols:
                # 模拟加载历史数据
                data = self._generate_mock_data(symbol, start_date, end_date, frequency)
                market_data[symbol] = data

            self.logger.info(f"Loaded market data for {len(symbols)} symbols")
            return market_data

        except Exception as e:
            self.logger.error(f"Failed to load market data: {e}")
            return {}

    def _generate_mock_data(
        self, symbol: str, start_date: datetime, end_date: datetime, frequency: str
    ) -> pd.DataFrame:
        """生成模拟数据"""
        # 计算时间间隔
        freq_map = {"1m": 1, "5m": 5, "1h": 60, "1d": 1440}
        interval_minutes = freq_map.get(frequency, 1)

        # 生成时间序列
        timestamps = pd.date_range(start_date, end_date, freq=f"{interval_minutes}min")

        # 基础价格
        if "BTC" in symbol:
            base_price = 45000
        elif "ETH" in symbol:
            base_price = 3000
        else:
            base_price = 2500  # 期权

        # 生成价格数据（随机游走）
        np.random.seed(42)  # 确保可重复性
        returns = np.random.normal(0, 0.02, len(timestamps))  # 2%波动率
        prices = [base_price]

        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(max(new_price, base_price * 0.5))  # 防止价格过低

        # 创建OHLCV数据
        data = pd.DataFrame(
            {
                "timestamp": timestamps,
                "open": prices,
                "high": [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
                "low": [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
                "close": prices,
                "volume": np.random.uniform(100, 1000, len(timestamps)),
            }
        )

        # 为期权添加Greeks数据
        if "-C" in symbol or "-P" in symbol:
            data["delta"] = np.random.uniform(0.3, 0.7, len(timestamps))
            data["gamma"] = np.random.uniform(0.00001, 0.0001, len(timestamps))
            data["theta"] = np.random.uniform(-20, -5, len(timestamps))
            data["vega"] = np.random.uniform(10, 50, len(timestamps))
            data["iv"] = np.random.uniform(0.6, 1.2, len(timestamps))

        return data


class MockBroker:
    """模拟经纪商"""

    def __init__(self, config: BacktestConfig):
        self.config = config
        self.cash = config.initial_capital
        self.positions: Dict[str, Decimal] = {}
        self.trades: List[Trade] = []
        self.current_prices: Dict[str, Decimal] = {}

    async def submit_order(
        self, symbol: str, side: str, quantity: Decimal, price: Optional[Decimal] = None
    ) -> Trade:
        """提交订单"""
        # 使用市价或指定价格
        execution_price = price or self.current_prices.get(symbol, Decimal("0"))

        # 计算滑点
        slippage = execution_price * self.config.slippage_rate
        if side == "buy":
            execution_price += slippage
        else:
            execution_price -= slippage

        # 计算手续费
        commission = execution_price * quantity * self.config.commission_rate

        # 检查资金充足性
        if side == "buy":
            required_cash = execution_price * quantity + commission
            if required_cash > self.cash:
                raise ValueError("Insufficient cash")
            self.cash -= required_cash
        else:
            self.cash += execution_price * quantity - commission

        # 更新仓位
        if symbol not in self.positions:
            self.positions[symbol] = Decimal("0")

        if side == "buy":
            self.positions[symbol] += quantity
        else:
            self.positions[symbol] -= quantity

        # 创建交易记录
        trade = Trade(
            timestamp=datetime.now(timezone.utc),
            symbol=symbol,
            side=side,
            quantity=quantity,
            price=execution_price,
            commission=commission,
            trade_id=f"trade_{len(self.trades) + 1}",
        )

        self.trades.append(trade)
        return trade

    def update_prices(self, prices: Dict[str, Decimal]):
        """更新价格"""
        self.current_prices.update(prices)

    def get_portfolio_value(self) -> Decimal:
        """获取组合价值"""
        portfolio_value = self.cash

        for symbol, quantity in self.positions.items():
            if quantity != 0:
                price = self.current_prices.get(symbol, Decimal("0"))
                portfolio_value += quantity * price

        return portfolio_value


class PerformanceAnalyzer:
    """性能分析器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def calculate_metrics(
        self,
        portfolio_values: List[Decimal],
        trades: List[Trade],
        benchmark_returns: Optional[List[float]] = None,
    ) -> BacktestMetrics:
        """计算性能指标"""
        try:
            if len(portfolio_values) < 2:
                return BacktestMetrics()

            # 转换为numpy数组
            values = np.array([float(v) for v in portfolio_values])
            returns = np.diff(values) / values[:-1]

            # 基础收益指标
            total_return = (values[-1] - values[0]) / values[0]
            cumulative_return = values[-1] / values[0] - 1

            # 年化收益率（假设252个交易日）
            trading_days = len(values)
            annualized_return = (1 + total_return) ** (252 / trading_days) - 1

            # 波动率
            volatility = np.std(returns) * np.sqrt(252)

            # 最大回撤
            peak = np.maximum.accumulate(values)
            drawdown = (values - peak) / peak
            max_drawdown = np.min(drawdown)

            # 最大回撤持续期
            max_dd_duration = self._calculate_max_drawdown_duration(drawdown)

            # VaR计算
            var_95 = np.percentile(returns, 5)
            var_99 = np.percentile(returns, 1)

            # 风险调整收益
            risk_free_rate = 0.02  # 假设2%无风险利率
            excess_returns = returns - risk_free_rate / 252

            sharpe_ratio = (
                np.mean(excess_returns) / np.std(returns) * np.sqrt(252)
                if np.std(returns) > 0
                else 0
            )

            # Sortino比率
            downside_returns = returns[returns < 0]
            downside_std = np.std(downside_returns) if len(downside_returns) > 0 else 0
            sortino_ratio = (
                np.mean(excess_returns) / downside_std * np.sqrt(252)
                if downside_std > 0
                else 0
            )

            # Calmar比率
            calmar_ratio = (
                annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
            )

            # 交易统计
            trade_metrics = self._calculate_trade_metrics(trades)

            # Beta和Alpha（如果有基准）
            beta, alpha = 0.0, 0.0
            if benchmark_returns and len(benchmark_returns) == len(returns):
                beta, alpha = self._calculate_beta_alpha(
                    returns, benchmark_returns, risk_free_rate
                )

            return BacktestMetrics(
                total_return=total_return,
                annualized_return=annualized_return,
                cumulative_return=cumulative_return,
                volatility=volatility,
                max_drawdown=max_drawdown,
                max_drawdown_duration=max_dd_duration,
                var_95=var_95,
                var_99=var_99,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                calmar_ratio=calmar_ratio,
                beta=beta,
                alpha=alpha,
                **trade_metrics,
            )

        except Exception as e:
            self.logger.error(f"Performance calculation failed: {e}")
            return BacktestMetrics()

    def _calculate_max_drawdown_duration(self, drawdown: np.ndarray) -> int:
        """计算最大回撤持续期"""
        in_drawdown = drawdown < 0
        durations = []
        current_duration = 0

        for is_dd in in_drawdown:
            if is_dd:
                current_duration += 1
            else:
                if current_duration > 0:
                    durations.append(current_duration)
                current_duration = 0

        if current_duration > 0:
            durations.append(current_duration)

        return max(durations) if durations else 0

    def _calculate_trade_metrics(self, trades: List[Trade]) -> Dict[str, Any]:
        """计算交易指标"""
        if not trades:
            return {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate": 0.0,
                "profit_factor": 0.0,
                "avg_win": 0.0,
                "avg_loss": 0.0,
            }

        # 计算每笔交易的PnL（简化计算）
        winning_trades = 0
        losing_trades = 0
        total_profit = 0.0
        total_loss = 0.0

        for trade in trades:
            pnl = float(trade.pnl)
            if pnl > 0:
                winning_trades += 1
                total_profit += pnl
            elif pnl < 0:
                losing_trades += 1
                total_loss += abs(pnl)

        total_trades = len(trades)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        profit_factor = total_profit / total_loss if total_loss > 0 else 0
        avg_win = total_profit / winning_trades if winning_trades > 0 else 0
        avg_loss = total_loss / losing_trades if losing_trades > 0 else 0

        return {
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "losing_trades": losing_trades,
            "win_rate": win_rate,
            "profit_factor": profit_factor,
            "avg_win": avg_win,
            "avg_loss": avg_loss,
        }

    def _calculate_beta_alpha(
        self, returns: np.ndarray, benchmark_returns: List[float], risk_free_rate: float
    ) -> Tuple[float, float]:
        """计算Beta和Alpha"""
        try:
            benchmark_array = np.array(benchmark_returns)

            # 计算超额收益
            excess_returns = returns - risk_free_rate / 252
            excess_benchmark = benchmark_array - risk_free_rate / 252

            # 计算Beta
            covariance = np.cov(excess_returns, excess_benchmark)[0, 1]
            benchmark_variance = np.var(excess_benchmark)
            beta = covariance / benchmark_variance if benchmark_variance > 0 else 0

            # 计算Alpha
            alpha = np.mean(excess_returns) - beta * np.mean(excess_benchmark)
            alpha *= 252  # 年化

            return beta, alpha

        except Exception:
            return 0.0, 0.0


class StrategyValidator:
    """策略验证器"""

    def __init__(self, config: BacktestConfig):
        self.config = config
        self.data_loader = HistoricalDataLoader()
        self.performance_analyzer = PerformanceAnalyzer()
        self.logger = logging.getLogger(__name__)

        # 设置日志
        logging.basicConfig(level=logging.INFO)

    async def run_backtest(self, strategy_class, symbols: List[str]) -> Dict[str, Any]:
        """运行回测"""
        try:
            self.logger.info(
                f"Starting backtest from {self.config.start_date} to {self.config.end_date}"
            )

            # 加载历史数据
            market_data = await self.data_loader.load_market_data(
                symbols,
                self.config.start_date,
                self.config.end_date,
                self.config.data_frequency,
            )

            if not market_data:
                raise ValueError("No market data loaded")

            # 初始化模拟经纪商
            broker = MockBroker(self.config)

            # 初始化策略
            strategy = strategy_class(self.config.strategy_params)

            # 运行回测
            portfolio_values, trades, positions = await self._run_simulation(
                strategy, broker, market_data
            )

            # 计算性能指标
            metrics = self.performance_analyzer.calculate_metrics(
                portfolio_values, trades
            )

            # 生成报告
            report = {
                "config": {
                    "start_date": self.config.start_date.isoformat(),
                    "end_date": self.config.end_date.isoformat(),
                    "initial_capital": float(self.config.initial_capital),
                    "strategy_params": self.config.strategy_params,
                },
                "metrics": {
                    "total_return": metrics.total_return,
                    "annualized_return": metrics.annualized_return,
                    "volatility": metrics.volatility,
                    "max_drawdown": metrics.max_drawdown,
                    "sharpe_ratio": metrics.sharpe_ratio,
                    "sortino_ratio": metrics.sortino_ratio,
                    "win_rate": metrics.win_rate,
                    "profit_factor": metrics.profit_factor,
                    "total_trades": metrics.total_trades,
                },
                "portfolio_values": [float(v) for v in portfolio_values],
                "trades": [self._trade_to_dict(trade) for trade in trades]
                if self.config.save_trades
                else [],
                "positions": [self._position_to_dict(pos) for pos in positions]
                if self.config.save_positions
                else [],
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            self.logger.info(
                f"Backtest completed. Total return: {metrics.total_return:.2%}"
            )
            return report

        except Exception as e:
            self.logger.error(f"Backtest failed: {e}")
            raise

    async def _run_simulation(
        self, strategy, broker, market_data: Dict[str, pd.DataFrame]
    ) -> Tuple[List[Decimal], List[Trade], List[Position]]:
        """运行模拟"""
        portfolio_values = []
        all_trades = []
        all_positions = []

        # 获取所有时间戳（使用第一个symbol的时间戳）
        first_symbol = list(market_data.keys())[0]
        timestamps = market_data[first_symbol]["timestamp"].tolist()

        for i, timestamp in enumerate(timestamps):
            # 更新当前价格
            current_prices = {}
            for symbol, data in market_data.items():
                if i < len(data):
                    current_prices[symbol] = Decimal(str(data.iloc[i]["close"]))

            broker.update_prices(current_prices)

            # 策略决策（简化实现）
            # 实际实现中需要调用策略的具体逻辑

            # 记录组合价值
            portfolio_value = broker.get_portfolio_value()
            portfolio_values.append(portfolio_value)

            # 记录仓位
            for symbol, quantity in broker.positions.items():
                if quantity != 0:
                    price = current_prices.get(symbol, Decimal("0"))
                    position = Position(
                        timestamp=timestamp,
                        symbol=symbol,
                        quantity=quantity,
                        avg_price=price,
                        market_value=quantity * price,
                        unrealized_pnl=Decimal("0"),  # 简化计算
                    )
                    all_positions.append(position)

        all_trades = broker.trades
        return portfolio_values, all_trades, all_positions

    def _trade_to_dict(self, trade: Trade) -> Dict[str, Any]:
        """交易转字典"""
        return {
            "timestamp": trade.timestamp.isoformat(),
            "symbol": trade.symbol,
            "side": trade.side,
            "quantity": float(trade.quantity),
            "price": float(trade.price),
            "commission": float(trade.commission),
            "pnl": float(trade.pnl),
            "trade_id": trade.trade_id,
        }

    def _position_to_dict(self, position: Position) -> Dict[str, Any]:
        """仓位转字典"""
        return {
            "timestamp": position.timestamp.isoformat(),
            "symbol": position.symbol,
            "quantity": float(position.quantity),
            "avg_price": float(position.avg_price),
            "market_value": float(position.market_value),
            "unrealized_pnl": float(position.unrealized_pnl),
        }

    async def parameter_sensitivity_analysis(
        self, strategy_class, symbols: List[str], param_ranges: Dict[str, List]
    ) -> Dict[str, Any]:
        """参数敏感性分析"""
        results = {}

        for param_name, param_values in param_ranges.items():
            param_results = []

            for param_value in param_values:
                # 更新策略参数
                test_config = BacktestConfig(
                    start_date=self.config.start_date,
                    end_date=self.config.end_date,
                    strategy_params={
                        **self.config.strategy_params,
                        param_name: param_value,
                    },
                )

                # 创建临时验证器
                temp_validator = StrategyValidator(test_config)

                try:
                    # 运行回测
                    result = await temp_validator.run_backtest(strategy_class, symbols)

                    param_results.append(
                        {
                            "param_value": param_value,
                            "total_return": result["metrics"]["total_return"],
                            "sharpe_ratio": result["metrics"]["sharpe_ratio"],
                            "max_drawdown": result["metrics"]["max_drawdown"],
                        }
                    )

                except Exception as e:
                    self.logger.warning(
                        f"Parameter test failed for {param_name}={param_value}: {e}"
                    )

            results[param_name] = param_results

        return results


# 使用示例
async def run_strategy_validation():
    """运行策略验证"""
    config = BacktestConfig(
        start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
        end_date=datetime(2024, 12, 31, tzinfo=timezone.utc),
        initial_capital=Decimal("100000"),
        strategy_params={
            "grid_levels": 10,
            "grid_spacing": 0.02,
            "position_size": Decimal("1000"),
        },
    )

    validator = StrategyValidator(config)
    symbols = ["BTC-USDT", "BTC-07JAN25-45000-C"]

    # 运行回测
    result = await validator.run_backtest(GridStrategy, symbols)

    print("Backtest Results:")
    print(f"Total Return: {result['metrics']['total_return']:.2%}")
    print(f"Sharpe Ratio: {result['metrics']['sharpe_ratio']:.2f}")
    print(f"Max Drawdown: {result['metrics']['max_drawdown']:.2%}")

    return result


if __name__ == "__main__":
    result = asyncio.run(run_strategy_validation())
