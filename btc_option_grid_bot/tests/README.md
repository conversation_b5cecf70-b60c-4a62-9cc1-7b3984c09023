# 测试规范说明

## 📁 目录结构

```
tests/
├── unit/                    # 单元测试
│   ├── test_celery_manager.py      # CeleryTaskManager测试
│   ├── test_base_component.py      # BaseComponent测试
│   ├── test_event_bus.py           # EventBus测试
│   └── ...                         # 其他组件测试
├── integration/             # 集成测试
│   ├── test_data_flow.py           # 数据流集成测试
│   └── test_system_integration.py  # 系统集成测试
└── fixtures/                # 测试数据和工具
    ├── sample_data.py              # 示例数据
    └── test_helpers.py             # 测试辅助函数
```

## 🧪 测试规范

### 单元测试

- **文件命名**: `test_{component_name}.py`
- **类命名**: `Test{ComponentName}`
- **方法命名**: `test_{specific_functionality}`
- **覆盖率要求**: > 80%

### 测试独立性

- 每个测试方法必须独立运行
- 不依赖其他测试的执行结果
- 使用mock模拟外部依赖

### 资源清理

- 测试完成后自动清理临时文件
- 不在项目中留下测试痕迹
- 使用fixtures提供的辅助函数

## 🚫 禁止行为

- ❌ 在项目根目录创建测试脚本
- ❌ 创建任务完成报告文档
- ❌ 创建演示脚本
- ❌ 在tests目录外创建测试文件

## 🛠️ 使用示例

```python
import pytest
from unittest.mock import Mock, patch
from tests.fixtures.sample_data import SampleData
from tests.fixtures.test_helpers import TestHelpers

class TestMyComponent:
    @pytest.fixture
    async def component(self):
        # 使用辅助函数创建组件
        return MyComponent()
    
    @pytest.mark.asyncio
    async def test_functionality(self, component):
        # 使用示例数据进行测试
        data = SampleData.get_option_chain_data()
        result = await component.process(data)
        assert result is not None
```

## 🏃 运行测试

```bash
# 运行所有测试
pytest tests/

# 运行单元测试
pytest tests/unit/

# 运行集成测试
pytest tests/integration/

# 运行特定测试文件
pytest tests/unit/test_celery_manager.py -v

# 生成覆盖率报告
pytest tests/ --cov=src --cov-report=html
```